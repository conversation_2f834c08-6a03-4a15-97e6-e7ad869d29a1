{"name": "player", "type": "module", "private": true, "version": "2.3.1", "scripts": {"dev": "vite --mode development", "develop": "vite build --mode develop", "release": "vite build --mode release", "preview": "vite preview --mode development", "start": "npm run run-build && npm run serve", "start-sw": "npm run run-build-sw && npm run serve", "start-claims": "npm run run-build-claims && npm run serve", "start-sw-claims": "npm run run-build-sw-claims && npm run serve", "run-build": "cross-env DEBUG=vite-plugin-pwa vite build --mode release", "run-build-report": "cross-env REPORT=true vite build --mode release", "run-build-claims": "cross-env DEBUG=vite-plugin-pwa CLAIMS=true vite build --mode release", "run-build-sw": "rimraf dist && cross-env DEBUG=vite-plugin-pwa SW=true vite build --mode release", "run-build-sw-claims": "cross-env DEBUG=vite-plugin-pwa CLAIMS=true SW=true vite build --mode release", "run-build-sw-claims-destroy": "cross-env DEBUG=vite-plugin-pwa SW_DESTROY=true CLAIMS=true SW=true vite build --mode release", "serve": "serve dist --port=4173", "start-preview": "vite preview --port=4173"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.4.3", "@tanstack/vue-query": "^5.80.6", "@vueuse/core": "12.3.0", "@vueuse/integrations": "12.3.0", "axios": "^0.27.2", "compressorjs": "^1.2.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.5", "dsbridge": "^3.1.4", "echarts": "^5.5.1", "jsonwebtoken-esm": "1.0.5", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pako": "^2.1.0", "pinia": "^2.0.23", "pinia-plugin-persistedstate": "^2.3.0", "qrcode": "^1.5.4", "qs": "^6.11.0", "swiper": "^11", "tcplayer.js": "^5.1.0", "vant": "^4.9.20", "vue": "3.5.4", "vue-clipboard3": "^2.0.0", "vue-router": "^4.1.5", "vuedraggable": "^4.1.0", "web-vitals": "^4.2.4"}, "devDependencies": {"@antfu/eslint-config": "2.26.0", "@cloudcare/browser-rum": "^3.2.23", "@mini-code/base-func": "^1.1.1", "@rollup/plugin-replace": "^5.0.5", "@types/lodash-es": "^4.17.12", "@unocss/eslint-plugin": "^0.62.3", "@unocss/preset-rem-to-px": "^66.1.4", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "4.0.0", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "eslint": "^9.9.0", "eslint-plugin-format": "^0.1.2", "esno": "^4.8.0", "fs-extra": "^11.3.0", "https-localhost": "^4.7.1", "less": "^4.1.3", "picocolors": "^1.1.1", "postcss": "^8.4.39", "postcss-mobile-forever": "^5.0.0", "rollup-plugin-visualizer": "^5.12.0", "terser": "^5.31.1", "typescript": "^5.5.4", "unocss": "^66.0.0", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "unplugin-vue-router": "^0.10.7", "vite": "^5.0.10", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-pwa": "^0.20.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.0.29", "workbox-build": "^7.1.0", "workbox-core": "^7.1.0", "workbox-precaching": "^7.1.0", "workbox-routing": "^7.1.0", "workbox-window": "^7.1.0"}, "browserslist": ["> 1%", "last 2 versions"]}