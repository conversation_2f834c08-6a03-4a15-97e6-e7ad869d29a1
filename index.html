<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
  <link rel="icon" href="/favicon.svg" type="image/svg+xml">
  <link rel="apple-touch-icon" href="/apple-touch-icon.png">
  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-title" content="player">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="#171717" />
  <meta name="theme-color" content="#171717">
  <meta http-equiv="cache-control" content="no-cache,no-store">
  <title><%= title %></title>
  <link href="https://tcsdk.com/player/tcplayer/release/v5.3.2/tcplayer.min.css" rel="stylesheet"/>
  <script src="https://tcsdk.com/player/tcplayer/release/v5.3.2/tcplayer.v5.3.2.min.js"></script>
    
  <style>
    :root {
      --loadingbgcolor: #01030c;
      --loadingSpinnerColor: #9b7b65;
    }
  </style>
  <style>
    #loading-overlay {
        /* position: fixed;
        top: 0;
        left: 0; */
        width: 100%;
        height: 100%;
        /* background: rgba(255, 255, 255, 0.8); */
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        z-index: 9999;
        /* background-position: top top; */
        background-size: 100% 100%;
        background-position: center center;
        background-color: var(--loadingbgcolor);
        position: absolute;
        top: 0;
        left: 0;
      }
      .loading-bottom {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        position: absolute;
        bottom: 40px;
      }
      .loading-text {
        color: var(--loadingSpinnerColor);
        font-size: 14px;
        /* margin-bottom: 12px; */
      }
      .logo {
        width: 70px;
        height: 70px;
        /* margin-bottom: 10px; */
        /* position: absolute;
        bottom: 20px; */
      }
      .loading-spinner {
        border: 2px solid #c9c9c9;
        border-top: 2px solid var(--loadingSpinnerColor);
        border-radius: 50%;
        width: 20px;
        height: 20px;
        animation: spin 0.5s linear infinite;
        margin-bottom: 12px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
  </style>
</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="app"></div>
  <div id="loading-overlay">
    <!-- <img src="/logo/logo.png" class="logo" /> -->
    <div class="loading-bottom">
      <div class="loading-spinner"></div>
      <p class="loading-text">检测线路中，请稍等</p>
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>