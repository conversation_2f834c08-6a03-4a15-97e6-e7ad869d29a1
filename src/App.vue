<template>
  <van-config-provider :theme="currentTheme">
    <router-view v-slot="{ Component, route }">
      <keep-alive :include="keepAliveComponents">
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </van-config-provider>
  

  <!-- 客服弹窗 -->
  <ServicePopup />

  <van-floating-bubble v-model:offset="offset" axis="xy" magnetic="x" :gap="16" v-if="$route.meta.baywindow">
    <baywindow />
  </van-floating-bubble>

  <!-- 登录选项 -->
  <LoginSelector
    v-model:show="hasNeedLogin"
    :closeOnClickOverlay="false"
    @account-login="goToAccountLogin"
    @phone-login="confirmHandle"
    @guest-login="cancelHandle"
  />

  <!-- 绑定手机 -->
  <PromptComponents
    v-model="hasNeedPhone"
    :closeOnClickOverlay="false"
    title="温馨提示"
    content="游客暂不开放，是否去进行手机验证"
    hasCancelBtn
    :confirmCall="confirmPhoneHandle"
    :cancelCall="cancelPhoneHandle"
  />
</template>
<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from '@/store/app';
import { useUserStore } from '@/store/user';
import { useRouteStore } from '@/store/route';
import { PageEnum } from '@/enums/pageEnum';
import fingerPrint from '@fingerprintjs/fingerprintjs';
import baywindow from '@/components/baywindow/baywindow';
import ServicePopup from '@/components/ServicePopup/index.vue';
import PromptComponents from '@/components/prompt/prompt';
import LoginSelector from '@/components/login/LoginSelector.vue';
import { CheckHeartBeat, GetWSToken } from '@/api/user';
import { GeneratorSign } from '@/api/promotion';
import { GetAdListAPI, AppConfigAPI, BaywindowAPI } from '@/api/home';
import { GetTagListApi } from '@/api/video';
import { InitSDK } from '@/sdk';
import { getRefferUrl, param2Obj, getLocalStorage } from '@/utils';
import { useLogin } from '@/hooks';

const appStore = useAppStore();
const userStore = useUserStore();
const routeStore = useRouteStore();
const keepAliveComponents = computed(() => routeStore.keepAliveComponents);
const { height } = useWindowSize();
const offset = ref({ y: height.value / 2 - 50 });

const route = useRoute();
const router = useRouter();
const data = reactive({
  tabIndex: '0',
  isClick: false
});

const updateData = ({ tabIndex, isClick }) => {
  if (tabIndex) data.tabIndex = tabIndex;
  data.isClick = isClick;
};

provide('ProviderContent', {
  data,
  updateData
});

const { hasNeedLogin, hasNeedPhone } = storeToRefs(appStore);
const currentTheme = ref('dark');
const { VITE_HIDDEN_TAB } = import.meta.env;

const verifyTokenHandle = async () => {
  try {
    await CheckHeartBeat({});
  } catch (error) {
    pause();
  }
};

const GeneratorSignHandle = async () => {
  try {
    const res = await GeneratorSign();
    localStorage.setItem('sign', res.data.sign);
  } catch (error) {
    if (error.code === 101) {
      GeneratorSignHandle();
    }
  }
};

const { pause, resume } = useIntervalFn(verifyTokenHandle, 120 * 1000, { immediate: false });

useIntervalFn(GeneratorSignHandle, 3 * 60 * 1000, { immediateCallback: true });

const getAppConfig = async () => {
  try {
    const res = await AppConfigAPI({});
    appStore.SetAppConfigReducer(res.data);
  } catch (error) {}
};

const getBaywindow = async () => {
  try {
    const res = await BaywindowAPI();
    console.log(res, 'app baywindow res');
    appStore.SetBaywindowAction(res.data.list);
  } catch (error) {}
};

const getTagList = async () => {
  try {
    const res = await GetTagListApi();
    console.log(res, 'tag list res');
    appStore.SetTagListReducer(res.data?.tags);
  } catch (error) {}
};

const getAdList = async () => {
  try {
    const res = await GetAdListAPI();
    console.log(res, 'adlist res');
    appStore.SetAppAdListReducer(res.data);
  } catch (error) {}
};

const getWsToken = async () => {
  try {
    const getWsParams = async () => {
      const res = await GetWSToken();
      const Authorization = getLocalStorage(import.meta.env.VITE_TOKEN_KEY);
      const token = res.data.token;
      const connectionUrl = import.meta.env.VITE_GLOB_APP_SOCKET + '?token=' + token + '&Authorization=' + Authorization;
      return {
        aes_iv: res.data.aes_iv,
        aes_key: res.data.aes_key,
        is_aes: res.data.is_aes,
        connectionUrl
      };
    };

    InitSDK(getWsParams);
  } catch (error) {
    console.error(error, 'error');
  }
};

const initDevices = async () => {
  try {
    let fingerInfo = await fingerPrint.load();
    let uuid = await fingerInfo.get();
    const deviceId = uuid.visitorId;
    localStorage.setItem('deviceId', deviceId);

    const userToken = getLocalStorage(import.meta.env.VITE_TOKEN_KEY);

    if (!!userToken) {
      resume();
      getWsToken();
      userStore.updatePersonData();
    } else {
      pause();
    }
  } catch (error) {
    pause();
  } finally {
    getTagList();
    getAppConfig();
    getBaywindow();
    getAdList();
  }
};

const goToAccountLogin = () => {
  router.push('/account-login');
  hasNeedLogin.value = false;
};

const confirmHandle = () => {
  pause();
  userStore.logoutHandle();
  appStore.SetHasNeedLoginAction(false);
  router.replace({ path: '/login' });
};

const cancelHandle = async () => {
  const { isFinished } = await useLogin({
    type: 0
  });

  if (isFinished.value) {
    console.log(1);
    sessionStorage.removeItem(VITE_HIDDEN_TAB);
    sessionStorage.removeItem('HOME_POPUP');
    sessionStorage.removeItem('GAME_POPUP');
    setTimeout(() => {
      appStore.SetHasNeedLoginAction(false);
      appStore.SetHasNeedAdAction(true);
      appStore.SetHomePopupVisibleAction(true);
      router.replace({ path: PageEnum.BASE_HOME });
    }, 500);
  }
};

const confirmPhoneHandle = () => {
  appStore.SetHasNeedPhoneAction(false);
  router.push({ path: '/bind_phone' });
};

const cancelPhoneHandle = () => {
  appStore.SetHasNeedPhoneAction(false);
};

function hideLoadingPage() {
  const getLoadingId = document.getElementById('loading-overlay');
  getLoadingId.style.display = 'none';
}

watch(() => route.path, (path) => {
  if (path.startsWith('/game_lottery')) {
    currentTheme.value = 'light'
  } else {
    currentTheme.value = 'dark'
  }
}, { immediate: true })

onMounted(() => {
  getRefferUrl(window.location.href);
  hideLoadingPage();
  // const { channelCode = 'abcd1234', code = '' } = param2Obj(window.location.href);
  // sessionStorage.setItem('channelCode', channelCode); // 设置渠道码到sessionStorage
  // sessionStorage.setItem('inviteCode', code); // 设置邀请码到sessionStorage
  initDevices();
});
</script>

<style lang="less">
.scroll-container {
  --webkit-overflow-scrolling: touch;
}

.backface-visible {
  backface-visibility: visible;
}

.backface-hidden {
  backface-visibility: hidden;
}
</style>
