import { PageEnum } from '@/enums/pageEnum';

const Login = () => import('@/views/login/login.vue');
const AccountLogin = () => import('@/views/login/account_login.vue');
// const Launch = () => import('@/views/launch/launch.vue');
// const AppPage = () => import('@/views/application/application.vue');
const HomePage = () => import('@/views/home/<USER>');
const ShortVideoPage = () => import('@/views/shorts/shorts.vue');
const ActivityPage = () => import('@/views/activity/activity.vue');

const GamesPage = () => import('@/views/games/games.vue');

const ShortChannelPage = () => import('@/views/shorts_channel/shorts_channel.vue');
const RechargePage = () => import('@/views/recharge/recharge.vue');
const RechargeDetailPage = () => import('@/views/recharge/children/recharge_detail.vue');
const RechargeResultPage = () => import('@/views/recharge/children/recharge_result.vue');
const WithdrawPage = () => import('@/views/withdraw/withdraw.vue');
const WithdrawDetailsPage = () => import('@/views/withdraw_details/withdraw_details.vue');
const BindWalletPage = () => import('@/views/withdraw/children/bind_wallet.vue');
const BindBankPage = () => import('@/views/withdraw/children/bind_bankcard.vue');
const GameCategory = () => import('@/views/game_category/game_category.vue');
const GameRecord = () => import('@/views/game_record/game_record.vue');
const GameRecordOrder = () => import('@/views/game_record_order/game_record_order.vue');
const GameRecordDetail = () => import('@/views/game_record_detail/game_record_detail.vue');

const GameLottery = () => import('@/views/game_lottery/game_lottery.vue');
const LotteryHome = () => import('@/views/game_lottery/children/home.vue');
const LotteryRecord = () => import('@/views/game_lottery/children/record.vue');
const LotteryResult = () => import('@/views/game_lottery/children/result.vue');
const GameExplain = () => import('@/views/game_lottery/children/explain.vue');


const ProfilePage = () => import('@/views/profile/profile.page.vue');
const VideoDetails = () => import('@/views/video_details/video_details.vue');
const VideoTag = () => import('@/views/video_tag/video_tag.vue');
const SearchPage = () => import('@/views/search/search.vue');
const SearchResultPage = () => import('@/views/search/children/result.vue');
const MoreVideo = () => import('@/views/more_video/more_video.vue');
const ExpenseRecord = () => import('@/views/expenses_record/expenses_record.vue');
const FocusRecord = () => import('@/views/focus_record/focus_record.vue');
const AboutPage = () => import('@/views/aboutus/aboutus.vue');
const BindPhone = () => import('@/views/bind_phone/bind_phone.vue');
const BindPhoneSuccess = () => import('@/views/bind_phone/bind_phone_success.vue');
const AmountRecord = () => import('@/views/amount_record/amount_record.vue');
const AmountWithdrawPage = () => import('@/views/amount_record/children/withdraw.vue');
const WebviewPage = () => import('@/views/webview/webview.vue');
const WebviewGamePage = () => import('@/views/webview_game/webview_game.vue');
const WebviewHtmlPage = () => import('@/views/webview_html/webview_html.vue');
const WebviewServerPage = () => import('@/views/webview_server/webview_server.vue');
const WebviewActivityPage = () => import('@/views/webview_activity/webview_activity.vue');

const PayPasswordPage = () => import('@/views/pay_password/pay_password.vue');
const PayPasswordAdd = () => import('@/views/pay_password/children/add.vue');
const PayPasswordReset = () => import('@/views/pay_password/children/reset.vue');
const PayPasswordRecover = () => import('@/views/pay_password/children/recover.vue');

const PromotionPage = () => import('@/views/promotion/promotion.vue');
const PromotionUserPage = () => import('@/views/promotion/children/promotion_user/index.vue');
const PromotionPayPage = () => import('@/views/promotion/children/promotion_pay/index.vue');
const PromotionGameRecordPage = () => import('@/views/promotion/children/promotion_game_record/index.vue');
const PromotionGameDetailPage = () => import('@/views/promotion/children/promotion_game_detail/index.vue');
const PromotionBindPage = () => import('@/views/promotion/children/promotion_bind/index.vue');
const PromotionPlanPage = () => import('@/views/promotion/children/promotion_plan/index.vue');
const PromotionPlanSubPage = () => import('@/views/promotion/children/promotion_plan_sub/index.vue');
const PromotionRecordPage = () => import('@/views/promotion/children/promotion_record/index.vue');
const PromotionRelationPage = () => import('@/views/promotion/children/promotion_relation/index.vue');
const PromotionCartePage = () => import('@/views/promotion/children/promotion_carte/index.vue');

// const Feedback = () => import('@/views/feedback/feedback.vue');
const Officialgroup = () => import('@/views/official_group/official_group.vue');
const Business = () => import('@/views/business/business.vue');

const Newbietask = () => import('@/views/newbietask/newbietask.vue');
const TaskDetail = () => import('@/views/task_detail/task_detail.vue');

const ActivityClaim = () => import('@/views/activity_claim/activity_claim.vue');
const VipPage = () => import('@/views/vip/vip.vue');
const VipRecordPage = () => import('@/views/vip_record/vip_record.vue');
const VipDetailPage = () => import('@/views/vip_detail/vip_detail.vue');

const invitePage = () => import('@/views/invite/invite.vue');
const inviteRecordPage = () => import('@/views/invite_record/invite_record.vue');

const SettingPage = () => import('@/views/setting/setting.vue');
const PasswordSettingPage = () => import('@/views/setting/password_setting.vue');
const ProfileData = () => import('@/views/profile_data/profile_data.vue');
const EditNickname = () => import('@/views/edit_nickname/edit_nickname.vue');


const routeModuleList = [
  // {
  //   path: '/',
  //   component: Launch
  // },
  // {
  //   path: '/home',
  //   component: AppPage,
  //   name: 'Home',
  //   meta: { keepAlive: true, baywindow: true }
  // },
  {
    path: '/login',
    component: Login
  },
  {
    path: '/account-login',
    component: AccountLogin,
  },
  {
    path: '/video',
    name: 'Video',
    component: HomePage,
    meta: { keepAlive: true, baywindow: true }
  },
  {
    path: '/dsp',
    name: 'Shorts',
    component: ShortVideoPage,
    meta: { keepAlive: true, baywindow: true }
  },
  {
    path: '/dsp_channel',
    component: ShortChannelPage,
    meta: { title: '全部频道' }
  },
  {
    path: '/hd',
    name: 'Activity',
    component: ActivityPage,
    meta: { title: '优惠活动' }
  },
  {
    path: '/vip',
    component: VipPage,
    meta: { title: 'VIP等级' }
  },
  {
    path: '/vip_record',
    component: VipRecordPage,
    meta: { title: '领取记录' }
  },
  {
    path: '/vip_detail',
    component: VipDetailPage,
    meta: { title: 'VIP详情' }
  },
  {
    path: '/newbietask',
    component: Newbietask,
  },
  {
    path: '/task_detail',
    component: TaskDetail,
  },
  {
    path: '/activity_claim',
    component: ActivityClaim,
    meta: { title: '领取记录' }
  },
  {
    path: '/yx',
    name: 'Games',
    component: GamesPage,
    meta: { keepAlive: true }
  },
  {
    path: '/wd',
    name: 'Profile',
    component: ProfilePage,
    meta: { keepAlive: true, baywindow: true }
  },
  {
    path: '/videos/:id(\\d+)',
    name: 'VideoDetails',
    component: VideoDetails,
    meta: { keepAlive: true, baywindow: true }
  },
  {
    path: '/video_tag/:tagId(\\d+)',
    name: 'VideoTag',
    component: VideoTag,
  },
  {
    path: '/search',
    name: 'Search',
    component: SearchPage,
    meta: { baywindow: true },
  },
  {
    path: '/search/result',
    component: SearchResultPage
  },
  {
    path: '/more_video',
    component: MoreVideo,
    meta: { baywindow: true }
  },
  {
    path: '/aboutus',
    component: AboutPage,
    meta: { title: '关于我们' }
  },
  // {
  //   path: '/feedback',
  //   component: Feedback,
  //   meta: { title: '意见反馈' }
  // },
  // {
  //   path: '/official_group',
  //   component: Officialgroup,
  //   meta: { title: '官方群组' }
  // },
  {
    path: '/business',
    component: Business,
    meta: { title: '商务合作' }
  },
  {
    path: '/bind_phone',
    component: BindPhone
  },
  {
    path: '/bind_phone_success',
    component: BindPhoneSuccess,
  },
  {
    path: '/focus_record',
    component: FocusRecord,
    meta: { title: '我的收藏' }
  },
  {
    path: '/expenses_record',
    component: ExpenseRecord,
    meta: { title: '观影记录' }
  },
  {
    path: '/cz',
    component: RechargePage,
    meta: { title: '充值' }
  },
  {
    path: '/recharge_detail',
    component: RechargeDetailPage,
    meta: { title: '存款信息' }
  },
  {
    path: '/recharge_result',
    component: RechargeResultPage,
    meta: { title: '订单确认中' }
  },
  {
    path: '/withdraw',
    component: WithdrawPage,
    meta: { title: '提现' }
  },
  {
    path: '/withdraw_details',
    component: WithdrawDetailsPage,
    meta: { title: '提现' }
  },
  {
    path: '/bind_wallet',
    component: BindWalletPage,
    meta: { title: '添加钱包' }
  },
  {
    path: '/bind_bankcard',
    component: BindBankPage,
    meta: { title: '绑定银行卡' }
  },
  {
    path: '/game_category',
    component: GameCategory
  },
  {
    path: '/game_record',
    component: GameRecord,
    meta: { title: '游戏记录' }
  },
  {
    path: '/game_record_order',
    component: GameRecordOrder,
    meta: { title: '游戏注单' }
  },
  {
    path: '/game_record_detail',
    component: GameRecordDetail,
    meta: { title: '下注详情' }
  },
  {
    path: '/amount_record',
    component: AmountRecord,
    meta: { title: '充提记录' }
  },
  {
    path: '/amount_withdraw',
    component: AmountWithdrawPage,
    meta: { title: '详情' }
  },
  {
    path: '/pay_password',
    component: PayPasswordPage
  },
  {
    path: '/pay_password_set',
    component: PayPasswordAdd,
    meta: { title: '设置支付密码' }
  },
  {
    path: '/pay_password_reset',
    component: PayPasswordReset,
    meta: { title: '重置支付密码' }
  },
  {
    path: '/pay_password_recover',
    component: PayPasswordRecover,
    meta: { title: '找回支付密码' }
  },
  {
    path: '/webview',
    component: WebviewPage
  },
  {
    path: '/webview_game',
    component: WebviewGamePage
  },
  {
    path: '/webview_html',
    component: WebviewHtmlPage
  },
  {
    path: '/webview_server',
    component: WebviewServerPage
  },
  {
    path: '/webview_activity',
    component: WebviewActivityPage
  },
  {
    path: '/promotion',
    component: PromotionPage,
    meta: { title: '全民代理' }
  },
  {
    path: '/promotion_user',
    component: PromotionUserPage,
    meta: { title: '成员管理' }
  },
  {
    path: '/promotion_pay',
    component: PromotionPayPage,
    meta: { title: '我要赚钱' }
  },
  {
    path: '/promotion_game_record',
    component: PromotionGameRecordPage,
    meta: { title: '游戏记录' }
  },
  {
    path: '/promotion_game_detail',
    component: PromotionGameDetailPage,
    meta: { title: '游戏注单' }
  },
  {
    path: '/promotion_bind',
    component: PromotionBindPage,
    meta: { title: '绑定下级' }
  },
  {
    path: '/promotion_plan',
    component: PromotionPlanPage,
    meta: { title: '佣金方案' }
  },
  {
    path: '/promotion_plan_sub',
    component: PromotionPlanSubPage,
    meta: { title: '下级佣金方案' }
  },
  {
    path: '/promotion_record',
    component: PromotionRecordPage,
    meta: { title: '佣金报表' }
  },
  {
    path: '/promotion_relation',
    component: PromotionRelationPage,
    meta: { title: '上下级' }
  },
  {
    path: '/promotion_carte',
    component: PromotionCartePage,
    meta: { title: '我的名片' }
  },
  {
    path: '/invite',
    component: invitePage,
    meta: { title: '邀请好友' }
  },
  {
    path: '/invite_record',
    component: inviteRecordPage,
    meta: { title: '邀请记录' }
  },
  {
    path: '/setting',
    component: SettingPage,
    meta: { title: '系统设置' }
  },
  {
    path: '/password_setting',
    component: PasswordSettingPage,
    meta: { title: '设置账号密码' }
  },
  {
    path: '/profile_data',
    component: ProfileData,
    meta: { title: '个人资料' }
  },
  {
    path: '/edit_nickname',
    component: EditNickname,
    meta: { title: '修改昵称' }
  },
  {
    path: '/game_lottery',
    component: GameLottery,
    redirect: '/game_lottery/home',
    children: [
      {
        path: 'home',
        name: 'LotteryHome',
        component: LotteryHome
      },
      {
        path: 'record',
        name: 'LotteryRecord',
        component: LotteryRecord
      },
      {
        path: 'result',
        name: 'LotteryResult',
        component: LotteryResult
      },
      {
        path: 'explain',
        name: 'GameExplain',
        component: GameExplain
      }
    ]
  },
  {
    name: 'notFound',
    path: '/:path(.*)+',
    redirect: { path: PageEnum.BASE_HOME }
  }
];

export default routeModuleList