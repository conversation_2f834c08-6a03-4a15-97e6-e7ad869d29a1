import { isNavigationFailure } from 'vue-router'
// import NProgress from 'nprogress';
// import 'nprogress/nprogress.css';
import { useRouteStoreWidthOut } from '@/store/route'
// NProgress.configure({ parent: '#app' })

const whitePathList = ["/login", "/launch"]; // no redirect whitelist

export function createRouterGuards(router) {
  router.beforeEach((to, from, next) => {
    // NProgress.start();

    next();
  });
  
  router.afterEach((to, from, failure) => {
    if (isNavigationFailure(failure)) {
      console.warn('failed navigation', failure)
    };

    const routeStore = useRouteStoreWidthOut();

    // 在这里设置需要缓存的组件名称
    const keepAliveComponents = routeStore.keepAliveComponents;

    // 获取当前组件名
    const currentComName = to.name && to.matched.find(item => item.name === to.name)?.name;
    // 如果 currentComName 且 keepAliveComponents 不包含 currentComName 且 即将要进入的路由 meta 属性里 keepAlive 为 true，则缓存该组件
    if (currentComName && !keepAliveComponents.includes(currentComName) && to.meta?.keepAlive) {
      // 需要缓存的组件
      keepAliveComponents.push(currentComName)
      // keepAlive 为 false 则不缓存
    }

    else if (!to.meta?.keepAlive) {
      // 这里的作用一开始组件设置为缓存，之后又设置不缓存但是它还是存在 keepAliveComponents 数组中
      // keepAliveComponents 使用 findIndex 与 当前路由对比，如果存在则返回具体下标位置，不存在返回 -1
      const index = routeStore.keepAliveComponents.findIndex(name => name === currentComName)
      if (index !== -1) {
        // 通过返回具体下标位置删除 keepAliveComponents 数组中缓存的 元素
        keepAliveComponents.splice(index, 1)
      }
    }

    routeStore.setKeepAliveComponents(keepAliveComponents);

    // NProgress.done();
  });
}