import { createRouter, createWebHistory } from 'vue-router';
import { createRouterGuards } from './router-guards';
import routeModuleList from './modules';


const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: routeModuleList,
  strict: true,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) return savedPosition;

    return { top: 0 };
  }
});

export function setupRouter(app) {
  app.use(router)
  // 创建路由守卫
  createRouterGuards(router)
}

export default router;
