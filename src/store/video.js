import { defineStore } from 'pinia';

export const useVideoStore = defineStore('video', {
  state: () => ({
    // 默认选择筹码,
    chip: 6,
    // 默认显示的筹码
    chips: [6, 7, 8, 9, 10, 11],
    // 可选择筹码
    normalChips: [
      { point: 10, active: '10', key: 6 },
      { point: 20, active: '20', key: 7 },
      { point: 50, active: '50', key: 8 },
      { point: 100, active: '100', key: 9 },
      { point: 200, active: '200', key: 10 },
      { point: 500, active: '500', key: 11 },
      { point: 1000, active: '1k', key: 12 },
      { point: 5000, active: '5k', key: 13 },
      { point: 10000, active: '10k', key: 14 },
      { point: 'ALL', active: 'all_in', key: 15 }
    ],
    customChips: [
      { point: '', active: 'customize1_blank', inactive: 'customize1', key: 1, custom: true },
      { point: '', active: 'customize2_blank', inactive: 'customize2', key: 2, custom: true },
      { point: '', active: 'customize3_blank', inactive: 'customize3', key: 3, custom: true },
      { point: '', active: 'customize4_blank', inactive: 'customize4', key: 4, custom: true },
      { point: '', active: 'customize5_blank', inactive: 'customize5', key: 5, custom: true }
    ],
    // 上期投注
    lastTimeBettings: {},
    // 是否已设置保留
    hasHelpKeep: false,
    // 是否保留上一期预选
    keepLastIssuePreselected: false
  }),
  getters: {
    getDisplayChip: (state) => (key) => {
      return state.selectedChips.find((o) => o.key === key);
    }
  },
  actions: {
    setChip(chip) {
      this.chip = chip;
    },

    setChips(chips) {
      this.chips = chips;
    },

    setLastTimeBettings({ game_id, bet }) {
      this.lastTimeBettings[game_id] = bet;
    },

    setHasHelpKeep(paylod) {
      this.hasHelpKeep = paylod;
    },
    setKeepLastIssuePreselected(paylod) {
      this.keepLastIssuePreselected = paylod;
    }
  },
  persist: {
    key: 'hemav_video_displayed',
    storage: localStorage,
    paths: ['chip', 'chips', 'normalChips', 'lastTimeBettings', 'customChips', 'hasHelpKeep', 'keepLastIssuePreselected']
  }
});
