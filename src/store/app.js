import { defineStore } from 'pinia';
import { store } from '@/store';
export const useAppStore = defineStore('app', {
  state: () => ({
    hasNeedAd: true,
    hasNeedLogin: false,
    hasNeedPhone: false,
    hasNeedPassword: false,
    hasNeedService: false,
    hasCacheVideoPage: false,
    homePopupVisible: false,
    homePopupBannerVisible: false,
    customerServiceUrl: [],
    playedID: null,
    appConfig: {},
    adList: [],
    tagList: [],
    baywindow: []
  }),
  getters: {
    config: (state) => state.appConfig,
    adItem: (state) => {
      return (id) => state.adList.find((o) => o.id === id)?.adList
    } 
  },
  actions: {
    SetHasNeedAdAction(payload) {
      this.hasNeedAd = payload;
    },
    SetHasNeedLoginAction(preload) {
      this.hasNeedLogin = preload;
    },
    SetHasNeedPhoneAction(preload) {
      this.hasNeedPhone = preload;
    },
    SetHasNeedPasswordAction(preload) {
      this.hasNeedPassword = preload;
    },
    SetHasNeedServiceAction(preload) {
      this.hasNeedService = preload;
    },
    SetHasCacheVideoPageAction(preload) {
      this.hasCacheVideoPage = preload;
    },
    SetHomePopupVisibleAction(preload) {
      this.homePopupVisible = preload;
    },
    SetHomePopupBannerVisibleAction(preload) {
      this.homePopupBannerVisible = preload;
    },
    SetCustomerServiceUrlAction(preload) {
      this.customerServiceUrl = preload;
    },
    SetTagListReducer(payload = {}) {
      const keys = Object.keys(payload);
      const arr = keys.reduce((c, p) => {
        c = c.concat(payload[p]);
        return c;
      }, []);

      this.tagList = arr;
    },
    SetAppAdListReducer(payload) {
      this.adList = payload;
    },
    SetAppConfigReducer(payload) {
      this.appConfig = payload;
    },
    SetBaywindowAction(payload) {
      this.baywindow = payload;
    },
    SetPlatedIDAction(payload) {
      this.playedID = payload;
    }
  }
});
// Need to be used outside the setup
export function useAppStoreWidthOut() {
  return useAppStore(store);
}
