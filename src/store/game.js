import { defineStore } from 'pinia';

export const useGameStore = defineStore('game', {
  state: () => ({
    // 默认选择筹码,
    chip: 0,
    // 默认显示的筹码
    chips: [0, 1, 2, 4, 5, 7, 8],
    // 可选择筹码
    selectedChips: [
      { point: 1, icon: 0, key: 0 },
      { point: 5, icon: 1, key: 1 },
      { point: 10, icon: 2, key: 2 },
      { point: 20, icon: 3, key: 3 },
      { point: 50, icon: 4, key: 4 },
      { point: 100, icon: 5, key: 5 },
      { point: 200, icon: 6, key: 6 },
      { point: 500, icon: 0, key: 7 },
      { point: 1000, icon: 1, key: 8 },
      { point: 2000, icon: 2, key: 9 },
      { point: 5000, icon: 3, key: 10 },
      { point: 10000, icon: 4, key: 11 },
      { point: 20000, icon: 5, key: 12 },
      { point: 50000, icon: 6, key: 13 },
      { point: 100000, icon: 0, key: 14 },
      { point: 200000, icon: 1, key: 15 },
      { point: 500000, icon: 2, key: 16 },
      { point: 1000000, icon: 3, key: 17 },
      ...Array.from({ length: 6 }).map((_, i) => ({ point: '', icon: 'x', key: 99 + i, custom: true }))
    ],
    closedTime: 5,
    // 剩余时间
    time: 5000,
    //
    next: false,
    // 加载中
    isLoading: true,
    // 是否封盘
    isStop: false,
    // 当前彩种信息
    currentLottery: {},
    // 当前彩种期号
    currentLotteryPeriod: null,
    // 初始化彩种列表
    crybetos: [],
    // 是否已设置保留
    hasHelpKeep: false,
    // 是否保留上一期预选
    keepLastIssuePreselected: false
  }),
  getters: {
    getDisplayChip: (state) => (key) => {
      return state.selectedChips.find((o) => o.key === key);
    }
  },
  actions: {
    reset() {
      this.closedTime = 5;
      this.next = false;
      this.isLoading = false;
      this.isStop = true;
      this.currentLottery = {};
      this.currentLotteryPeriod = null;
    },

    setTime(payload) {
      this.time = Date.now() + payload * 1000;
    },

    setNext(payload) {
      this.next = payload;
    },

    setClosedTime(payload) {
      this.closedTime = payload;
    },

    setLoading(payload) {
      this.isLoading = payload;
    },

    SetGameListAction(payload) {
      this.crybetos = payload;
    },

    setChip(chip) {
      this.chip = chip;
    },

    setChips(chips) {
      this.chips = chips;
    },

    setIsStop(payload) {
      this.isStop = payload;
    },

    setCurrentLottery(paylod) {
      this.currentLottery = paylod;
    },

    setCurrentLotteryPeriod(pariod) {
      this.currentLotteryPeriod = pariod;
    },

    setHasHelpKeep(paylod) {
      this.hasHelpKeep = paylod;
    },
    setKeepLastIssuePreselected(paylod) {
      this.keepLastIssuePreselected = paylod;
    }
  },
  persist: {
    key: 'COMMON_CHIP_DISPLAYED_STORAGE_KEY',
    storage: localStorage,
    paths: ['chip', 'chips', 'selectedChips', 'hasHelpKeep', 'keepLastIssuePreselected']
  }
});
