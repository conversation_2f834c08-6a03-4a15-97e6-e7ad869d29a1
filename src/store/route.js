import { defineStore } from 'pinia';
import { store } from '@/store';
export const useRouteStore = defineStore('app-route', {
  state: () => ({
    menus: [],
    routers: [],
    keepAliveComponents: [],
  }),
  getters: {
    getMenus() {
      return this.menus
    },
  },
  actions: {
    setRouters(routers) {
      this.routers = routers
    },
    setMenus(menus) {
      this.menus = menus
    },
    setKeepAliveComponents(compNames) {
      // 设置需要缓存的组件
      this.keepAliveComponents = compNames
    },
  },
});
// Need to be used outside the setup
export function useRouteStoreWidthOut() {
  return useRouteStore(store);
}
