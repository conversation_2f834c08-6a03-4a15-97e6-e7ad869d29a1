/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AvatarPopup: typeof import('./components/AvatarPopup/index.vue')['default']
    Banner: typeof import('./components/banner/banner.vue')['default']
    CellBanner: typeof import('./components/banner/cell-banner.vue')['default']
    CountrySelector: typeof import('./components/CountrySelector/index.vue')['default']
    DanmuLayer: typeof import('./components/danmuLayer/danmuLayer.vue')['default']
    Icon: typeof import('./components/SvgIcon/Icon.vue')['default']
    Img: typeof import('./components/img/img.vue')['default']
    LoginMethodSelector: typeof import('./components/login/LoginMethodSelector.vue')['default']
    LoginSelector: typeof import('./components/login/LoginSelector.vue')['default']
    Notice: typeof import('./components/notice/notice.vue')['default']
    Render: typeof import('./components/ThemeSort/render.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ServicePopup: typeof import('./components/ServicePopup/index.vue')['default']
    Tabbar: typeof import('./components/TinsTabbar/tabbar.vue')['default']
    TopBanner: typeof import('./components/topBanner/topBanner.vue')['default']
    VanActionSheet: typeof import('vant/es')['ActionSheet']
    VanBadge: typeof import('vant/es')['Badge']
    VanButton: typeof import('vant/es')['Button']
    VanCalendar: typeof import('vant/es')['Calendar']
    VanCell: typeof import('vant/es')['Cell']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanConfigProvider: typeof import('vant/es')['ConfigProvider']
    VanCountDown: typeof import('vant/es')['CountDown']
    VanDatePicker: typeof import('vant/es')['DatePicker']
    VanDialog: typeof import('vant/es')['Dialog']
    VanDropdownItem: typeof import('vant/es')['DropdownItem']
    VanDropdownMenu: typeof import('vant/es')['DropdownMenu']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanFloatingBubble: typeof import('vant/es')['FloatingBubble']
    VanForm: typeof import('vant/es')['Form']
    VanGrid: typeof import('vant/es')['Grid']
    VanGridItem: typeof import('vant/es')['GridItem']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanIndexAnchor: typeof import('vant/es')['IndexAnchor']
    VanIndexBar: typeof import('vant/es')['IndexBar']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanNoticeBar: typeof import('vant/es')['NoticeBar']
    VanNumberKeyboard: typeof import('vant/es')['NumberKeyboard']
    VanPasswordInput: typeof import('vant/es')['PasswordInput']
    VanPopover: typeof import('vant/es')['Popover']
    VanPopup: typeof import('vant/es')['Popup']
    VanProgress: typeof import('vant/es')['Progress']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanSearch: typeof import('vant/es')['Search']
    VanSpace: typeof import('vant/es')['Space']
    VanSwipe: typeof import('vant/es')['Swipe']
    VanSwipeItem: typeof import('vant/es')['SwipeItem']
    VanTab: typeof import('vant/es')['Tab']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanTabs: typeof import('vant/es')['Tabs']
    VanTextEllipsis: typeof import('vant/es')['TextEllipsis']
  }
}
