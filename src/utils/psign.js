import { sign } from 'jsonwebtoken-esm';

const appId = 1323165252;

const audioVideoType = "ProtectedAdaptive" // 播放的音视频类型
const rawAdaptiveDefinition = 10 // 允许输出的未加密的自适应码流模板 ID
const imageSpriteDefinition = 10 // 做进度条预览的雪碧图模板 ID
const privateEncryptionDefinition = 1464303 // SimpleAES 的 转自适应码流模板 ID。
const widevineDefinition = 13
const fairPlayDefinition = 11
const currentTime = Math.floor(Date.now()/1000)
const psignExpire = currentTime + 3600 // 可任意设置过期时间，示例1h
const urlTimeExpire = psignExpire.toString(16) // 可任意设置过期时间，16进制字符串形式，示例1h
const playKey = 'C9Kz045ck5ustmgKr2fL'
export function generatepsign (fileId, domain) {

  var url = new URL(domain)

  var payload = {
    appId: appId,
    fileId: fileId,
    contentInfo: {
      audioVideoType: audioVideoType,
      drmAdaptiveInfo: {
        privateEncryptionDefinition: privateEncryptionDefinition,
      }
    },
    currentTimeStamp: currentTime,
    expireTimeStamp: psignExpire,
    urlAccessInfo: {
      t: urlTimeExpire,
      externalDomain: url.host || 'r.examcdn.com',
      scheme: url.protocol.split(':')[0]?.toUpperCase() || 'HTTPS'
    }
  }

  const token = sign(payload, playKey)

  return token
}
