/**
 * Format timestamp to relative time ago string
 * @param timestamp - Date object or string timestamp
 * @returns Formatted time ago string in Chinese
 */
export const formatTimeAgo = (timestamp: Date | string): string => {
  try {
    const now = new Date();
    const date = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return '刚刚';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}天前`;
    return date.toLocaleDateString('zh-CN');
  } catch (error) {
    return '刚刚';
  }
};
