import pkg from '../../package.json'
import { getConfigFileName } from '../../build/getConfigFileName'

import { warn } from '@/utils/log'

export function getCommonStoragePrefix() {
  const { VITE_GLOB_APP_SHORT_NAME } = getAppEnvConfig()
  return `${VITE_GLOB_APP_SHORT_NAME}__${getEnv()}`.toUpperCase()
}

// Generate cache key according to version
export function getStorageShortName() {
  return `${getCommonStoragePrefix()}${`__${pkg.version}`}__`.toUpperCase()
}

export function getAppEnvConfig() {
  const ENV_NAME = getConfigFileName(import.meta.env)

  // Get the global configuration (the configuration will be extracted independently when packaging)
  const ENV = (import.meta.env.DEV
    ? import.meta.env
    : window[ENV_NAME])

  const {
    VITE_GLOB_APP_SHORT_NAME,
    VITE_GLOB_APP_BASE_API,
    VITE_GLOB_APP_GAME_API,
    VITE_GLOB_APP_FINANCE_API,
    VITE_GLOB_APP_CHAT_API,
    VITE_GLOB_APP_PROMOTION_API,
    VITE_GLOB_APP_PLAY_LICENSE_URL,
  } = ENV

  if (!/^[a-z_]*$/i.test(VITE_GLOB_APP_SHORT_NAME)) {
    warn(
      `VITE_GLOB_APP_SHORT_NAME Variables can only be characters/underscores, please modify in the environment variables and re-running.`,
    )
  }

  return {
    VITE_GLOB_APP_SHORT_NAME,
    VITE_GLOB_APP_BASE_API,
    VITE_GLOB_APP_GAME_API,
    VITE_GLOB_APP_FINANCE_API,
    VITE_GLOB_APP_CHAT_API,
    VITE_GLOB_APP_PROMOTION_API,
    VITE_GLOB_APP_PLAY_LICENSE_URL,
  }
}

/**
 * @description: Development model
 */
export const devMode = 'development'

/**
 * @description: Production mode
 */
export const prodMode = 'production'

/**
 * @description: Get environment variables
 * @returns:
 * @example:
 */
export function getEnv() {
  return import.meta.env.MODE
}

/**
 * @description: Is it a development mode
 * @returns:
 * @example:
 */
export function isDevMode() {
  return import.meta.env.DEV
}

/**
 * @description: Is it a production mode
 * @returns:
 * @example:
 */
export function isProdMode() {
  return import.meta.env.PROD
}
