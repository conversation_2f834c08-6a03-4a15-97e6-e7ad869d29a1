import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

dayjs.extend(duration)


import { isEmpty } from './types'

export const formatter = function (
  date,
  format = 'YYYY-MM-DD HH:mm:ss'
) {
  if (isEmpty(format)) return date
  if (format === 'x') return +date
  return dayjs(date).format(format)
}

export const parseDate = function (
  date,
  format = 'YYYY-MM-DD HH:mm:ss'
) {
  const day =
    isEmpty(format) || format === 'x'
      ? dayjs(date)
      : dayjs(date, format)

  return day.isValid() ? day : undefined
}

export const pickerDate = function (date, format = 'YYYY-MM-DD') {
  date = parseDate(date)
  const formatValue = formatter(date, format)
  return formatValue.split('-')
}

export const getDateRange = (left = 0) => {
  const maps = [dayjs().subtract(left, 'day').startOf('date'), dayjs().subtract(left, 'day').endOf('date')]
  return maps.map(date => formatter(date))
}

export const isAfter = function (a, b) {
  return dayjs(a).isAfter(dayjs(b))
}

export const isBefore = function (a, b) {
  return dayjs(a).isBefore(dayjs(b))
}

export const durationSeconds = function (seconds, simple = false) {
  let format = 'HH:mm:ss'

  if (seconds < 3600 && simple) format = 'mm:ss'

  return dayjs.duration(seconds, 'seconds').format(format);
}

export const formatBankCardNumber = (str) => str.replace(/\s/g, '').replace(/(\d{4})/g, '$1 ')