/* eslint-disable no-useless-escape */

import axios from 'axios';

import CryptoJS from 'crypto-js';

export function getImageType(imgUrl) {
  if (!imgUrl) return '';
  const type = imgUrl.split('.').pop();
  const lastPointIndex = type?.lastIndexOf('?');
  if (lastPointIndex != -1) {
    return type?.substring(0, lastPointIndex);
  }
  return type;
}
export async function getImageBase64(imgUrl, key, iv) {
  if (!imgUrl) {
    return Promise.reject('');
  }
  return new Promise((resolve, reject) => {
    axios({
      method: 'GET',
      url: imgUrl,
      responseType: 'arraybuffer'
    })
      .then((res) => {
        const base64 = decryptoImgData(res.data, key, iv);
        const imageType = getImageType(imgUrl);
        resolve(`data:image/${imageType};base64,${base64}`);
      })
      .catch((err) => {
        console.log(err, 'error');
        reject(err);
      });
  });
}
export function decryptoImgData(buffer, key, iv) {
  const view = new TextDecoder('utf-8').decode(buffer);
  const base64String = view.toString(CryptoJS.enc.Base64);

  const decrypted = CryptoJS.AES.decrypt(base64String, CryptoJS.enc.Utf8.parse(key), {
    mode: CryptoJS.mode.CBC,
    iv: CryptoJS.enc.Utf8.parse(iv),
    padding: CryptoJS.pad.Pkcs7
  });

  return decrypted.toString(CryptoJS.enc.Base64);
}

export function encryptoData(data, key, iv) {
  const str = JSON.stringify(data);
  const encrypted = CryptoJS.AES.encrypt(str, CryptoJS.enc.Utf8.parse(key), {
    mode: CryptoJS.mode.CBC,
    iv: CryptoJS.enc.Utf8.parse(iv),
    padding: CryptoJS.pad.Pkcs7
  });
  return encrypted.toString();
}

export function decryptoData(base64String, key, iv) {
  const decrypted = CryptoJS.AES.decrypt(base64String, CryptoJS.enc.Utf8.parse(key), {
    mode: CryptoJS.mode.CBC,
    iv: CryptoJS.enc.Utf8.parse(iv),
    padding: CryptoJS.pad.Pkcs7
  });

  return decrypted.toString(CryptoJS.enc.Utf8);
}
