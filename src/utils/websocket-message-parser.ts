/**
 * WebSocket Message Parser Utility
 *
 * This module provides type guards and filters for different types of WebSocket messages
 * in the lottery popup comment system. It handles user comments, betting notifications,
 * and winning announcements by working directly with WebSocket message types.
 */

import type {
  WsMessage,
  UserWsMessage,
  BettingWsMessage,
  WinningWsMessage
} from '@/types/comment';

// Constants for message types and event types
export const MESSAGE_EVENT_TYPES = {
  GROUP_MESSAGE: 6,
  SUBSCRIBE: 2,
  UNSUBSCRIBE: 3
} as const;

export const USER_MESSAGE_TYPES = {
  NORMAL: 0,
  BARRAGE: 1
} as const;

export const GAME_MESSAGE_TYPES = {
  USER_BET: 'USER_BET',
  WIN_MESSAGE: 'WIN_MESSAGE'
} as const;

export const CLIENT_IDS = {
  GAME: 'Game'
} as const;



/**
 * Type guard to check if a WebSocket message is a user message
 * @param message - The WebSocket message to check
 * @returns True if the message is a user message
 */
export const isUserWsMessage = (message: WsMessage): message is UserWsMessage => {
  try {
    const asUserMessage = message as UserWsMessage;
    return (
      asUserMessage.event_type === MESSAGE_EVENT_TYPES.GROUP_MESSAGE &&
      asUserMessage.data &&
      typeof asUserMessage.data.type === 'number' &&
      (asUserMessage.data.type === USER_MESSAGE_TYPES.NORMAL || 
       asUserMessage.data.type === USER_MESSAGE_TYPES.BARRAGE) &&
      typeof asUserMessage.data.content === 'string' &&
      asUserMessage.data.user_info &&
      typeof asUserMessage.data.user_info.nickname === 'string'
    );
  } catch {
    return false;
  }
};

/**
 * Validate a user WebSocket message has required fields
 * @param message - The user WebSocket message to validate
 * @returns True if message has all required fields
 */
export const validateUserWsMessage = (message: UserWsMessage): boolean => {
  return !!(message.data?.content && message.data?.user_info?.nickname);
};

/**
 * Type guard to check if a WebSocket message is a betting message
 * @param message - The WebSocket message to check
 * @returns True if the message is a betting message
 */
export const isBettingWsMessage = (message: WsMessage): message is BettingWsMessage => {
  try {
    const asBettingMessage = message as BettingWsMessage;
    return (
      asBettingMessage.event_type === MESSAGE_EVENT_TYPES.GROUP_MESSAGE &&
      asBettingMessage.client_id === CLIENT_IDS.GAME &&
      asBettingMessage.data &&
      asBettingMessage.data.game_type === GAME_MESSAGE_TYPES.USER_BET &&
      asBettingMessage.data.data &&
      typeof asBettingMessage.data.data.game_name === 'string' &&
      typeof asBettingMessage.data.data.money === 'string' &&
      typeof asBettingMessage.data.data.game_id === 'number' &&
      Array.isArray(asBettingMessage.data.data.bet) &&
      asBettingMessage.data.data.bet.length > 0
    );
  } catch {
    return false;
  }
};

/**
 * Validate a betting WebSocket message has required fields
 * @param message - The betting WebSocket message to validate
 * @returns True if message has all required fields
 */
export const validateBettingWsMessage = (message: BettingWsMessage): boolean => {
  const { data } = message;
  return !!(
    data?.data?.game_name &&
    data?.data?.money &&
    data?.data?.game_id &&
    data?.data?.bet?.[0]?.bet_data &&
    message.uuid
  );
};

/**
 * Type guard to check if a WebSocket message is a winning message
 * @param message - The WebSocket message to check
 * @returns True if the message is a winning message
 */
export const isWinningWsMessage = (message: WsMessage): message is WinningWsMessage => {
  try {
    const asWinningMessage = message as WinningWsMessage;
    return (
      asWinningMessage.event_type === MESSAGE_EVENT_TYPES.GROUP_MESSAGE &&
      asWinningMessage.client_id === CLIENT_IDS.GAME &&
      asWinningMessage.data &&
      asWinningMessage.data.game_type === GAME_MESSAGE_TYPES.WIN_MESSAGE &&
      asWinningMessage.data.data &&
      typeof asWinningMessage.data.data.user_name === 'string' &&
      typeof asWinningMessage.data.data.money === 'string'
    );
  } catch {
    return false;
  }
};

export const isPrizeWsMessage = (message: WsMessage, userId: number): message is WinningWsMessage => {
  try {
    const asWinningMessage = message as WinningWsMessage;
    if (asWinningMessage.data.game_type === GAME_MESSAGE_TYPES.WIN_MESSAGE) {
      if (asWinningMessage.data.data.user_id === userId) {
        return true;
      }
      return false;
    }
    return false;
  } catch {
    return false;
  }
};

/**
 * Validate a winning WebSocket message has required fields
 * @param message - The winning WebSocket message to validate
 * @returns True if message has all required fields
 */
export const validateWinningWsMessage = (message: WinningWsMessage): boolean => {
  const { data } = message;
  return !!(data?.data?.user_name && data?.data?.money && message.uuid);
};

/**
 * Filter WebSocket messages to only include valid comment messages
 * @param message - The WebSocket message to filter
 * @returns The original message if it's a valid comment message, null otherwise
 */
export const filterWebSocketMessage = (message: WsMessage): WsMessage | null => {
  try {
    // Only handle group messages
    if (message.event_type !== MESSAGE_EVENT_TYPES.GROUP_MESSAGE) {
      return null;
    }

    if (isUserWsMessage(message) && validateUserWsMessage(message)) {
      return message;
    } else if (isBettingWsMessage(message) && validateBettingWsMessage(message)) {
      return message;
    } else if (isWinningWsMessage(message) && validateWinningWsMessage(message)) {
      return message;
    }

    return null;
  } catch (error) {
    console.warn('Failed to filter WebSocket message:', error);
    return null;
  }
};

/**
 * Filter multiple WebSocket messages
 * @param messages - Array of WebSocket messages to filter
 * @returns Array of valid WebSocket messages (excluding null results)
 */
export const filterWebSocketMessages = (messages: WsMessage[]): WsMessage[] => {
  return messages
    .map(filterWebSocketMessage)
    .filter((message): message is WsMessage => message !== null);
};
