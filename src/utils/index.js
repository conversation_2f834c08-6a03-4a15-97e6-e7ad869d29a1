import { isEmpty, isArray } from "./types";

export * from "./types";
export * from "./format";
export * from "./localStorage";
export * from "./libs";

export function noop() {}

export const selectLabel = (options, value, label) => {
  if (isEmpty(value)) return label;
  return options.find((o) => o.value === value)?.text;
};

export const selectTagsLabel = (arr, value = []) => {
  if (!isArray(arr) || !isArray(value)) return
  return value.map((o) => arr.find((t) => t.code === o)).filter(Boolean);
};

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = [];
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i]);
    }
  }
  return newArray;
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split("?")[1]).replace(/\+/g, " ");
  if (!search) {
    return {};
  }
  const obj = {};
  const searchArr = search.split("&");
  searchArr.forEach((v) => {
    const index = v.indexOf("=");
    if (index !== -1) {
      const name = v.substring(0, index);
      const val = v.substring(index + 1, v.length);
      obj[name] = val;
    }
  });
  return obj;
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return "";
  return cleanArray(
    Object.keys(json).map((key) => {
      if (json[key] === undefined) return "";
      return encodeURIComponent(key) + "=" + encodeURIComponent(json[key]);
    })
  ).join("&");
}

export function checkDevice() {
  var u = window.navigator.userAgent;
  return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) ? 2 : 1;
}

/**
 * 生成唯一ID
 */
export function UUID(e) {
  e = e || 16
  const t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  const a = t.length
  let n = ''
  for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
  return n
}

export const getRefferUrl = (url) => {
  const parseUrl = url ? new URL(url) : new URL(window.location.href);
  const pathWithParams = parseUrl.pathname + parseUrl.search;
  window.sessionStorage.setItem('ReffferUrl', pathWithParams);
  return pathWithParams;
};

export function insertAds(list, ads, interval = 5) {
  if (!ads || ads.length === 0) return list;
  const result = [];
  let adIndex = 0;
  for (let i = 0; i < list.length; i++) {
    result.push(toRaw(list[i]));
    if ((i + 1) % interval === 0 && ads.length > 0) {
      result.push({ ...toRaw(ads[adIndex]), isAd: true });
      adIndex = (adIndex + 1) % ads.length;
    }
  }
  return result;
}

export function openExternal(url) {
  const a = document.createElement("a");
  a.href = url;
  a.target = "_blank";
  a.rel = "noopener noreferrer";
  a.click();
}