import Compressor from 'compressorjs'

export function compressorImage (image, quality = 0.4) {
  return new Promise((resolve, reject) => {
    const t = new Compressor(image, {
      quality,
      success (result) {
        const resultSize = (result.size / 1024).toFixed(2)
        const file = new File([result], image.name, { type: image.type })

        resolve({
          file,
          resultSize
        })
      },
      error (err) {
        reject(err)
      }
    })
    console.log(t)
  })
}
