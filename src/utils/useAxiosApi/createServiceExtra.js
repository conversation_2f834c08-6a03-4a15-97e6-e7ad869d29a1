import axios from "axios";
import router from '@/router';
import { useGlobSetting } from '@/hooks/useSetting';
import { isEmpty, getLocalStorage } from "@/utils";
import { useUserStoreWidthOut } from '@/store/user';
import { encryptoData, decryptoData } from '@/utils/decrypto';


export const key = 'BD8r6FMu8tVhPDBo';
export const iv = 'I7cvKphClAJrigOL';

export default function createService(flag) {
  allowMultipleToast();
  const glob = useGlobSetting();
  const instance = axios.create({
    baseURL: glob[flag],
    withCredentials: false,
    timeout: 15000,
    headers: {
      channelId: import.meta.env.VITE_APP_SITE,
      mid: import.meta.env.VITE_APP_SITE,
      device: 2,
      'api-version': '20250418'
    }
  });

  // request interceptor
  instance.interceptors.request.use(
    (config) => {
      // const channelCode = sessionStorage.getItem('channelCode');
      config.headers['subchannelId'] = window.__static_public_data__?.subchannelId;

      const uuid = localStorage.getItem("deviceId");
      config.headers['uuid'] = uuid;

      const sign = localStorage.getItem("sign");
      config.headers['sign'] = sign;

      const userStore = useUserStoreWidthOut();
      config.headers['uid'] = userStore.id;


      if (config.needToken !== false) {
        const token = getLocalStorage(import.meta.env.VITE_TOKEN_KEY);
        if (token) {
          config.headers.token = token;
        }
      }

      if (import.meta.env.MODE === 'release') {
        if (config.method === 'get') {
          if (!isEmpty(config.params)) {
            config.params = { data: encryptoData(config.params, key, iv) }
            // config.params = { data: window.dsBridge.call('app.loadInData', config.params) }
          }
        }

        if (config.method === 'post') {
          if (!isEmpty(config.data)) {
            config.data = { data: encryptoData(config.data, key, iv) }
            // config.data = { data: window.dsBridge.call('app.loadInData', config.data) }
          }
        }

      } else {
        config.headers.justtest = 1
      }
      return config;
    },
    (error) => {
      // do something with request error
      console.log(error); // for debug
      return Promise.reject(error);
    }
  );

  // response interceptor
  instance.interceptors.response.use(
    /**
     * If you want to get http information such as headers or status
     * Please return  response => response
     */

    /**
     * Determine the request status by custom code
     * Here is just an example
     * You can also judge the status by HTTP Status Code
     */
    (response) => {

      const userStore = useUserStoreWidthOut();

      const data = response.data;
      let res


      if (import.meta.env.MODE === 'release') {
        const tmpData = decryptoData(data.data, key, iv)

        res = JSON.parse(tmpData)
        // try {
        //   const tmpData = window.dsBridge.call('app.loadOutData', data.data)
        //   res = JSON.parse(tmpData)
        // } catch (e) {
        //   //
        // }
      } else {
        res = data
      }

      if (res) {
        if (res.code !== 0) {
          if (res.code === 101) {

          } else if (res.code === 103) {
            userStore.logoutHandle();
            router.replace({ path: '/login' });
          } else if (res.code === 10002030) {
            showConfirmDialog({
              title: '系统提示',
              message: '游客暂不开放,是否去进行手机认证',
              confirmButtonText: '前往认证'
            }).then(() => {
              router.push({ path: '/bind_phone' })
            }).catch(() => {
              //
            })
          } else if (res.code === 10002040) {
            showConfirmDialog({
              title: '系统提示',
              message: '请先绑定支付密码',
              confirmButtonText: '前往绑定'
            }).then(() => {
              router.push({ path: '/pay_password_set' })
            }).catch(() => {
              //
            })
          } else {
            showToast(res.msg);
          }
          return Promise.reject(res);
        } else {
          return res;
        }
      }
    },
    (error) => {
      console.log(error);
      const { response, code, message } = error || {}

      const err = error.toString()

      if (code === 'ECONNABORTED' && message.includes('timeout')) {
        showFailToast('接口请求超时，请稍后重试!')
        return Promise.reject(error)
      }

      if (err && err.includes('Network Error')) {
        showToast('网络异常')
        return Promise.reject(error)
      }

      showToast(error.message);
      return Promise.reject(error.message);
    }
  );

  return instance;
}
