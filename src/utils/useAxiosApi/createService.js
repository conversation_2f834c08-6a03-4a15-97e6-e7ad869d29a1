import { getLocalStorage } from "@/utils";
import axios from "axios";
import { useUserStoreWidthOut } from '@/store/user';
import { useGlobSetting } from '@/hooks/useSetting';
import router from '@/router';

export default function createService(url) {

  const glob = useGlobSetting();

  const instance = axios.create({
    baseURL: url || glob.url_base,
    withCredentials: false,
    method: "POST",
    timeout: 15000,
    headers: {
      channelId: import.meta.env.VITE_APP_SITE,
      mid: import.meta.env.VITE_APP_SITE,
      device: 2,
    }
  });
  // request interceptor
  instance.interceptors.request.use(
    (config) => {
      // const channelCode = sessionStorage.getItem('channelCode');
      config.headers['subchannelId'] = window.__static_public_data__?.subchannelId;

      const uuid = localStorage.getItem("deviceId");
      config.headers['uuid'] = uuid;

      const sign = localStorage.getItem("sign");
      if (sign) config.headers['sign'] = sign;

      if (config.needToken !== false) {
        const token = getLocalStorage(import.meta.env.VITE_TOKEN_KEY);
        if (token) {
          config.headers.token = token;
        }
      }
      return config;
    },
    (error) => {
      // do something with request error
      console.log(error); // for debug
      return Promise.reject(error);
    }
  );

  // response interceptor
  instance.interceptors.response.use(
    /**
     * If you want to get http information such as headers or status
     * Please return  response => response
     */

    /**
     * Determine the request status by custom code
     * Here is just an example
     * You can also judge the status by HTTP Status Code
     */
    (response) => {

      const userStore = useUserStoreWidthOut();

      let res = response.data;

      if (res.code !== 200) {
        if (res.code === 6016) {
          userStore.logoutHandle();
        } else {
          showToast(res.msg);
        return Promise.reject(res.msg);
        }

      } else {
        return res;
      }
    },
    (error) => {
      console.log("err" + error);
      const { response, code, message } = error || {}

      const err = error.toString()

      if (code === 'ECONNABORTED' && message.includes('timeout')) {
        showFailToast('接口请求超时，请刷新页面重试!')
        return
      }

      if (err && err.includes('Network Error')) {
        showToast('网络异常')
        return Promise.reject(error)
      }

      showToast(error.message);
      return Promise.reject(error.message);
    }
  );

  return instance;
}
