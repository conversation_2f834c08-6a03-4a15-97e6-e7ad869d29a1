/**
 * Mobile Virtual Keyboard Detection Utility
 * 
 * This utility provides multiple methods to detect when the virtual keyboard
 * opens/closes on mobile devices to prevent layout issues.
 */

export class KeyboardDetector {
  constructor(options = {}) {
    this.options = {
      threshold: 150, // Minimum height change to consider keyboard open
      debounceDelay: 100, // Debounce delay for resize events
      ...options
    };

    this.isKeyboardOpen = false;
    this.initialViewportHeight = window.innerHeight;
    this.initialVisualViewportHeight = window.visualViewport?.height || window.innerHeight;
    this.callbacks = {
      onKeyboardOpen: [],
      onKeyboardClose: [],
      onKeyboardToggle: []
    };

    this.debounceTimer = null;
    this.isInitialized = false;

    // Bind methods to preserve context
    this.handleResize = this.handleResize.bind(this);
    this.handleVisualViewportChange = this.handleVisualViewportChange.bind(this);
    this.handleFocusIn = this.handleFocusIn.bind(this);
    this.handleFocusOut = this.handleFocusOut.bind(this);
  }

  /**
   * Initialize keyboard detection
   */
  init() {
    if (this.isInitialized) return;

    // Store initial viewport dimensions
    this.initialViewportHeight = window.innerHeight;
    this.initialVisualViewportHeight = window.visualViewport?.height || window.innerHeight;

    // Method 1: Visual Viewport API (most reliable for modern browsers)
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', this.handleVisualViewportChange);
    }

    // Method 2: Window resize events (fallback)
    window.addEventListener('resize', this.handleResize);

    // Method 3: Focus events on input elements
    document.addEventListener('focusin', this.handleFocusIn);
    document.addEventListener('focusout', this.handleFocusOut);

    this.isInitialized = true;
  }

  /**
   * Cleanup event listeners
   */
  destroy() {
    if (!this.isInitialized) return;

    if (window.visualViewport) {
      window.visualViewport.removeEventListener('resize', this.handleVisualViewportChange);
    }

    window.removeEventListener('resize', this.handleResize);
    document.removeEventListener('focusin', this.handleFocusIn);
    document.removeEventListener('focusout', this.handleFocusOut);

    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.isInitialized = false;
  }

  /**
   * Handle Visual Viewport API changes (most accurate)
   */
  handleVisualViewportChange() {
    if (!window.visualViewport) return;

    const currentHeight = window.visualViewport.height;
    const heightDifference = this.initialVisualViewportHeight - currentHeight;
    
    this.updateKeyboardState(heightDifference > this.options.threshold);
  }

  /**
   * Handle window resize events (fallback method)
   */
  handleResize() {
    // Debounce resize events
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = setTimeout(() => {
      // Skip if Visual Viewport API is available (it's more accurate)
      if (window.visualViewport) return;

      const currentHeight = window.innerHeight;
      const heightDifference = this.initialViewportHeight - currentHeight;
      
      this.updateKeyboardState(heightDifference > this.options.threshold);
    }, this.options.debounceDelay);
  }

  /**
   * Handle focus events on input elements
   */
  handleFocusIn(event) {
    const target = event.target;
    
    // Check if focused element is an input that would trigger virtual keyboard
    if (this.isInputElement(target)) {
      // Delay check to allow keyboard animation to complete
      setTimeout(() => {
        this.checkKeyboardState();
      }, 300);
    }
  }

  /**
   * Handle focus out events
   */
  handleFocusOut(event) {
    const target = event.target;
    
    if (this.isInputElement(target)) {
      // Delay check to allow keyboard animation to complete
      setTimeout(() => {
        this.checkKeyboardState();
      }, 300);
    }
  }

  /**
   * Check if element is an input that triggers virtual keyboard
   */
  isInputElement(element) {
    if (!element) return false;

    const tagName = element.tagName.toLowerCase();
    const inputTypes = ['text', 'email', 'password', 'search', 'tel', 'url', 'number'];
    
    return (
      tagName === 'input' && inputTypes.includes(element.type) ||
      tagName === 'textarea' ||
      element.contentEditable === 'true'
    );
  }

  /**
   * Force check current keyboard state
   */
  checkKeyboardState() {
    let heightDifference = 0;

    if (window.visualViewport) {
      heightDifference = this.initialVisualViewportHeight - window.visualViewport.height;
    } else {
      heightDifference = this.initialViewportHeight - window.innerHeight;
    }

    this.updateKeyboardState(heightDifference > this.options.threshold);
  }

  /**
   * Update keyboard state and trigger callbacks
   */
  updateKeyboardState(isOpen) {
    const wasOpen = this.isKeyboardOpen;
    this.isKeyboardOpen = isOpen;

    // Only trigger callbacks if state actually changed
    if (wasOpen !== isOpen) {
      this.triggerCallbacks('onKeyboardToggle', isOpen);
      
      if (isOpen) {
        this.triggerCallbacks('onKeyboardOpen');
      } else {
        this.triggerCallbacks('onKeyboardClose');
      }
    }
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (this.callbacks[event]) {
      this.callbacks[event].push(callback);
    }
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (this.callbacks[event]) {
      const index = this.callbacks[event].indexOf(callback);
      if (index > -1) {
        this.callbacks[event].splice(index, 1);
      }
    }
  }

  /**
   * Trigger callbacks for specific event
   */
  triggerCallbacks(event, ...args) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => {
        try {
          callback(...args);
        } catch (error) {
          console.error('Error in keyboard detector callback:', error);
        }
      });
    }
  }

  /**
   * Get current keyboard state
   */
  getKeyboardState() {
    return {
      isOpen: this.isKeyboardOpen,
      initialHeight: this.initialViewportHeight,
      currentHeight: window.visualViewport?.height || window.innerHeight,
      heightDifference: this.initialViewportHeight - (window.visualViewport?.height || window.innerHeight)
    };
  }
}

/**
 * Create a singleton instance for global use
 */
let globalKeyboardDetector = null;

export function useKeyboardDetector(options = {}) {
  if (!globalKeyboardDetector) {
    globalKeyboardDetector = new KeyboardDetector(options);
    globalKeyboardDetector.init();
  }
  
  return globalKeyboardDetector;
}

/**
 * Cleanup global instance
 */
export function destroyGlobalKeyboardDetector() {
  if (globalKeyboardDetector) {
    globalKeyboardDetector.destroy();
    globalKeyboardDetector = null;
  }
}
