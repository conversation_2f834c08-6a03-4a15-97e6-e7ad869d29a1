export function isOfficialLottery (id)  {
  return [10, 11, 12].indexOf(Number(id)) > -1
};

export const reducer = (accumulator, currentValue) => accumulator + currentValue;

// 双倍投注
export const doubleSelect = (preselected, multiple = 2) => {
  preselected.forEach(o => {
    o.bet_data.forEach(b => {
      b.point = b.point * multiple
    })
  })
};

// 多次投注，合并投注项
export const mergeSelectes = (selected, preselected) => {
  preselected.forEach((p, i) => {
    const index = selected.findIndex(s => s.how_id === p.how_id)
    if (index > -1) {
      p.bet_data.forEach((pc, t) => {
        const tmp = selected[index].bet_data
        const idx = tmp.findIndex(o => o.type_id === pc.type_id)
        if (idx > -1) {
          tmp[idx].point += pc.point
        } else {
          tmp.push(p.bet_data[t])
        }
      })
    } else {
      selected.push(preselected[i])
    }
  })
};

export const getLotteryColor = (number) => {
  const redNumbers = [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46];
  const blueNumbers = [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48];
  // const greenNumbers = [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49];
  
  const num = parseInt(number);
  
  if (redNumbers.includes(num)) {
    return 'text-red';
  } else if (blueNumbers.includes(num)) {
    return 'text-blue';
  } else {
    return 'text-green'; // 默认样式
  }
};

export const getRouletteColor = (number) => {
  const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];
  const num = parseInt(number);
  
  if (num === 0) {
    return 'text-green';
  } else if (redNumbers.includes(num)) {
    return 'text-red';
  } else {
    return 'text-blue';
  }
};