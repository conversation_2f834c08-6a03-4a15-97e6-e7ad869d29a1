<template>
  <SecondPageLayout>
    <div class="grid grid-cols-2 gap-4 mt-4 mx-5">
      <div v-for="val in grops" :key="val.id" class="flex flex-col items-center justify-center">
        <a :href="val.groupUrl" target="_blank" className='w-24 h-24 overflow-hidden rounded-lg'>
          <ImgComponents :imgUrl="val.logo" />
        </a>
        <p class="text-lg mt-2">{{ val.groupType }}</p>
      </div>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useAppStore } from '@/store/app';
const appStore = useAppStore();

const grops = computed(() => appStore.appConfig?.groupInfo);
</script>
