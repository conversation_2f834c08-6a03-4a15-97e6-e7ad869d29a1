<template>
  <div class="flex items-center flex-wrap rounded bg-[var(--van-black-500)]">
    <div
      v-for="item in options"
      :key="item.key"
      class="w-1/4 flex flex-col items-center justify-center my-2 relative"
      @click="navigateHandle(item)"
    >
      <div class="mb-2.5">
        <img :src="item.icon" class="h-5" />
      </div>
      <p class="text-white text-xs">{{ item.title }}</p>
    </div>
  </div>
</template>

<script setup>
import business from '@/assets/webp/business.webp';
import gamerecored from '@/assets/webp/gamerecored.webp';
import record from '@/assets/webp/record.webp';
import message from '@/assets/webp/message.webp';
import focus from '@/assets/webp/focus.webp';
import expendrecord from '@/assets/webp/expendrecord.webp';
import share from '@/assets/webp/share.webp';
import group from '@/assets/webp/group.webp';
import service from '@/assets/webp/service.webp';
import { useService } from '@/hooks';

const router = useRouter();

const options = [
  { title: '在线客服', icon: service, key: 1 },
  { title: '游戏记录', icon: gamerecored, key: 2 },
  { title: '我的账单', icon: record, key: 3 },
  { title: '我的信息', icon: message, key: 4 },
  { title: '我的收藏', icon: focus, key: 5 },
  { title: '观影记录', icon: expendrecord, key: 6 },
  { title: '邀请好友', icon: share, key: 7 },
  { title: '商务合作', icon: business, key: 8 },
  // { title: '官方群组', icon: group, key: 8 }
  // Remove for request from Lance (2025-06-24)
];

const navigateHandle = ({ key }) => {
  switch (key) {
    case 1:
      useService();
      return;
    case 2:
      router.push({ path: '/game_record' });
      return;
    case 3:
      router.push({ path: '/amount_record' });
      return;
    case 5:
      router.push({ path: '/focus_record' });
      return;
    case 6:
      router.push({ path: '/expenses_record' });
      return;
    case 7:
      router.push({ path: '/invite' });
      return;
    case 8:
      router.push({ path: '/business' });
      return;
    default:
      showToast('敬请期待');
      return;
  }
};
</script>
