<template>
  <div
    class="flex flex-col w-full h-[177px] mx-auto mt-3 box-border rounded-md border border-solid border-[var(--van-primary-color)] bg-no-repeat bg-[url(@/assets/webp/header.webp)]"
    style="background-size: 100% 100%"
  >
    <div class="flex items-center h-[78px] px-3">
      <div class="relative" @click="avatarHandle">
        <van-image round width="3.25rem" height="3.25rem" :src="`/static/webp/avatar${picUrl}.webp`" style="display: block" />
        <img src="@/assets/icons/camera-icon.png" class="absolute bottom-0 right-0 h-3" />
      </div>
      <div class="pl-3">
        <div class="flex items-center">
          <h2 class="text-base">{{ nickName }}</h2>
        </div>
        <div class="text-xs text-[#999]">
          <template v-if="vipValidTime > 0">无限观影截止{{ formatter(vipValidTime, 'YYYY-MM-DD') }}</template>
          <template v-else-if="vipLevel > 0">每日观影:+∞</template>
          <template v-else
            >每日观影:视频&nbsp;<span class="text-[var(--van-primary-color)]">{{ longVideoFreeWatchLeft }}</span> / 抖阴&nbsp;<span
              class="text-[var(--van-primary-color)]"
              >{{ shortVideoFreeWatchLeft }}</span
            ></template
          >
        </div>
        <div v-if="vipIcon" class="flex items-center justify-center w-12 h-22px rounded-md">
          <ImgComponents :imgUrl="vipIcon" />
        </div>
      </div>
      <div class="flex flex-col items-center justify-center ml-auto" @click="navigatorHandle('/setting')">
        <img src="@/assets/icons/setting.png" class="h-6" />
        <p class="text-xs mt-1">设置</p>
      </div>
    </div>

    <div class="flex flex-col flex-1">
      <div class="flex items-center justify-between px-3">
        <div class="flex items-center" @click="onRefresh">
          <img src="@/assets/icons/balance.png" class="w-19px h-19px" />
          <span class="mx-2.5 text-[15px]">{{ wallet?.points }}</span>
          <img src="@/assets/webp/refresh.webp" alt="" :class="['h-4', { 'animate-spin': loading }]" />
        </div>
        <div class="flex items-center text-xs">
          <div
            role="button"
            class="flex items-center justify-center w-[45px] h-19px rounded bg-[var(--van-primary-color)] text-[var(--van-black)]"
            @click="navigatorHandle('/cz')"
          >
            <span>充值</span>
          </div>
          <div
            role="button"
            class="flex items-center justify-center w-[45px] h-19px rounded bg-[var(--van-primary-color)] text-[var(--van-black)] ml-1"
            @click="navigatorWithdraw"
          >
            <span>提现</span>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-around pt-3 flex-1">
        <div role="button" class="flex flex-col items-center justify-center">
          <img src="@/assets/webp/wallet.webp" class="h-5" @click="normalHandle" />
          <p class="text-xs mt-1">Vi钱包</p>
        </div>
        <div role="button" class="flex flex-col items-center justify-center" @click="navigatorHandle('/vip')">
          <img src="@/assets/webp/vip.webp" class="h-5" />
          <p class="text-xs mt-1">VIP</p>
        </div>
        <div role="button" class="flex flex-col items-center justify-center" @click="navigatorHandle('/hd')">
          <img src="@/assets/webp/activity.webp" class="h-5" />
          <p class="text-xs mt-1">活动中心</p>
        </div>
        <div role="button" class="flex flex-col items-center justify-center" @click="navigatorHandle('/promotion')">
          <img src="@/assets/webp/prop.webp" class="h-5" />
          <p class="text-xs mt-1">全民代理</p>
        </div>
      </div>
    </div>
  </div>

  <AvatarPopup v-model="avatarVisible"></AvatarPopup>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/store/user';
import { useAppStore } from '@/store/app';
import AvatarPopup from '@/components/AvatarPopup/index.vue';
import { formatter } from '@/utils';

const router = useRouter();

const userStore = useUserStore();
const appStore = useAppStore();
const { nickName, id, wallet, picUrl, type, vipIcon, vipLevel, vipValidTime, longVideoFreeWatchLeft, shortVideoFreeWatchLeft } =
  storeToRefs(userStore);

const loading = ref(false);

const avatarVisible = ref(false);

const avatarHandle = () => {
  avatarVisible.value = true;
};

const navigatorHandle = (path) => {
  router.push({ path });
};

const navigatorWithdraw = () => {
  if (type.value !== 0) {
    router.push({ path: '/withdraw' });
  } else {
    appStore.SetHasNeedPhoneAction(true);
  }
};

const onRefresh = async () => {
  loading.value = true;
  try {
    await userStore.updateUserWalletData();
  } catch {
  } finally {
    loading.value = false;
  }
};

const normalHandle = () => {
  showToast('敬请期待');
};
</script>
