<template>
  <PageLayout>
    <div class="w-345px mx-auto relative z-10 h-full overflow-y-auto">
      <ProfileHeader></ProfileHeader>
      <div v-if="notice" class="flex flex-col my-2.5">
        <Notice :list="notice"></Notice>
      </div>
      <div class="my-2.5 h-90px" v-if="advList && advList.length" >
        <Banner :options="advList" />
      </div>
      <ProfileMiddle></ProfileMiddle>
      <ProfileBottom></ProfileBottom>
    </div>
  </PageLayout>
</template>
<script setup>
import { useUserStore } from "@/store/user";
import { useAppStore } from "@/store/app";
import Banner from "@/components/banner/banner.vue";
import Notice from "@/components/notice/notice.vue";
import ProfileHeader from './components/header.vue';
import ProfileMiddle from './components/profile_middle.vue';
import ProfileBottom from './components/profile_bottom.vue';

defineOptions({
  name: 'Profile'
})

const userStore = useUserStore();
const appStore = useAppStore();

const advList = computed(() => appStore.adItem(4));
const notice = computed(() => appStore.adItem(15));


onActivated(() => {
  userStore.updatePersonData();
  userStore.updateUserWalletData();
})
</script>