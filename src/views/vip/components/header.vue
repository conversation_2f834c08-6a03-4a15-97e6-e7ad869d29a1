<template>
  <div class="flex-none relative flex items-center w-317px h-92px mx-auto bg-cover bg-[url(@/assets/image/bg_vip.png)]">
    <div class="flex flex-col items-center px-4">
      <div v-if="currentLevel.icon" className='w-38px overflow-hidden'>
        <ImgComponents :imgUrl="currentLevel.icon" />
      </div>
      <p class="text-sm text-[#361608]">{{ currentLevel.name || '--' }}</p>
      <p class="text-9px text-[#333333]">当前等级</p>
    </div>
    <div class="flex flex-col flex-1 items-center">
      <p class="text-sm text-[#361608] font-bold font-stretch-normal">升级还需{{ diff }}充值</p>
      <div class="w-full mt-2" style="--van-progress-height: 9px;--van-progress-background: #8e5a49;--van-progress-color: white;">
        <van-progress :percentage="percentage" :show-pivot="false" />
      </div>
    </div>
    <div class="flex flex-col items-center px-4">
      <div v-if="nextLevel.icon" className='w-38px overflow-hidden'>
        <ImgComponents :imgUrl="nextLevel.icon" />
      </div>
      <p class="text-sm text-[#361608]">{{ nextLevel.name || '--' }}</p>
      <p class="text-9px text-[#333333]">{{ max ? '最高等级' : '下一等级' }}</p>
    </div>

    <div class="vip-execed" @click="navigatorHandle"><span>详情</span></div>

  </div>
</template>

<script setup>

const props = defineProps({
  max: Boolean,
  top_up: {
    type: Number,
    default: 0
  },
  currentLevel: {
    type: Object,
    default: () => ({})
  },
  nextLevel: {
    type: Object,
    default: () => ({})
  }
});

const router = useRouter();

const percentage = computed(() => {
  if (props.nextLevel && props.nextLevel.top_up) {
    return props.top_up / props.nextLevel.top_up * 100
  } else {
    return 0;
  }
})

const diff = computed(() => {
  if (props.nextLevel && props.nextLevel.top_up) {
    return props.nextLevel.top_up - props.top_up
  } else {
    return 0;
  }
})

const navigatorHandle = () => {
  router.push({ path: '/vip_detail' })
}

</script>

<style lang="less" scoped>
.vip-execed {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 49px;
  height: 18px;
  transform: translate(-50%);
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fae3cc;
  border-radius: 5px 5px 0 0;
  color: #361608;
  border: 1px solid white;
}
</style>