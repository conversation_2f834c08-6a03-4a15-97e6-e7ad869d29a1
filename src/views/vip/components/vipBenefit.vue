<template>
  <div class="flex-none w-345px mx-auto -mt-5 rounded-lg bg-[var(--van-black-500)]">
    <div class="flex items-center justify-center py-3">
      <img src="@/assets/image/vip_title_l_icons.png" class="w-56px mr-3">
      <span class="text-[var(--van-primary-color)]">{{ title }}</span>
      <img src="@/assets/image/vip_title_r_icons.png" class="w-56px ml-3">
    </div>
    <p class="text-xs text-[var(--van-primary-color)] mx-3">周奖励每周一【00:10】进行更新，每个自然周可领取一次</p>

    <ul class="mx-3">
      <li class="flex items-center justify-between p-4 mt-4 rounded-lg cell-item">
        <div class="text-sm">晋级彩金奖励</div>
        <div class="ml-auto mr-5 text-xs">{{ state?.reach_award_amount }}彩金</div>
        <van-button v-if="state.claim_bonus_award === 0" round size="small" type="primary" disabled>未开通</van-button>
        <van-button v-else-if="state.claim_bonus_award === 1" round size="small" type="primary" @click="awardHandler(1)">待领取</van-button>
        <van-button v-else round size="small" type="primary" disabled>已领取</van-button>
      </li>
      <li class="flex items-center justify-between p-4 mt-4 rounded-lg cell-item">
        <div class="text-sm">长视频次数奖励</div>
        <div class="ml-auto mr-5 text-xs">{{ state?.video_award_Num }}次</div>
        <van-button v-if="state.claim_video_award === 0" round size="small" type="primary" disabled>未开通</van-button>
        <van-button v-else-if="state.claim_video_award === 1" round size="small" type="primary" @click="awardHandler(2)">待领取</van-button>
        <van-button v-else round size="small" type="primary" disabled>已领取</van-button>
      </li>
      <li class="flex items-center justify-between p-4 mt-4 rounded-lg cell-item">
        <div class="text-sm">短视频次数奖励</div>
        <div class="ml-auto mr-5 text-xs">{{ state?.short_video_award_num }}次</div>
        <van-button v-if="state.claim_short_video_award === 0" round size="small" type="primary" disabled>未开通</van-button>
        <van-button v-else-if="state.claim_short_video_award === 1" round size="small" type="primary" @click="awardHandler(3)">待领取</van-button>
        <van-button v-else round size="small" type="primary" disabled>已领取</van-button>
      </li>
    </ul>

    <ul class="flex flex-wrap py-4">
      <li class="w-1/4 flex flex-col items-center justify-center mb-2.5">
        <div class="w-50px h-50px">
          <img :src="state?.level > 0 ? adFree : ad" alt="">
        </div>
        <p class="text-xs mt-2.5">广告特权</p>
        <p class="text-center text-[#9eafc7] text-10px">无广告观影</p>
      </li>
    </ul>

  </div>
</template>

<script setup>
import { RewardVipAPI } from '@/api/user';

import adFree from '@/assets/icons/ad-free.png';
import ad from '@/assets/icons/ad.png';

const title = import.meta.env.VITE_APP_TITLE;


const props = defineProps({
  state: {
    type: Object,
    default: () => ({})
  },
})

const emit = defineEmits(['refresh'])

const awardHandler = async (award_type) => {
  const toast = showLoadingToast({ duration: 0, forbidClick: true });
  try {
    const res = await RewardVipAPI({ award_type });
    emit('refresh');
  } catch (error) {
    //
  } finally {
    toast.close()
  }
}

</script>


<style lang="less" scoped>

.cell-item {
  background-color: #655550;
  border-radius: 10px;
  border: solid 1.5px #93796b;
}
</style>