<template>
  <div
    class="flex-none flex items-center justify-center h-180px perspective-midrange bg-no-repeat bg-contain bg-bottom bg-[url(@/assets/image/bg_vip_swiper.png)]"
  >
    <Swiper
      :slides-per-view="5"
      :initial-slide="swiperIndex"
      :centeredSlides="true"
      :freeMode="true"
      :spaceBetween="0"
      :grabCursor="true"
      class="w-full h-full"
      @swiper="onSwiper"
      @slideChange="updateRotation"
      @transitionEnd="onTransitionEnd"
    >
      <SwiperSlide v-for="val in list" class="will-change-transform">
        <div class="flex flex-col items-center slide-content">
          <div className="w-15 h-15 overflow-hidden">
            <ImgComponents :imgUrl="val.logo_icon" objectFit="contain" />
          </div>
          <p class="text-sm mt-1">{{ val.name }}</p>
        </div>
      </SwiperSlide>
    </Swiper>
  </div>
</template>

<script setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';

const props = defineProps({
  list: {
    type: Array,
    default: []
  },
  index: Number,
  callBackHandle: Function,
  endHandle: Function
});

const swiperRef = ref(null);

const swiperIndex = ref(0);

const updateRotation = (swiper) => {
  if (swiper.activeIndex === swiper.slides.length - 1) {
    props.endHandle?.(true);
  } else {
    props.endHandle?.(false);
  }

  swiperIndex.value = swiper.activeIndex;
  const totalSlides = swiper.slides.length;
  const centerIndex = Math.floor(swiper.activeIndex);
  swiper.slides.forEach((slide, index) => {
    const offset = index - centerIndex; // 计算相对中心滑块的偏移量
    const angle = offset * 25; // 让滑块形成弧形角度
    const scale = offset === 0 ? 1 : 1 - Math.abs(offset) * 0.15; // 中间不缩放，两侧递减
    const opacity = offset === 0 ? 1 : 1 - Math.abs(offset) * 0.3;
    const translateX = offset * 50; // 控制滑块左右偏移
    const translateZ = 250 - Math.abs(offset) * 50; // 让远处的滑块更远，增强 3D 效果

    slide.style.transform = `translateY(${Math.abs(offset) * 20 + 10}px) scale(${scale})`;
    slide.style.transition = 'transform 0.2s ease-out';
    slide.style.opacity = opacity;
  });
};

const onSwiper = (swiper) => {
  swiperRef.value = swiper;
};

const onTransitionEnd = (swiper) => {
  props.callBackHandle?.(swiper.activeIndex);
};

watch(
  () => props.list,
  () => {
    if (swiperRef.value) {
      nextTick(() => {
        swiperRef.value.update();
        updateRotation(swiperRef.value);
      });
    }
  }
);

watch(
  () => props.index,
  (val, be) => {
    if (val !== swiperIndex.value) {
      swiperIndex.value = val;
      swiperRef.value.slideTo?.(val);
    }
  }
);
</script>
