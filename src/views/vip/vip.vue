<template>
  <SecondPageLayout @click-right="onClickRight" :clickLeftHandle="clickLeftHandle">
    <template #navbar-right>
      <span class="text-xs text-[#029dff]">领取记录</span>
    </template>

    <VipHeader
      :top_up="top_up"
      :currentLevel="searchLevel(vip_level)"
      :nextLevel="nextLevel"
      :max="max"
    />

    <SwiperComponents :list="swiperList" :index="swiperIndex" :callBackHandle="callBackHandle" :endHandle="endHandle"></SwiperComponents>

    <VipBenefit :state="selectSlide" @refresh="getVipList"></VipBenefit>

    <div
      class="fixed right-0 top-40 z-50 w-60px h-30px flex items-center justify-center bg-cover bg-[url(@/assets/image/cz-bg.png)]"
      @click="navigatorHandle"
    >
      <span class="text-sm">充值</span>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { VIPListAPI } from '@/api/user';
import VipHeader from './components/header.vue';
import SwiperComponents from './components/swiper.component.vue';
import VipBenefit from './components/vipBenefit.vue';
import { PageEnum } from '@/enums/pageEnum';

const router = useRouter();
const { query: { redirect } } = useRoute();

const max = ref(false);
const top_up = ref(0);
const vip_level = ref(0);
const swiperIndex = ref(0);
const swiperList = ref([]);

const clickLeftHandle = () => {
  if (redirect) {
    router.replace({ path: PageEnum.BASE_HOME })
  } else {
    history.back();
  }
}

const onClickRight = () => {
  router.push({ path: '/vip_record' });
};

const searchLevel = computed(() => (level) => {
  return swiperList.value.find((o) => o.level === level);
});

const selectSlide = computed(() => {
  return swiperList.value[swiperIndex.value];
});


const nextLevel = computed(() => {
  if (selectSlide.value?.level <= vip_level.value) {
    return swiperList.value.find((o) => o.level === vip_level.value + 1);
  } else {
    const tmp = swiperList.value.find((o) => o.level === selectSlide.value.level);
    const last = swiperList.value[swiperList.value.length - 1];
    return tmp ? tmp : last;
  }
})

const callBackHandle = (val) => {
  swiperIndex.value = val;
};

const endHandle = (val) => {
  max.value = val;
};

const navigatorHandle = () => {
  router.push({ path: '/cz' });
};

const getVipList = async () => {
  try {
    const res = await VIPListAPI();
    top_up.value = res.data.user_top_up;
    swiperList.value = res.data.vip_list;
    vip_level.value = res.data.user_vip_level;
    swiperIndex.value = res.data.vip_list.findIndex((o) => o.level === res.data.user_vip_level) || 1;
  } catch (error) {
    console.log(error, 'error');
  }
};

onMounted(() => {
  getVipList();
});
</script>
