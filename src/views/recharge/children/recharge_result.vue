<template>
  <SecondPageLayout>
    <div class="flex flex-col items-center w-11/12 bg-[var(--van-black-500)] rounded box-border mt-8 mx-auto h-[378px] relative text-sm">
      <div class="h-[60px] overflow-hidden] mt-10 mb-8"><img src="@/assets/image/check.png" class="h-full"></div>
      <p class="text-[var(--van-primary-color)]">财务正在核对</p>
      <p class="text-white mt-3 mb-12">3-5分钟未到账，请联系客服</p>
      <van-button type="primary" round class="w-[125px]" @click="clickHandler">返 回</van-button>
      <p class="mt-5" @click="useService">联系客服</p>
    </div>
  </SecondPageLayout>
</template>

<script setup>

import { useService } from "@/hooks";

const router = useRouter();
const clickHandler = () => {
  history.back();
}
</script>