<script setup>
import { storeToRefs } from 'pinia'
import useClipboard from 'vue-clipboard3'
import Img from '@/assets/icons/bank.png'
import { CreayeQuickPayment } from '@/api/finance'
import { uActions } from '@/api/user'
import { formatBankCardNumber } from '@/utils'

const { query } = useRoute()
const router = useRouter()

const { toClipboard } = useClipboard()

function copyHandler(value) {
  toClipboard(value)
  showToast('复制成功')
}

function cancelHandler() {
  router.replace({ path: '/cz' })
}

function clickHandler() {
  const toast = showLoadingToast({ duration: 0 })
  CreayeQuickPayment({
    amount: Number(query.amount),
    account: query.account,
    bank_id: Number(query.bank_id),
    source: query.source ?? '',
  })
    .then(async (res) => {
      router.replace({ path: '/recharge_result' })

      await uActions({ actionType: 7, eventId: Number.parseInt(query.recharge_type_id) })
    })
    .finally(() => {
      toast.close()
    })
}
</script>

<template>
  <SecondPageLayout>
    <div class="relative mx-auto mt-8 box-border w-11/12 flex flex-col rounded bg-[var(--van-black-500)] px-4">
      <div class="absolute inset-x-2/4 top-[-20px] ml-[-20px] h-10 w-10">
        <img :src="Img" alt="">
      </div>

      <div class="pt-12 text-center text-sm text-[var(--van-primary-color)]">
        <span class="text-4xl">{{ query.amount }}</span>元
      </div>
      <p class="text-center text-xs text-[var(--van-red)]">
        （请确认订单金额是否正确）
      </p>
      <ul class="my-4">
        <li class="h-10 flex items-center text-sm text-white">
          <label>收款姓名</label>
          <div class="ml-4 flex flex-1 items-center" @click="copyHandler(query.realname)">
            <span class="mr-2">{{ query.realname }}</span>
            <SvgIcon name="copy" color="#ffd631" style="font-size: 14px;" />
          </div>
        </li>
        <li class="h-10 flex items-center text-sm text-white">
          <label>银行卡号</label>
          <div class="ml-4 flex flex-1 items-center" @click="copyHandler(query.bank_card)">
            <span class="mr-2">{{ formatBankCardNumber(query.bank_card) }}</span>
            <SvgIcon name="copy" color="#ffd631" style="font-size: 14px;" />
          </div>
        </li>
        <li class="h-10 flex items-center text-sm text-white">
          <label>开户银行</label>
          <div class="ml-4 flex flex-1 items-center" @click="copyHandler(query.bank_name)">
            <span class="mr-2">{{ query.bank_name }}</span>
            <SvgIcon name="copy" color="#ffd631" style="font-size: 14px;" />
          </div>
        </li>
        <li class="h-10 flex items-center text-sm text-white">
          <label>开户支行</label>
          <div class="ml-4 flex flex-1 items-center" @click="copyHandler(query.bank_address)">
            <span class="mr-2">{{ query.bank_address }}</span>
            <SvgIcon name="copy" color="#ffd631" style="font-size: 14px;" />
          </div>
        </li>
      </ul>

      <div class="h-10 flex items-center justify-center rounded-md bg-[#262626] text-xs">
        <span>转账成功后，请及时点击我已转账，否则无法到账</span>
      </div>

      <div class="my-[50px] flex items-center justify-around">
        <van-button plain type="primary" round class="w-[125px]" @click="cancelHandler">
          取消存款
        </van-button>
        <van-button type="primary" round class="w-[125px]" @click="clickHandler">
          我已转账
        </van-button>
      </div>
    </div>
  </SecondPageLayout>
</template>
