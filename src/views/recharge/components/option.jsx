import selectIcon from "@/assets/icons/balance.png";
import { useService } from "@/hooks";

export default defineComponent({
  name: "RechargeOption",
  props: {
    pay_type: Number,
    recharge_type_id: Number,
    description: String,
    list: {
      type: Array,
      default: () => [],
    },
  },
  setup(props, { emit, expose }) {
    const state = reactive({
      amount: null,
      price_id: null,
      type_id: null,
      account: '',
    });

    const [visible, toggle] = useToggle(true);

    const payIndex = ref(0);
    const single = computed(() => props.list.length === 1);
    const current = computed(() => props.list[payIndex.value]);
    


    const navigatorHandler = () => {

    }

    const clickHandler = ({ price, id }) => {
      state.amount = price;
      state.price_id = id;
      state.type_id = current.value?.type_id;

      emit("select", {
        amount: price,
        price_id: id,
        type_id: state.type_id
      });
    };


    const customizeHandler = (e) => {

      const [min, max] = current.value?.amount_area || [];

      if (!/^[1-9]\d*$/.test(state.amount)) {
        showToast('请输入正确金额')
        return
      }

      if (+state.amount < min || +state.amount > max) {
        showToast(`请输入${min}-${max}区间金额`)
        return
      }


      emit("select", {
        amount: +state.amount,
        type_id: state.type_id
      });
    }

    const handleSubmit = () => {
      if (!state.account) {
        showToast('请输入姓名')
        return
      }

      if (!state.amount) {
        showToast('请输入金额')
        return
      }
      emit("select", {
        amount: state.amount,
        account: state.account,
        bank_id: current.value.id,
        tp: current.value
      });

    }

    const reset = () => {
      state.amount = null;
      state.price_id = null;
      state.type_id = null;
      state.account = "";
    }

    watch(
      () => props.recharge_type_id,
      () => {
        reset();
      }
    )

    expose({
      reset
    })

    const renderSelect = (list) => {
      return (
        <div className="flex flex-col">
          <div className="flex items-center h-8 relative tp-line pl-3 my-3">
            <p className="text-[15px]">请选择充值金额</p>
          </div>

          {current.value?.allow_sj_amount === 1 && <renderInput></renderInput>}

          <div className="grid grid-cols-3 gap-4 py-3">
            {list &&
              list.map((val) => (
                <div
                  className="flex flex-col h-[70px] overflow-hidden rounded-xl"
                  onClick={() => clickHandler(val)}
                >
                  <div
                    class={[
                      'flex items-center justify-center flex-1 text-sm relative',
                      val.id === state.price_id ? 'bg-[var(--van-primary-color)] text-[#333]' : 'bg-[#353535] text-white'
                    ]}
                  >
                    <img src={selectIcon} className="w-3 h-3 absolute left-2" />
                    {val.real_amt}
                  </div>
                  <div className="flex items-center justify-center flex-1 text-xs text-white bg-[var(--van-black-500)]">
                    价格:{val.price}
                  </div>
                </div>
              ))}
          </div>
        </div>
      );
    };

    const renderInput = () => {
      const [min, max] = current.value?.amount_area || [];

      return (
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-full px-2">
          <span className="text-sm text-white mr-2">￥</span>
          <div className="flex-1 text-white text-sm">
            <input
              v-model={state.amount}
              type="number"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder={`${min}-${max}`}
            />
          </div>
          <div role="button">
            <van-button type="primary" disabled={!state.amount} round size="small" onClick={customizeHandler}>
              确认充值
            </van-button>
          </div>
        </div>
      );
    };

    const renderActions = () => {
      return (
        <div className="flex flex-col">
          <div className="flex items-center h-8 relative tp-line pl-3 my-3">
            <p className="text-[15px]">渠道选择</p>
          </div>
          <div className="grid grid-cols-3 gap-x-4 gap-y-2.5">
            {props.list &&
              props.list.map((val, i) => (
                <div
                  onClick={() => (payIndex.value = i)}
                  class={[
                    "h-9 flex items-center justify-center text-xs rounded-lg",
                    i === payIndex.value
                      ? "bg-gradient-orange text-[var(--van-black-500)]"
                      : "bg-[var(--van-black-500)] text-white",
                  ]}
                >
                  <span>{props.pay_type === 2 ? val.realname : val.name}</span>
                </div>
              ))}
          </div>
        </div>
      );
    };

    const renderDesc = () => {

      const _html = props.pay_type === 2 ? props.description : (current.value?.deschtml || current.value?.desc)

      return (
        <div className="flex flex-col">
          <div className="flex items-center justify-between h-8 relative tp-line pl-3 my-3">
            <p className="text-[15px]">{current.value?.name}介绍</p>
            <div
              role="button"
              className="flex items-center justify-center text-xs w-14 h-6 text-[var(--van-black-500)] rounded-xl bg-[var(--van-primary-color)]"
              onClick={() => toggle()}
            >
              <span style="font-size: 11px;">{visible.value ? "隐藏" : "显示"}</span>

              <van-icon name={visible.value ? "arrow-up" : "arrow-down"} size="10px" class="ml-1" />
            </div>
          </div>
          <div
            v-html={_html}
            class={["overflow-hidden whitespace-pre-wrap", visible.value ? "h-auto" : "h-0"]}
          ></div>
        </div>
      );
    };

    const renderOption = () => {
      return (
        <div className="flex flex-col">
          {renderDesc()}
          {!single.value && renderActions()}
          {renderSelect(current.value?.price_arr)}
        </div>
      );
    };

    const renderBankContent = () => {
      return (
        <div className="flex flex-col">
          <div className="flex items-center h-8 relative tp-line pl-3 my-3">
            <p className="text-[15px]">存款人姓名</p>
          </div>
          <div className="flex items-center justify-center h-12 bg-[var(--van-black-500)] rounded-lg px-3">
            <div className="flex-1 text-white text-sm">
              <input
                v-model={state.account}
                type="text"
                name="account"
                autocomplete="off"
                className="w-full text-left bg-transparent resize-none border-0"
                placeholder="请输入姓名"
              />
            </div>
          </div>
          <p className="text-white text-xs my-3 px-3">请务必输入正确的存款人姓名，否则无法到账！</p>

          <div className="flex items-center h-8 relative tp-line pl-3 my-3">
            <p className="text-[15px]">存款额度</p>
          </div>
          <div className="flex items-center justify-center h-12 bg-[var(--van-black-500)] rounded-lg px-3">
            <span className="text-sm text-white mr-2">￥</span>
            <div className="flex-1 text-white text-sm">
              <input
                v-model={state.amount}
                type="number"
                name="amount"
                autocomplete="off"
                className="w-full text-left bg-transparent resize-none border-0"
                placeholder="请输入金额"
              />
            </div>
          </div>
          <p className="text-white text-xs my-3 px-3">请务必确认金额！</p>

          <div className="flex flex-col items-center justify-center mt-6">
            <van-button type="primary" round class="w-[200px]" onClick={handleSubmit}>立即存款</van-button>
            <p className="text-xs text-center my-3">如未到账，请<span className="text-[var(--van-primary-color)]" onClick={useService}>联系客服</span>！</p>
          </div>

        </div>
      );
    }

    const renderDescription = () => {
      return (
        <div className="text-xs text-white leading-5	whitespace-pre-wrap">{ current.value?.description }</div>
      )
    }

    const renderOptionBank = () => {
      return (
        <div className="flex flex-col">
          {renderDesc()}
          {renderBankContent()}
          {renderDescription()}
        </div>
      );
    }

    const renderOptionService = () => {
      return (
        <div className="flex flex-col">
          <div v-html={current.value?.description}></div>
          <div className="flex items-center h-8 relative tp-line pl-3 my-3">
            <p className="text-[15px]">VIP专属客服列表</p>
          </div>
          {props.list &&
              props.list.map((val, i) => (
                <div
                  onClick={() => navigatorHandler(val)}
                  className="flex items-center justify-center mb-3 px-4 h-20 relative overflow-hidden rounded bg-[var(--van-black-500)]"
                >
                  <div className="w-10 h-10 overflow-hidden rounded-full">
                    <ImgComponents imgUrl={val.oss_img_url}></ImgComponents>
                  </div>
                  <div className="flex flex-col flex-1 text-xs ml-2">
                    <p>{val.title}</p>
                    <p>{val.sub_title}</p>
                  </div>

                  <van-button type="primary" round size="small" class="w-12">充值</van-button>
                </div>
              ))}
        </div>
      )
    }

    const renderContent = () => {
      if (!current.value) return;

      switch (props.pay_type) {
        case 1:
        case 4:
          return <renderOption></renderOption>;
        case 2:
          return <renderOptionBank></renderOptionBank>;
        case 5:
          return <renderOptionService></renderOptionService>;
      }
    };

    return () => <div className="mt-4 relative">{renderContent()}</div>;
  },
});
