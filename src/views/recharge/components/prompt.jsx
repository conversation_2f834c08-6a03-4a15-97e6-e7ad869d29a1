import { generateRandomFourDigitCombination } from '../utils';
import ImgIcon from '@/assets/icons/cp.png';
export default defineComponent({
  name: 'PromptConfirm',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    amount: Number
  },
  emits: ['update:modelValue', 'confirm', 'cancel'],
  setup(props, { emit }) {

    const password = ref('')

    const result = ref('')

    const [show, toggle] = useToggle(false)

    const onModelValueUpdated = (val) => {
      emit('update:modelValue', val)
    }

    const onClosed = () => {
      password.value = ''
      toggle(false)
      emit('cancel')
    }

    const onOpen = () => {
      result.value = generateRandomFourDigitCombination();
    }

    const onOpened = () => {
      toggle(true)
    }

    watch(
      password,
      (val) => {
        if (val.length === 4) {
          if (val !== result.value) {
            showToast({ message: '验证码错误', position: 'top' })
            password.value = ''
          } else {
            toggle(false)
            onModelValueUpdated(false)
            emit('confirm')
          }
        }
      }
    )

    return () => (
      <van-popup
        show={props.modelValue}
        onUpdate:show={onModelValueUpdated}
        onClosed={onClosed}
        onOpened={onOpened}
        onOpen={onOpen}
        round={true}
        closeable={true}
        safe-area-inset-bottom={true}
        style={{ width: '83.2%', backgroundColor: '#e5e5e5' }}
      >
        <div className="flex flex-col items-center text-black">
          <div className="text-lg my-4">提示</div>
          <img src={ImgIcon} className="h-[50px]" />
          <div className="text-sm text-gray-500 mt-3">筹码充值￥{props.amount}</div>
          <div className="text-[22px] my-5 font-bold">{result.value}</div>

          <van-password-input
            value={password.value}
            gutter={14}
            length={4}
            focused={props.modelValue}
            onFocus={() => toggle(true)}
          />

          <p className="text-gray-400 text-[13px] my-5">如订单无误，请输入以上验证码</p>

          <van-number-keyboard
            teleport="body"
            z-index="3000"
            v-model={password.value}
            show={props.modelValue}
            random-key-order
            onBlur={() => toggle(false)}
          />

        </div>

      </van-popup>
    )
  }
})