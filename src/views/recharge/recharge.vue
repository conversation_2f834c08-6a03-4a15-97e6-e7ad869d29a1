<template>
  <SecondPageLayout>
    <template #navbar-right>
      <div class="flex items-center justify-center w-8 h-8" @click="useService">
        <img src="@/assets/icons/service.png" class="w-5 h-5" />
      </div>
    </template>
    <div class="flex-1 overflow-auto">
      <van-pullRefresh
        v-model="state.refreshing"
        @refresh="onRefresh"
        class="min-h-full"
      >
        <div class="p-4">
          <div className="grid grid-cols-3 gap-x-3 gap-y-2.5">
            <div
              v-for="item in state.list"
              :key="item.id"
              :class="[
                'flex flex-col items-center justify-center h-16 rounded-xl relative',
                {
                  'bg-gradient-orange text-[var(--van-black-500)]': state.recharge_type_id === item.id,
                  'bg-[var(--van-black-500)] text-white': state.recharge_type_id !== item.id
                }
              ]"
              @click="changeHandler(item)"
            >
              <div v-if="item.tag" class="half-diagonal">
                <div class="half-diagonal__text" style="font-size: 10px">{{ item.tag }}</div>
              </div>
              <div class="overflow-hidden w-[25px] h-[25px]">
                <ImgComponents :imgUrl="item.oss_img_url" />
              </div>
              <p class="text-xs mt-2">{{ item.name }}</p>
            </div>
          </div>

          <Options
            ref="optionsRef"
            :recharge_type_id="state.recharge_type_id"
            :pay_type="state.pay_type"
            :list="state.recharge_config"
            :description="state.description"
            @select="selectHandler"
          />
        </div>
      </van-pullRefresh>
    </div>

    <PromptConfirm v-model="showConfirm" :amount="params.amount" @confirm="onConfirm" @cancel="onCancel" />

    <PromptComponents v-model="show" title="温馨提示" hasCancelBtn :content="confirm_content" :cancelCall="cancelHandle" :confirmCall="submitHandler" />
  </SecondPageLayout>
</template>
<script setup>
import {
  GetRechargeTypeList,
  GetRechargeConfigList,
  GetRechargeCollectionList,
  GetCustomerList,
  CreayePayment,
  CreayeQuickPayment
} from '@/api/finance';
import Options from './components/option';
import PromptComponents from '@/components/prompt/prompt';
import PromptConfirm from './components/prompt';
import { uActions } from '@/api/user';
import { confirm_content } from './utils';
import { openExternal } from '@/utils';
import { useService } from '@/hooks';

defineOptions({
  name: 'Recharge',
})

const router = useRouter()
const route = useRoute()
console.log(route.query)

const optionsRef = ref(null)
const show = ref(false)
const showConfirm = ref(false)
const showPopup = ref(false)

const state = reactive({
  refreshing: false,
  list: [],
  pay_type: null,
  recharge_type_id: null,
  recharge_config: [],
  description: '',
})

const params = reactive({
  account: '',
  amount: null,
  price_id: null,
  type_id: null,
  bank_id: null,
  tp: {},
})

function getRechargeConfig(id) {
  GetRechargeConfigList({ recharge_type_id: id }).then(({ data }) => {
    state.recharge_config = data
  })
}

function onRefresh() {
  GetRechargeTypeList()
    .then(({ data }) => {
      state.list = data || []
      const tmp = state.list[0]
      tmp && changeHandler(tmp)
    })
    .finally(() => {
      state.refreshing = false
    })
}

function changeHandler({ id, pay_type, description }) {
  state.recharge_type_id = id
  state.pay_type = pay_type

  // params.account = '';
  // params.amount = null;
  // params.price_id = null;
  // params.type_id = null;
  // params.bank_id = null;
  // params.tp = {};

  /**
   * 1: 通道入款
   * 2: 银行入款
   * 3: 客服入款
   * 4: 交易市场
   * 5: 人工代充
   */
  if (pay_type === 1 || pay_type === 4) {
    getRechargeConfig(id)
  }
  else if (pay_type === 2) {
    GetRechargeCollectionList().then(({ data }) => {
      state.recharge_config = data
      state.description = description
    })
  }
  else if (pay_type === 5) {
    GetCustomerList().then(({ data }) => {
      state.recharge_config = data
    })
  }
}

function cancelHandle() {
  show.value = false
}

function submitHandler() {
  const { tp, bank_id, account, amount } = params
  router.push({
    path: '/recharge_detail',
    query: {
      amount,
      bank_id,
      account,
      realname: tp.realname,
      bank_name: tp.bank_name,
      bank_card: tp.bank_card,
      bank_address: tp.bank_address,
      recharge_type_id: state.recharge_type_id,
      source: route.query.source ?? '',
    },
  })
}

function selectHandler(val) {
  if (state.pay_type === 2) {
    params.amount = val.amount
    params.account = val.account
    params.bank_id = val.bank_id
    params.tp = val.tp
    show.value = true
  }
  else {
    params.amount = val.amount
    params.price_id = val.price_id
    params.type_id = val.type_id
    showConfirm.value = true
  }
}

function onCancel() {
  // state.account = null;
  // state.price_id = null;
  // state.type_id = null;
}

const onConfirm = async () => {
  const toast = showLoadingToast({ duration: 0 });
  try {
    const { data: { pay_url, gateway_get_url, pay_mode } } = await CreayePayment({
      amount: params.amount,
      type_id: params.type_id,
      price_id: params.price_id,
      source: route.query.source ?? ''
    })
    await uActions({ actionType: 7, eventId: state.recharge_type_id })
    window.dsBridge.call('app.openExternalApp', pay_url)
  }
  catch (error) {
    //
  }
  finally {
    toast.close()
  }
}

onRefresh()
</script>
