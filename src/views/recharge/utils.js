export function generateRandomFourDigitCombination() {
  let result = '';
  for (let i = 0; i < 4; i++) {
      const randomDigit = Math.floor(Math.random() * 10); // 生成 0-9 之间的随机数
      result += randomDigit.toString();
  }
  return result;
}

export const confirm_content = '<p>公司银行卡不定期更换，每次充值根据提交订单生成的银行卡入账，<span style="color: red;">切勿</span>直接转账至<span style="color: red;">之前</span>转入的银行卡，否则无法到账，概不负责！</p>'