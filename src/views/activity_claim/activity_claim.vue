<script setup>
import EmptyPage from '@/components/empty/empty'
import { claimRewardsRecord } from '@/api/activity'
import { useListExtra } from '@/hooks'

const {
  query: { taskTypeId, isNewerTask, source },
} = useRoute()
const listParams = reactive({
  taskTypeId: Number(taskTypeId),
  isNewerTask: isNewerTask === 'true',
  paginationId: '',
  source: source || '',
})

function implementationFetched({ paginationId }) {
  listParams.paginationId = paginationId
}

function implementationRefresh() {
  listParams.paginationId = ''
}

const {
  list,
  isFetching,
  refreshing,
  loading,
  error,
  finished,
  onRefresh,
  onLoad,
} = useListExtra({
  serverHandle: claimRewardsRecord,
  immediateParams: listParams,
  pagination: { limit: 'pageSize', data: 'data.list' },
  implementationFetched,
  implementationRefresh,
})
</script>

<template>
  <SecondPageLayout>
    <div class="box-border h-full">
      <van-pull-refresh v-model="refreshing" class="min-h-full" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          v-model:error="error"
          :finished="finished"
          @load="onLoad"
        >
          <ul class="flex flex-col">
            <li class="h-12 flex items-center justify-center text-sm">
              <p style="width: 35%;" class="text-center">
                任务名称
              </p>
              <p style="width: 30%;" class="text-center">
                任务类型
              </p>
              <p style="width: 23%;" class="text-center">
                时间
              </p>
              <p style="width: 22%;" class="text-center">
                数量
              </p>
            </li>
            <li v-for="item in list" :key="item.id" class="h-10 flex items-center justify-center text-xs">
              <p style="width: 35%;" class="text-center">
                {{ item.title }}
              </p>
              <p style="width: 30%;" class="text-center">
                {{ item.activityType }}
              </p>
              <p style="width: 23%;" class="text-center">
                {{ item.optTime }}
              </p>
              <p style="width: 22%;" class="text-center">
                {{ item.amount }}
              </p>
            </li>
          </ul>
        </van-list>

        <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />
      </van-pull-refresh>
    </div>
  </SecondPageLayout>
</template>
