<template>
  <SecondPageLayout>
    <div class="w-[345px] mx-auto bg-[var(--van-black-500)] rounded-sm box-border my-2.5 py-5 px-4">
      <h4 class="text-sm">我遇到的问题</h4>
      <div class="flex items-center mt-3">
        <div
          v-for="tab in tabs"
          :key="tab.key"
          class="rounded-full px-3 text-xs py-1 mr-2.5"
          :class="[tab.key === params.questionType ? 'text-gray-800 bg-[var(--van-primary-color)]' : 'bg-[#444]']"
          @click="clickHandle(tab)"
        >
          <span>{{ tab.value }}</span>
        </div>
      </div>
    </div>
    <div class="w-[345px] mx-auto bg-[var(--van-black-500)] rounded-sm box-border my-2.5">
      <h4 class="text-sm px-4 pt-5">问题描述（必填）</h4>
      <van-field
        v-model="params.descp"
        rows="5"
        autosize
        type="textarea"
        maxlength="250"
        placeholder="请简单描述一下您遇到的问题"
        show-word-limit
      />
    </div>
    <div class="w-[345px] mx-auto bg-[var(--van-black-500)] rounded-sm box-border my-2.5 py-5 px-4">
      <h4 class="text-sm">添加图片</h4>
      <div class="mt-3">
        <van-uploader
          v-model="params.pics"
          multiple
          :preview-size="50"
          :max-size="1024 * 1024 * 5"
          :max-count="3"
          :before-read="asyncBeforeRead"
          :after-read="asyncAfterRead"
          upload-icon="plus"
          accept="image/*"
          @oversize="handleOversize"
        />
      </div>
    </div>
    <div class="w-[345px] mx-auto bg-[var(--van-black-500)] rounded-sm box-border my-2.5 py-5 px-4">
      <h4 class="text-sm">联系方式</h4>
      <div class="mt-3">
        <input
          v-model="params.contact"
          type="text"
          placeholder="微信/QQ/Telegram/skype"
          class="w-full text-white text-xs bg-transparent border-0 block"
        />
      </div>
    </div>

    <div class="w-[260px] mx-auto mt-20 mb-5">
      <van-button type="primary" block @click="onSubmit">提交意见</van-button>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useAppStore } from '@/store/app';
import { picUpload, sendFeedback } from '@/api/user';
import { compressorImage } from '@/utils/compress';


const appStore = useAppStore();

const tabs = computed(() => appStore.appConfig?.feedbackQuestionType);

const params = reactive({
  questionType: null,
  descp: '',
  pics: [],
  contact: ''
});

const handleOversize = () => {
  showToast('文件大小不可超过5M');
};

const asyncBeforeRead = (image) => {
  return new Promise((resolve, reject) => {
    compressorImage(image).then(({ file, resultSize }) => {
      resolve(file)
    })
  });
};

const asyncAfterRead = (file) => {
  file.status = 'uploading';
  const formData = new FormData();
  formData.append('file', file.file, file.file.name);

  picUpload(formData).then((res) => {
    file.status = 'done'
    file.url = res.data.relaUrl
  }).catch(() => {
    file.status = 'failed'
  })
};

const clickHandle = ({ key }) => {
  params.questionType = key;
};

const onSubmit = () => {
  if (!params.questionType) {
    showToast('请选择问题类型');
    return;
  }

  if (!params.descp) {
    showToast('请输入反馈意见');
    return;
  }

  if (!params.contact) {
    showToast('请输入联系方式');
    return;
  }

  sendFeedback({
    ...params,
    pics: params.pics.filter(o => o.status === 'done').map(o => o.url)
  }).then(() => {
    params.questionType = null;
    params.descp = '';
    params.contact = '';
    params.pics = [];

    showSuccessToast('反馈成功');
  }).finally(() => {
  })
}

</script>
