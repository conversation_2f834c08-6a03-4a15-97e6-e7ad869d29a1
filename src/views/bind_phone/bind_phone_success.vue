<template>
  <SecondPageLayout>
    <!-- <div class="login_logo mx-auto w-[196px] h-[55.75px] mt-12">
      <img src="/static/image/login-logo.png" alt="">
    </div> -->
    <div className="login_logo mx-auto w-[196px] h-[55.75px] mt-12" v-if="merchantLogo">
      <ImgComponents :imgUrl="merchantLogo?.bind_phone_logo" />
    </div>

    <div class="flex flex-col items-center flex-1 mt-12 rounded-t-3xl bg-[var(--van-black-500)]">
      <h1 class="text-[var(--van-primary-color)] text-base mt-20 my-8">已绑定手机号码</h1>
      <div class="w-[250px]">
        <van-button type="primary" round block>+86 {{ tel.replace(/(\d{3})\d*(\d{4})/, '$1****$2') }}</van-button>
      </div>

      <div class="flex items-center justify-around w-full mt-auto pb-5">
        <div class="flex flex-col items-center">
          <img src="@/assets/icons/login_tip.png" class="w-[68px] h-[68px]">
          <p class="mt-5 text-sm">手机登录</p>
        </div>
        <div class="flex flex-col items-center">
          <img src="@/assets/icons/login_safe.png" class="w-[68px] h-[68px]">
          <p class="mt-5 text-sm">安全认证</p>
        </div>
        <div class="flex flex-col items-center">
          <img src="@/assets/icons/login_max.png" class="w-[68px] h-[68px]">
          <p class="mt-5 text-sm">收益保障</p>
        </div>
      </div>

    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useAppStore } from '@/store/app';
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const tel = computed(() => userStore.tel)

const appStore = useAppStore();
const merchantLogo = computed(() => appStore.appConfig?.merchantLogo);

</script>
