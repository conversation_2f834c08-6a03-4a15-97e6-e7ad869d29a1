<template>

  <SecondPageLayout>
    <template #navbar>
      <div class="flex-none van-safe-area-top"></div>
    </template>
    <div class="w-full h-full relative" @click="launchHandle">
      <ImgComponents v-if="splash" :imgUrl="splash.picUrl" hasAnimation />
    </div>
    <div v-if="splash" class="absolute top-4 right-4 px-3 py-2 rounded-full bg-black text-white text-xs" @click.stop="overLaunch">VIP跳过{{ t }}秒</div>
  </SecondPageLayout>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from '@/store/app';
import { useUserStore } from "@/store/user";
import { uActions } from '@/api/user';
import { GetAdListAPI } from '@/api/home';
import { useNavigate } from '@/hooks';
import { PageEnum } from '@/enums/pageEnum';
import { getLocalStorage } from '@/utils';

const appStore = useAppStore();
const userStore = useUserStore();

const { vipLevel } = storeToRefs(userStore);

const router = useRouter();

const { navigateTo } = useNavigate();

const url = window.sessionStorage.getItem('ReffferUrl');
const t = ref(5);

const { pause, resume, isActive } = useIntervalFn(() => {
  if (t.value > 1) t.value -= 1;
  else pause();
}, 1000, {
  immediate: false
});

const splash = ref(null);
const splashImg = ref(null);
const splashLink = ref('');

const getAdList = async () => {
  try {
    const res = await GetAdListAPI({ app_index_arr: [8] });
    let data = res.data.find((val) => val.id === 8)?.adList;

    if (data.length > 0) {
      resume();
      const tmp = data[0];
      splash.value = tmp;
    } else {
      hideLaunch();
    }
  } catch (error) {}
};

const launchHandle = async () => {
  navigateTo(splash.value.content, splash.value.jumpType);
  await uActions({ actionType: 11, eventId: splash.value.id });
};

const overLaunch = () => {
  const token = getLocalStorage(import.meta.env.VITE_TOKEN_KEY);
  if (token) {
    if (vipLevel.value > 0) {
      hideLaunch();
    } else {
      router.push({ path: '/vip', query: { redirect: '1' } })
    }
  }
}

const hideLaunch = () => {
  appStore.SetHasNeedAdAction(true);
  router.replace({ path: PageEnum.BASE_HOME });

  setTimeout(() => {
    const token = getLocalStorage(import.meta.env.VITE_TOKEN_KEY);
    if (!token) {
      appStore.SetHasNeedLoginAction(true);
    }
  }, 200)
};

watch(isActive, (val) => {
  if (!val) {
    hideLaunch();
  }
});

onMounted(() => {
  getAdList();
});
</script>
