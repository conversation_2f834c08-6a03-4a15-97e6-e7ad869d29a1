<template>
  <SecondPageLayout>
    <div class="px-6 pt-8">
      <!-- Account Field -->
      <div class="flex items-center h-12 bg-[var(--van-black-500)] rounded-tl-lg rounded-tr-lg">
        <div class="flex items-center w-20 pl-4 text-white text-sm">
          <span>账号</span>
        </div>
        <div class="flex-1 pr-4">
          <van-field
            v-model="params.account"
            type="text"
            name="account"
            autocomplete="off"
            placeholder="请设置您的账号"
            class="password-field"
            :border="false"
          />
        </div>
      </div>
      <div class="mx-1 border-t-[1px] border-[#4c4c4c] border-solid"></div>

      <!-- Password Field -->
      <div class="flex items-center h-12 bg-[var(--van-black-500)]">
        <div class="flex items-center w-20 pl-4 text-white text-sm">
          <span>密码</span>
        </div>
        <div class="flex-1 pr-4">
          <van-field
            v-model="params.password"
            type="password"
            name="password"
            autocomplete="off"
            placeholder="请输入您的密码"
            class="password-field"
            :border="false"
          />
        </div>
      </div>

      <div class="mx-1 border-t-[1px] border-[#4c4c4c] border-solid"></div>

      <!-- Confirm Password Field -->
      <div class="flex items-center h-12 bg-[var(--van-black-500)] rounded-bl-lg rounded-br-lg">
        <div class="flex items-center w-20 pl-4 text-white text-sm">
          <span>确认密码</span>
        </div>
        <div class="flex-1 pr-4">
          <van-field
            v-model="params.confirmPassword"
            type="password"
            name="confirmPassword"
            autocomplete="off"
            placeholder="请输入您的密码"
            class="password-field"
            :border="false"
          />
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="px-6 mt-16">
      <van-button type="primary" round block @click="handleSubmit" :loading="loading" class="h-12"> 确认 </van-button>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { showToast } from 'vant';
import { useRouter } from 'vue-router';
import SecondPageLayout from '@/layouts/SecondPageLayout.vue';
import { PersonInfoDataAPI } from '@/api/user';
import { useUserStore } from '@/store/user';

defineOptions({
  name: 'PasswordSetting'
});

const router = useRouter();
const userStore = useUserStore();
const loading = ref(false);

// Form parameters
const params = reactive({
  account: '',
  password: '',
  confirmPassword: ''
});

// Handle form submission
const handleSubmit = async () => {
  // Basic validation
  if (!params.account) {
    showToast('请输入账号');
    return;
  }

  if (!params.password) {
    showToast('请输入密码');
    return;
  }

  if (!params.confirmPassword) {
    showToast('请确认密码');
    return;
  }

  if (params.password !== params.confirmPassword) {
    showToast('两次输入的密码不一致');
    return;
  }

  if (params.password.length < 6 || params.password.length > 20) {
    showToast('密码长度应为6-20位字符');
    return;
  }

  // Password strength validation
  const hasLetter = /[a-zA-Z]/.test(params.password);
  const hasNumber = /\d/.test(params.password);

  if (!hasLetter || !hasNumber) {
    showToast('密码必须包含字母和数字');
    return;
  }

  try {
    loading.value = true;

    PersonInfoDataAPI({
      username: params.account,
      newPwd: params.password,
      repeatNewPwd: params.confirmPassword
    }).then(() => {
      onModelValueUpdated(false);
      userStore.updatePersonData();
    });

    showToast('账号密码设置成功');

    // Navigate back to settings page
    router.back();
  } catch (error) {
    showToast(error.message || '设置失败，请重试');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
/* Custom styles for password fields */
.password-field {
  background: transparent !important;
  padding: 0 !important;
  height: 100% !important;
}

.password-field :deep(.van-field__control) {
  background: transparent !important;
  color: white !important;
  border: none !important;
  padding: 0 !important;
  font-size: 14px !important;
  height: 100% !important;
}

.password-field :deep(.van-field__control::placeholder) {
  color: #999 !important;
  font-size: 14px !important;
}

.password-field :deep(.van-field__control:focus) {
  outline: none !important;
}

.password-field :deep(.van-field__body) {
  background: transparent !important;
  padding: 0 !important;
  height: 100% !important;
}

.password-field :deep(.van-cell) {
  background: transparent !important;
  padding: 0 !important;
  height: 100% !important;
}
</style>
