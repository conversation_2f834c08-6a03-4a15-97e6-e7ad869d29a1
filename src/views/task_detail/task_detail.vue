<script setup>
import { claimAll, claimRewards, listSubTask } from '@/api/activity'
import { useNavigate } from '@/hooks'

const { query: { title, taskTypeId, isNewerTask, third } } = useRoute()

const { navigateTo } = useNavigate()

const router = useRouter()

const prizeTypeMaps = new Map([
  ['PT', '彩金'],
  ['DIA', '钻石'],
])

const list = ref([])

const refreshing = ref(false)

const linkUrl = ref('')

const progress = ref({
  done: 0,
  total: 0,
})

const percentage = computed(() => {
  if (progress.value.total === 0)
    return 0
  return progress.value.done / progress.value.total * 100
})

function generatorPercentage({ done, total }) {
  return done / total * 100
}

const toast = showLoadingToast({ duration: 0, forbidClick: true })

async function onRefresh() {
  try {
    const res = await listSubTask({
      taskTypeId: Number(taskTypeId),
      isNewerTask: false,
    })
    list.value = res.data.list || []
    linkUrl.value = res.data.linkUrl
    progress.value = res.data.progress
  }
  catch (error) {
    //
  }
  finally {
    toast.close()
    refreshing.value = false
  }
}

function navigatorHandle() {
  navigateTo(linkUrl.value, 1)
}

function clickRightHandle() {
  router.push({ path: '/activity_claim', query: { taskTypeId, isNewerTask } })
}

function claimHandle({ taskListId }) {
  claimRewards({
    taskListId,
    isNewerTask: false,
    source: third || '',
  })
    .then(() => {
      showSuccessToast('领取成功')
      onRefresh()
    })
    .catch((error) => {
      //
    })
}

function claimAllHandle() {
  if (progress.value.done > progress.value.total) {
    claimAll({ taskTypeId: Number(taskTypeId), source: third || '' }).then(() => {
      showSuccessToast('领取成功')
      onRefresh()
    })
  }
}

onRefresh()
</script>

<template>
  <SecondPageLayout :title="title">
    <template #navbar-right>
      <van-button round plain type="primary" size="mini" class="w-[75px]" @click="clickRightHandle">
        领取记录
      </van-button>
    </template>

    <div class="box-border h-full">
      <van-pull-refresh v-model="refreshing" class="min-h-full pb-20" @refresh="onRefresh">
        <div class="mx-4 h-[107px] flex flex-col overflow-hidden border border-white rounded-lg border-solid">
          <div class="h-[42px] flex items-center rounded-t-lg bg-[var(--van-primary-color)] px-2.5">
            <span class="text-sm text-[var(--van-gray-8)]">完成下个{{ title }}还需{{ progress.total - progress.done }}</span>
            <div v-if="progress.done > progress.total" class="relative ml-2.5 h-5 w-[74px] flex items-center rounded-full bg-[#ffcfb1] text-[0.625rem] text-[#ff553e]">
              <img src="@/assets/icons/PT.png" class="h-5 w-5">
              <span class="flex-1 text-center">可领取</span>
            </div>
          </div>
          <div class="flex flex-1 items-center justify-around text-xs">
            <div class="mr-1 w-[187px]">
              <van-progress
                :percentage="percentage"
                stroke-width="5"
                color="#ffd631"
                track-color="#666666"
                :show-pivot="false"
              />
            </div>
            <span>{{ progress.done }}/{{ progress.total }}</span>
            <van-button v-if="progress.total > progress.done" size="mini" type="primary" round class="w-[70px]" @click="navigatorHandle">
              前往完成
            </van-button>
            <van-button v-else-if="progress.total === progress.done" size="mini" type="info" round class="w-[70px]">
              已完成
            </van-button>
          </div>
        </div>

        <div v-for="item in list" :key="item.id" class="van-hairline--bottom flex items-center py-4">
          <div class="mx-4 flex flex-1 flex-col justify-around">
            <div class="mb-2 flex items-center">
              <span class="text-[15px]">{{ item.title }}</span>
              <div class="relative ml-2.5 box-border h-5 min-w-[59px] flex items-center rounded-full bg-[#ffcfb1] text-[0.625rem] text-[#ff553e]">
                <img src="@/assets/icons/PT.png" class="h-5 w-5">
                <span class="flex-1 px-1 text-center">{{ prizeTypeMaps.get(item.prizeType) }}{{ item.award }}</span>
              </div>
            </div>
            <div class="flex items-center text-xs">
              <span v-if="item.cycleType !== 'accumulate'" class="text-[0.625rem] text-[#e6e6e6]">{{ item.startTime }}-{{ item.endTime }}</span>
              <template v-else>
                <div class="mr-1 w-[150px]">
                  <van-progress
                    :percentage="generatorPercentage(item.progress)"
                    stroke-width="5"
                    color="#ffd631"
                    track-color="#666666"
                    :show-pivot="false"
                  />
                </div>
                <span>{{ item.progress.done }}/{{ item.progress.total }}</span>
              </template>
            </div>
          </div>
          <div class="mr-3 flex-none">
            <van-button v-if="item.awardStatus === 'FetchNow'" size="mini" type="primary" round class="w-[70px]" @click="claimHandle(item)">
              领取
            </van-button>
            <van-button v-else-if="item.awardStatus === 'DoJob'" size="mini" type="primary" round class="w-[70px]">
              未完成
            </van-button>
            <van-button v-else-if="item.awardStatus === 'Done'" size="mini" type="info" round class="w-[70px]">
              已领取
            </van-button>
          </div>
        </div>
      </van-pull-refresh>
    </div>

    <div class="fixed bottom-0 left-0 right-0 bg-[var(--van-black)] p-4">
      <van-button :type="progress.done > progress.total ? 'primary' : 'info'" round block @click="claimAllHandle">
        一键领取
      </van-button>
    </div>
  </SecondPageLayout>
</template>
