<template>
  <SecondPageLayout>
    <template #navbar>
      <van-nav-bar
        safe-area-inset-top
        left-arrow
        :title="title"
        @click-left="$router.go(-1)"
        @click-right="clickRightHandler"
        :border="false"
      >
        <template #right>
          <img src="@/assets/icons/wr.png" class="h-17px" />
        </template>
      </van-nav-bar>
    </template>

    <div v-if="_type === 1" class="mx-4 mt-3 h-20">
      <BankCard :list="bank_list" @change="changeHandler"></BankCard>
    </div>
    <div class="m-4 rounded-lg bg-[var(--van-black-500)] px-4">
      <div class="flex items-baseline text-base py-3">
        <p>提现金额</p>
        <span class="text-xs text-amber-200 ml-1"
          >每笔提现金额≥100，且金额为整数</span
        >
      </div>
      <div class="flex items-center py-5 van-hairline--bottom">
        <em class="px-3 text-xl">¥</em>
        <div class="flex-1 text-lg">
          <input
            type="number"
            v-model="params.amount"
            class="w-full text-left bg-transparent resize-none border-0"
            placeholder="请输入提现金额"
          />
        </div>
        <div
          role="button"
          class="py-2 text-xs text-[var(--van-primary-color)]"
          @click="maxHandler"
        >
          最大金额
        </div>
      </div>
      <div class="flex items-center justify-between text-xs py-5">
        <span>帐户余额 {{ wallet.points }}</span>
        <span>提现所需打码量 {{ wallet.quotas }}</span>
      </div>
    </div>

    <div class="my-8 mx-4">
      <van-button type="primary" round block @click="handleSubmit"
        >提现</van-button
      >
    </div>

    <div class="px-4 pb-6 text-xs whitespace-pre-wrap">{{ description }}</div>

    <PromptConfirm v-model="showConfirm" :amount="params.amount" :fee="fee" @success="submitHandler" />

    <PromptComponents
      v-model="show"
      title="温馨提示"
      content="为了您的账号安全，请设置支付密码"
      :hasCancelBtn="true"
      :confirmCall="confirmHandler"
      :cancelCall="cancelHandler"
    />

  </SecondPageLayout>
</template>
<script setup>
import { throttle } from "lodash-es";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store/user";
import PromptConfirm from './components/prompt';
import PromptComponents from "@/components/prompt/prompt";
import BankCard from "./components/bankcard.vue";
import { CreateWithdrawAPI, GetWithdrawBankList } from "@/api/finance";
import { uActions } from '@/api/user';
import { getLocalStorage } from "@/utils";

const {
  query: { title, type, id, fee },
} = useRoute();

const router = useRouter();

const show = ref(false);
const showConfirm = ref(false);

const _type = Number(type);
const type_id = Number(id);

const userStore = useUserStore();

const bank_list = shallowRef([]);

const description = getLocalStorage("ss_withdraw_description");
const { wallet, is_set_payment_password, tel } = storeToRefs(userStore);

const params = reactive({
  amount: null,
  card_id: -1,
});

const getBankList = () => {
  GetWithdrawBankList().then(({ data }) => {
    bank_list.value = data || [];
    if (data.length > 0) {
      params.card_id = data[0].card_id;
    }
  });
};

const clickRightHandler = () => {
  router.push({ path: "/amount_record", query: { type: 1 } });
};

const confirmHandler = () => {
  router.push({ path: "/pay_password_set" });
}

const cancelHandler = () => {
  show.value = false;
}

const handleSubmit = () => {
  if (_type === 1 && params.card_id === -1) {
    showToast("请选择银行卡");
    return;
  }
  if (!params.amount) {
    showToast("请输入金额");
    return;
  }

  if (!/^[1-9]\d{2,}$/.test(params.amount)) {
    showToast("请输入正确金额");
    return;
  }

  if (+params.amount > wallet.value.points) {
    showToast("余额不足");
    return;
  }

  const allow_amount = wallet.value.points - wallet.value.quotas

  if (+params.amount > allow_amount) {
    showToast(`该提现还需${+params.amount - allow_amount}打码量`)
    return
  }


  if (!tel.value) {
    router.push({ path: '/bind_phone' })
    return;
  }

  if (is_set_payment_password.value === 0) {
    show.value = true;
    return;
  }

  showConfirm.value = true;
};

const submitHandler = throttle(
  () => {
    showConfirm.value = false;
    const toast = showLoadingToast({ duration: 0 });
    CreateWithdrawAPI({
      amount: Number(params.amount),
      card_id: params.card_id,
      type_id: type_id,
    })
      .then(async () => {
        showToast('提现请求已提交');
        await uActions({ actionType: 8 })
      })
      .finally(() => {
        toast.close();
      });
  },
  2000,
  { trailing: false },
);

const changeHandler = (card_id) => {
  params.card_id = card_id;
};

const maxHandler = () => {
  params.amount = parseInt(wallet.value.points);
};

onMounted(() => {
  userStore.updateUserWalletData();
  if (_type === 1) getBankList();
});

onUnmounted(() => {
  submitHandler.cancel();
});
</script>
