<template>
  <Swiper
    slidesPerView="auto"
    :spaceBetween="15"
    @slideChange="onSlideChange"
  >
    <SwiperSlide v-for="item in list" :key="item.id" style="width: 86%;">
      <div class="flex h-20 rounded-xl bg-[var(--van-black-500)]">
        <img class="w-6 h-6 mx-5 mt-4" src="@/assets/icons/bank.png" alt="">
        <div class="flex flex-col justify-around py-2">
          <p class="text-sm">{{ item.bank_name }}</p>
          <p class="text-xs">{{ formatBankCardNumber(item.bank_card) }}</p>
        </div>
      </div>
    </SwiperSlide>
    <SwiperSlide  style="margin-right: 0;">
      <div class="flex items-center justify-center h-20 rounded-xl bg-[var(--van-black-500)]" @click="clickHandler">
        <img src="@/assets/icons/add.png" class="h-4">
        <span class="text-13px ml-1">立即绑定银行卡</span>
      </div>
    </SwiperSlide>
  </Swiper>
</template>

<script setup>
import { formatBankCardNumber } from '@/utils';
import { Swiper, SwiperSlide } from "swiper/vue";
import "swiper/css";


defineOptions({
  name: 'BankCard'
})

const emit = defineEmits(['change'])

const router = useRouter();

const props = defineProps({
  list: {
    type: Array,
    default: () => []
  }
})


const onSlideChange = (swiper) => {
  emit('change', props.list[swiper.activeIndex]?.card_id || -1)
}

const clickHandler = () => {
  router.push({ path: '/bind_bankcard' })
}

</script>