import { UUID } from '@/utils';
import { verifyPayPwd } from "@/api/user";
export default defineComponent({
  name: 'PromptConfirm',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    amount: [Number, null],
    fee: [Number, String]
  },
  emits: ['update:modelValue', 'success'],
  setup(props, { emit }) {
    const payPwd = ref('')
    const [show, toggle] = useToggle(false)
    const onModelValueUpdated = (val) => {
      emit('update:modelValue', val)
    }

    const onClosed = () => {
      payPwd.value = ''
      toggle(false)
    }

    const onOpened = () => {
      toggle(true)
    }

    watch(
      payPwd,
      (val) => {
        if (val.length === 6) {
          const toast = showLoadingToast({ duration: 0 })
          verifyPayPwd({
            nonce: UUID(),
            payPwd: unref(payPwd)
          }).then(() => {
            toggle(false);
            onModelValueUpdated(false);
            emit('success')
          }).catch((error) => {
            payPwd.value = ''
          }).finally(() => {
            toast.close();
          })
        }
      }
    )

    return () => (
      <van-popup
        show={props.modelValue}
        onUpdate:show={onModelValueUpdated}
        onClosed={onClosed}
        onOpened={onOpened}
        round={true}
        closeable={true}
        safe-area-inset-bottom={true}
        style={{ width: '83.2%', backgroundColor: 'white' }}
      >
        <div className="flex flex-col items-center text-black px-3 py-4" style="--van-password-input-height: 40px;">
          <div className="text-lg">请输入支付密码</div>
          <div className="text-2xl text-gray-500 mt-3">￥{props.amount}</div>
          <div className='w-full text-sm flex items-center justify-between h-8'>
            <p>实际到账</p>
            <p>{props.amount}</p>
          </div>
          <div className='w-full text-sm flex items-center justify-between h-8'>
            <p>手续费</p>
            <p>{props.fee}</p>
          </div>

          <van-password-input
            value={payPwd.value}
            gutter={4}
            length={6}
            focused={show.value}
            onFocus={() => toggle(true)}
          />
          <van-number-keyboard
            teleport="body"
            z-index="3000"
            v-model={payPwd.value}
            show={show.value}
            random-key-order
            onBlur={() => toggle(false)}
          />

        </div>

      </van-popup>
    )
  }
})