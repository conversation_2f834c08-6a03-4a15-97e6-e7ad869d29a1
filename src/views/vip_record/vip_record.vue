<template>
  <SecondPageLayout>
    <div class="h-full py-2 px-3 box-border">
      <van-pullRefresh
        v-model="refreshing"
        @refresh="onRefresh"
        class="min-h-full"
      >
        <van-list
          v-model:loading="loading"
          v-model:error="error"
          :finished="finished"
          @load="onLoad"
        >
          <ul>
            <li v-for="item in list" :key="item.id" class="flex items-center p-3 mb-3 text-xs rounded-lg bg-[var(--van-black-500)]">
              <span class="w-28">{{ item.award_name }}</span>
              <p class="flex-1 text-left text-[var(--van-gray-6)]">{{ formatter(item.create_time) }}</p>
              <span class="font-bold text-[var(--van-primary-color)]">{{ item.award_num }}</span>
            </li>
          </ul>
        </van-list>

        <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />

      </van-pullRefresh>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { RecordVipAPI } from '@/api/user';
import { formatter } from '@/utils';
import EmptyPage from '@/components/empty/empty';


const params = reactive({
  page_key: '',
  limit: 20
})
const refreshing = ref(false);
const loading = ref(false);
const error = ref(false);
const finished = ref(false);
const list = ref([]);

const onRefresh = () => {
  list.value = [];
  finished.value = false;
  loading.value = true;
  params.page_key = '';
  onLoad();
};

const onLoad = async () => {
  try {
    const res = await RecordVipAPI(params);

    refreshing.value = false;

    if (res) {
      const tmp = res.data.list || [];
      list.value = list.value.concat(tmp);
      loading.value = false;

      if (tmp.length < params.limit) {
        finished.value = true;
      } else if (tmp.length === params.limit) {
        states.finished = false;
        params.page_key = res.data.page_key.max_key_id;
      }
    } else {
      refreshing.value = false;
      finished.value = true;
      loading.value = false;
    }
  } catch(error) {
    refreshing.value = false;
    finished.value = true;
    loading.value = false;
  }
  };

</script>