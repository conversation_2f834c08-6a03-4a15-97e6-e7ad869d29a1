<template>
  <SecondPageLayout>
    <div class="w-[345px] mx-auto mt-3 flex items-center justify-center h-36 rounded bg-[var(--van-black-500)]">
      <!-- <img src="/static/image/login-logo.png" class="h-15"> -->

      <div className="w-205px overflow-hidden" v-if="merchantLogo">
        <ImgComponents :imgUrl="merchantLogo?.business_cooperation_logo" />
      </div>

    </div>
    <p class="my-4 text-sm text-center">》》专属商务联系方式《《</p>
    <ul class="w-[345px] mx-auto">
      <li v-for="val in list" :key="val.id" class="flex items-center h-50px rounded mb-4 bg-[var(--van-black-500)]">
        <div class="w-34px h-34px overflow-hidden rounded mx-4">
          <ImgComponents :imgUrl="val.logo" />
        </div>
        <div class="flex-1 text-sm"><p class="van-ellipsis">{{ val.groupUrl }}</p></div>
        <div>
          <van-button type="primary" round size="mini" class="w-50px" @click="copyHandler(val)">复制</van-button>
        </div>
      </li>
    </ul>
  </SecondPageLayout>
</template>

<script setup>
import { useAppStore } from '@/store/app';
import useClipboard from 'vue-clipboard3';

const appStore = useAppStore();
const merchantLogo = computed(() => appStore.appConfig?.merchantLogo);
const { toClipboard } = useClipboard();

const list = computed(() => appStore.appConfig?.businessCooperation);

const copyHandler = ({ groupUrl }) => {
  try {
    toClipboard(groupUrl);
    showToast('复制成功');
  } catch (e) {
    console.error(e)
  }
}

</script>
