<template>
  <SecondPageLayout>
    <template #navbar>
      <van-nav-bar safe-area-inset-top left-arrow :title="$route.meta.title" @click-left="$router.go(-1)" :border="false" />

      <div class="game_record_tabs">
        <van-tabs v-model:active="type" line-width="25">
          <van-tab v-for="date in dateList" :name="date.type" :title="date.title" />
        </van-tabs>
      </div>
    </template>

    <div class="flex flex-col p-2.5">
      <div class="flex items-center justify-center h-[85px] bg-[url('@/assets/image/bet-bg.png')] bg-no-repeat bg-cover mb-4">
        <div class="flex flex-1 flex-col items-center justify-center">
          <p class="text-xs text-[var(--van-primary-color)]">下注总金额</p>
          <p class="text-xl text-white font-bold mt-2">{{ state.all_point }}</p>
        </div>
        <img src="@/assets/image/bet.png" class="h-[59px]" />
        <div class="flex flex-1 flex-col items-center justify-center">
          <p class="text-xs text-[var(--van-primary-color)]">中奖总金额</p>
          <p class="text-xl text-white font-bold mt-2">{{ state.all_re_point }}</p>
        </div>
      </div>

      <div
        v-for="item in state.list"
        :key="item.id"
        class="flex flex-col mb-3 bg-#ffd631 rounded-2xl border border-white border-solid overflow-hidden"
      >
        <div class="flex items-center text-[var(--van-gray-8)] bg-[var(--van-primary-color)] h-16 px-3">
          <div class="w-[38px] h-[38px] overflow-hidden rounded-md">
            <ImgComponents :imgUrl="item.icon" />
          </div>
          <div class="text-md ml-3 mr-auto">{{ item.name }}</div>
          <router-link :to="{ path: '/game_record_order', query: { type, platform_id: item.id } }">
            <div role="button" class="text-xs p-1">更多<van-icon name="arrow" /></div>
          </router-link>
        </div>
        <div class="flex items-center justify-center bg-[var(--van-gray-8)] h-16 text-white text-xs">
          <div class="flex flex-col items-center justify-center flex-1">
            <p class="mb-2">下单注量</p>
            <p class="font-bold text-[var(--van-primary-color)]">{{ item.count }}</p>
          </div>
          <div class="flex flex-col items-center justify-center flex-1">
            <p class="mb-2">下单金额</p>
            <p class="font-bold text-[var(--van-red)]">￥{{ item.point }}</p>
          </div>
          <div class="flex flex-col items-center justify-center flex-1">
            <p class="mb-2">中奖金额</p>
            <p class="font-bold text-[var(--van-primary-color)]">￥{{ item.re_point }}</p>
          </div>
        </div>
      </div>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import BetIcon from '@/assets/image/bet.png';
import { GetGameRecordAPI } from '@/api/game';
import { getDateRange } from '@/utils';

const type = ref(1);

const state = reactive({
  list: [],
  all_point: '0.00',
  all_re_point: '0.00'
});

const dateList = [
  { title: '今日', type: 1 },
  { title: '昨日', type: 2 }
];

const getData = () => {
  const [time_start, time_end] = getDateRange(type.value === 1 ? 0 : 1);

  GetGameRecordAPI({
    time_start,
    time_end
  }).then(({ data }) => {
    console.log('GetGameRecordAPI', data);

    state.list = data.lists;
    state.all_point = data.all_point;
    state.all_re_point = data.all_re_point;
  });
};

watchEffect(getData);
</script>

<style lang="less">
.game_record_tabs {
  .van-tabs__wrap {
    height: 52px;
  }

  .van-tabs__nav {
    background-color: var(--van-black-500);
  }

  .van-tab--active {
    font-size: 20px;
  }

  .van-tabs__line {
    bottom: 22px;
  }
}
</style>
