<template>
  <div class="flex flex-col h-full">
    <VideoComponents
      ref="VideoRef"
      v-if="playedID"
      :key="playedID"
      :vid="videoData.id"
      :mediaType="videoData.mediaType"
      :type="videoData.type"
      :title="videoData.title"
      :cover="videoData.videoCover"
      :isAllowLong="videoData.isAllowLong"
      :isAllowShort="videoData.isAllowShort"
      :isCloudMode="videoData.isCloudMode"
      :cloudFileId="videoData.cloudFileId"
      :playUrl="videoData.playUrl"
      :cloudUrl="videoData.cloudUrl"
    />

    <div class="flex-1 overflow-hidden overflow-y-auto">
      <VideoHeader
        :vid="videoData.id"
        :mediaType="videoData.mediaType"
        :title="videoData.title"
        :isLike="videoData.isLike"
        :isCollected="videoData.isCollected"
        :updatedAt="videoData.updatedAt"
        :views="videoData.viewTimes"
        :tags="videoData.tags"
        :category-list="videoCategoryList"
        v-model:shareTimes="videoData.shareTimes"
        v-model:likeTimes="videoData.likeTimes"
        v-model:collectTimes="videoData.collectTimes"
        @open-popup="onOpenLotteryPopup"
      />

      <TabsComponents :list="tabList" :callback="tabSelectHandle"></TabsComponents>

      <div class="flex-1">
        <ul class="overflow-hidden">
          <li v-if="activeIndex === '1'">
            <FirstTab :key="playedID"></FirstTab>
          </li>
          <li v-else-if="activeIndex === '2'">
            <SecondTab></SecondTab>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <VideoLotteryPopup v-model:show="showLotteryPopup" v-model:game_id="popupGameId"></VideoLotteryPopup>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useUserStore } from "@/store/user";
import { useAppStore } from "@/store/app";
import VideoComponents from "./components/video.vue";
import VideoHeader from "./components/video_header.vue";
import VideoLotteryPopup from "./components/video_lottery_popup.vue";
import TabsComponents from './components/tabs.vue';
import FirstTab from './components/first_tab.vue';
import SecondTab from './components/second_tab.vue';
import { GetGameVideoCategoryNEWAPI } from '@/api/game';
import { uActions } from '@/api/user';
import { videoDetail } from "@/api/video";

defineOptions({
  name: 'VideoDetails'
})

const route = useRoute();
const userStore = useUserStore();
const appStore = useAppStore();

const { playedID } = storeToRefs(appStore);

const VideoRef = ref(null);
const showPlayer = ref(true)
const showLotteryPopup = ref(false);
const popupGameId = ref(null);
const loading = ref(false);
const activeIndex = ref('1');
const videoCategoryList = ref([]);
const tabList = [
  {
    title: '为你推荐',
    key: '1'
  },
  {
    title: '评论',
    key: '2'
  },
];

const tabSelectHandle = (index) => {
  activeIndex.value = index;
};

const videoData = ref({});

const getVideoDetailInfo = (videoId) => {
  try {
    loading.value = true;
    videoDetail({ videoId }).then(({ data }) => {
      videoData.value = data;
    });

  } finally {
    loading.value = false;
  }
};

const getVideoCategory = () => {
  try {
    GetGameVideoCategoryNEWAPI().then(({ data }) => {
      popupGameId.value = data[0].id;
      showLotteryPopup.value = true;
      videoCategoryList.value = data;
    });

  } catch(e) {
    //
  }
};

const onOpenLotteryPopup = async (game_id) => {
  popupGameId.value = game_id;
  showLotteryPopup.value = true;
};

// clean the game id when close the lottery popup
watch(
  () => showLotteryPopup.value,
  (val) => {
    if (!val) {
      popupGameId.value = null;
    }
  }
);


onMounted(() => {
  getVideoCategory();
});

onActivated(() => {
  if (videoCategoryList.value.length) {
    popupGameId.value = videoCategoryList.value[0].id;
    showLotteryPopup.value = true;
  }
  
  VideoRef.value?.play();
});

onDeactivated(() => {
  console.log('onDeactivated');
  VideoRef.value?.pause();
});

watch(
  () => route.params.id,
  (newId, oldId) => {
    // if (oldId) {
    //   VideoRef.value?.uploadProgress();
    // };
    if (newId && newId !== oldId) {
      appStore.SetPlatedIDAction(newId);
      getVideoDetailInfo(parseInt(newId));
      userStore.updatePersonData();
    }
  },
  { immediate: true }
)
</script>
