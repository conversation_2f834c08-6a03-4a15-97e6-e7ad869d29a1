<template>
  <div class="flex items-center px-15px">
    <div v-for="item in list" :key="item.key" class="text-[15px] h-12 mr-8" @click="onChange(item)">
      <div class="pt-3" :class="item.key === activeIndex ? 'text-[var(--van-primary-color)]' : 'text-[#999]'">{{ item.title }}</div>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  list: {
    type: Array,
    default: () => []
  },
  callback: Function
})

const activeIndex = ref('1');

const onChange = ({ key }) => {
  activeIndex.value = key;
  props.callback(key);
}

</script>