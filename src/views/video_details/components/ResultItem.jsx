import LotteryComponent from "@/views/game_lottery/components/TinsLottery";
import LotteryPopupAreaIssueTip from "../children/lottery_popup_area_issue_tip.vue";

export default defineComponent({
  name: 'ResultItem',
  props: {
    period: String,
    res: String,
    result: String,
    gid: String
  },

  setup(props) {

    const renderItem1 = () => {
      return (
        <div className="flex items-center justify-between py-1.5 px-2.5 rounded-md mb-1 bg-black/[.3]">
          <div className="text-xs">{ props.period }</div>
          <LotteryComponent res={props.res} gid={props.gid} size="mini" />
          <div className="min-w-20 flex justify-end">
            <LotteryPopupAreaIssueTip result={props.result}></LotteryPopupAreaIssueTip>
          </div>
        </div>
      )
    }

    const renderItem6 = () => {
      return (
        <div className="flex items-center justify-between py-1.5 px-2.5 rounded-md mb-1 bg-black/[.3]">
          <div className="text-xs">{ props.period }</div>
          <LotteryComponent res={props.res} gid={props.gid} size="mini" />
        </div>
      )
    }

    const renderItem2 = () => {
      return (
        <div className="flex items-center justify-between py-1.5 px-2.5 rounded-md mb-1 bg-black/[.3]">
          <div className="text-xs">{ props.period }</div>
          <div className="flex justify-center flex-1 h-full">
            <LotteryComponent res={props.res} gid={props.gid} size="mini" />
          </div>
        </div>
      )
    }

    const renderItem3 = () => {
      const getRouletteColor = (number) => {
        const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];
        const num = parseInt(number);
        
        if (num === 0) {
          return 'number-traffic-3';
        } else if (redNumbers.includes(num)) {
          return 'number-traffic-1';
        } else {
          return 'number-traffic-4';
        }
      };

      return (
        <div className="flex items-center justify-between py-1.5 px-2.5 rounded-md mb-1 bg-black/[.3]">
          <div className="text-xs">{ props }</div>
          <div className="flex justify-center flex-1">
            <div class={['number-traffic', getRouletteColor(props.res)]}>{ props.res }</div>
          </div>
          <div className="ml-auto">
            <LotteryPopupAreaIssueTip result={props.result}></LotteryPopupAreaIssueTip>
          </div>
        </div>
      )
    }

    const renderItem4 = () => {
      return (
        <div className="flex items-center py-1.5 px-2.5 rounded-md mb-1 bg-black/[.3]">
          <div className="text-xs">{ props.period }</div>
          <div className="flex flex-col flex-1 items-end">
            <LotteryComponent res={props.res} gid={props.gid} size="mini" />
            <div className="mt-1.5">
              <LotteryPopupAreaIssueTip result={props.result}></LotteryPopupAreaIssueTip>
            </div>
          </div>
        </div>
      )
    }

    const renderItem = () => {
      switch (props.gid) {
        case '-20':
          return <renderItem3></renderItem3>
        case '2':
        case '3':
        case '4':
        case '5':
          return <renderItem4></renderItem4>
        case '6':
        case '7':
          return <renderItem6></renderItem6>
        case '8':
        case '9':
        case '-21':
          return <renderItem2></renderItem2>
        default:
          return <renderItem1></renderItem1>
      }
    }
    return () => {
      return (
        <>
          {renderItem()}
        </>
      )
    }
  }
})