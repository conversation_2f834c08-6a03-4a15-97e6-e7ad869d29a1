<template>
  <van-popup
    :show="show"
    @update:show="onModelValueUpdated"
    destroy-on-close
    close-on-popstate
    position="bottom"
    teleport="body"
    :overlay="false"
    @open="openHandle"
    @closed="closedHandle"
    style="background: rgba(0,0,0,0.9)"
  >
    <div class="h-14 flex items-center justify-around">
      <div v-for="val in toolbar" :key="val.name" @click.stop="clickHandle(val.name)" :class="visibleToobar(val.name)">
        <img v-if="val.name === active" :src="val.active" class="w-44px" />
        <img v-else :src="val.inactive" class="h-9" />
      </div>
    </div>
    <div class="bg-[#333333]/[.6] rounded-t-2xl min-h-374px van-safe-area-bottom">
      <keep-alive>
        <lottery_popup_area
          v-if="active === 'game' || active === 'record' || active === 'result'"
          :active="active"
          :game_id="props.game_id"
          :leaveCallHandle="leaveCallHandle"
          :switchCallHandle="switchCallHandle"
          :explainCallHandle="explainCallHandle"
        />
        <lottery_popup_explain :data="explain" v-else-if="active === 'help'" />
      </keep-alive>
      <keep-alive>
        <lottery_popup_comment
          v-show="active === 'chat'"
          :active="active === 'chat'"
          :game_id="props.game_id"
          :user_id="id"
          @switch-to-game="handleSwitchToGame"
        />
      </keep-alive> 
    </div>
  </van-popup>

  <!-- <lottery_popup_category v-model:show="showCategoryPopup" :game_id="game_id" @switch="switchApplyHandle"></lottery_popup_category> -->
</template>

<script setup>
import { useUserStore } from '@/store/user';
import { storeToRefs } from 'pinia';
import { toolbar } from '../utils';

// import lottery_popup_category from '../children/lottery_popup_category.vue';
import lottery_popup_area from '../children/lottery_popup_area.vue';
import lottery_popup_explain from '../children/lottery_popup_explain.vue';
import lottery_popup_comment from '../children/lottery_popup_comment.vue';

const props = defineProps({
  show: Boolean,
  game_id: Number,
});

const emit = defineEmits(['update:show', 'update:game_id']);
const router = useRouter();
const userStore = useUserStore();

const { id } = storeToRefs(userStore);

const onModelValueUpdated = (val) => {
  emit('update:show', val);
};

const showCategoryPopup = ref(false);
const explain = ref('');
const active = ref('chat');


const clickHandle = (name) => {
  switch (name) {
    case 'close':
      onModelValueUpdated(false);
      break;
    case 'shared':
      onModelValueUpdated(false);
      router.push({ path: '/invite' })
      break;
    case 'record':
      active.value = active.value === name ? "game" : name;
      break;
    case 'result':
      active.value = active.value === name ? "game" : name;
      break;
    case 'help':
      active.value = active.value === name ? "game" : name;
      break;
    case 'chat':
      active.value = active.value === name ? "game" : name;
      break;
    case 'activity':
      onModelValueUpdated(false);
      router.push({ path: '/hd' })
      break;
    default:
      active.value = name;
  }
};


const visibleToobar = (name) => {
  if (active.value === 'activity') {
    if (name === 'help' || name === 'record' || name === 'result') {
      return 'invisible'
    }
  }
  return 'visible'
};

const leaveCallHandle = () => {
  onModelValueUpdated(false);
};

const switchCallHandle = () => {
  // onModelValueUpdated(false);
  // showCategoryPopup.value = true;
};

const explainCallHandle = (data) => {
  explain.value = data;
};

const handleSwitchToGame = () => {
  active.value = 'game';
};

const switchApplyHandle = (game_id) => {
  emit('update:game_id', game_id);
  onModelValueUpdated(true);
};

const openHandle = () => {
  //
};

const closedHandle = () => {
  active.value = 'chat';
  onModelValueUpdated(false);
};
</script>
