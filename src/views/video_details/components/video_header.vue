<template>
  <div class="flex flex-col" style="flex: 0 1">
    <div class="flex flex-col mx-3 my-2.5" v-if="notice">
      <Notice :list="notice"></Notice>
    </div>
    <div class="px-2.5">
      <van-text-ellipsis v-if="title" :content="title" class="text-sm mb-2">
        <template #action="{ expanded }">
          <van-icon color="white" :name="expanded ? 'arrow-up' : 'arrow-down'" />
        </template>
      </van-text-ellipsis>
      <div class="text-[var(--van-primary-color)] text-10px w-full whitespace-nowrap overflow-x-auto scrollable-none">
        <div class="flex items-center">
          <span v-for="val in tagMaps" :key="val.code" class="bg-white/[.1] h-18px leading-[19px] rounded-full px-1 mr-1">
            #{{ val.name }}
          </span>
        </div>
      </div>
      <div class="flex items-center justify-between py-4 text-xs">
        <div class="flex items-center">
          <img src="@/assets/icons/skin_icon_video_play_detail.png" />
          <span class="ml-1.5">{{ formatViews(views) }}</span>
        </div>
        <div class="flex items-center" @click.stop="onCollect">
          <img :src="isCollect ? collectedIcon : collectIcon" />
          <span class="ml-1.5">{{ collectTimes }}</span>
        </div>
        <div class="flex items-center" @click.stop="onLike">
          <img :src="isLiked ? likeedIcon : likeIcon" />
          <span class="ml-1.5">{{ likeTimes }}</span>
        </div>
        <div class="flex items-center" @click.stop="onShare">
          <img src="@/assets/icons/shared.png" />
          <span class="ml-1.5">{{ shareTimes }}</span>
        </div>
      </div>

      <div class="flex items-center justify-between mb-2">
        <div v-for="val in categoryList" :key="val.id" class="flex flex-col items-center justify-center" @click.stop="navigatorPopop(val.id)">
          <div class="w-12 h-12 rounded-md overflow-hidden">
            <ImgComponents :imgUrl="val.icon" />
          </div>
          <p class="text-xs leading-7">{{ val.name }}</p>
        </div>
      </div>

    </div>
    <div v-if="advList && advList.length" class="flex-none px-15px h-65px">
      <Banner :options="advList" />
    </div>
  </div>

  <PopupComponents v-model="visible"></PopupComponents>
</template>

<script setup>
import { useAppStore } from '@/store/app';
import collectIcon from '@/assets/icons/collect.png';
import collectedIcon from '@/assets/icons/collected.png';
import likeIcon from '@/assets/icons/like.png';
import likeedIcon from '@/assets/icons/likeed.png';

import { formatter, getLocalStorage, selectTagsLabel } from '@/utils';
import Notice from "@/components/notice/notice.vue";
import Banner from '@/components/banner/banner.vue';
import PopupComponents from '@/views/shorts/components/popup';
import { userLogged } from '@/hooks';

import { uActions } from '@/api/user';

const props = defineProps({
  vid: Number,
  mediaType: Number,
  title: String,
  isLike: Boolean,
  isCollected: Boolean,
  updatedAt: Number,
  views: Number,
  shareTimes: Number,
  likeTimes: Number,
  collectTimes: Number,
  tags: {
    type: Array,
    default: () => []
  },
  categoryList: {
    type: Array,
    default: () => []
  }
});

const appStore = useAppStore();
const emit = defineEmits(['update:shareTimes', 'update:likeTimes', 'update:collectTimes', 'open-popup'])
let tagMaps = [];

const notice = computed(() => appStore.adItem(22));
const advList = computed(() => appStore.adItem(12));

const visible = ref(false);
const isLiked = ref(props.isLike ?? false);
const isCollect = ref(props.isCollected ?? false);

const formatViews = (num) => {
  return num > 9999 ? Math.floor(num / 1000) / 10 + "万" : num;
};

const onCollect = async () => {
  isCollect.value = !isCollect.value;
  emit('update:collectTimes', isCollect.value ? props.collectTimes + 1 : props.collectTimes - 1);
  const ed = new Map([
    [1, [10, 12]],
    [2, [15, 16]],
  ]);

  const edt = ed.get(props.mediaType);

  const res = await uActions({ actionType: isCollect.value ? edt[0] : edt[1], eventId: props.vid });

  if (res) {
    showSuccessToast(isCollect.value ? '收藏成功' : '取消收藏');
  }
};

const onLike = async () => {
  isLiked.value = !isLiked.value;
  emit('update:likeTimes', isLiked.value ? props.likeTimes + 1 : props.likeTimes - 1);
  const ed = new Map([
    [1, [17, 18]],
    [2, [19, 20]],
  ]);
  const edt = ed.get(props.mediaType);
  const res = await uActions({ actionType: isLiked.value ? edt[0] : edt[1], eventId: props.vid });

  if (res) {
    showSuccessToast(isLiked.value ? '点赞成功' : '取消点赞');
  }
}

const onShare = async () => {
  visible.value = true;
  emit('update:shareTimes', props.shareTimes + 1);
  await uActions({ actionType: 23, eventId: props.vid });
};

const isPopupOpening = ref(false);

const navigatorPopop = (game_id) => {
  if (isPopupOpening.value) return; // 已经在弹窗，直接返回
  isPopupOpening.value = true;
  emit('open-popup', game_id);
  setTimeout(() => {
    isPopupOpening.value = false;
  }, 500); // 500ms内不允许再次点击
}

watch(
  () => props.tags,
  (val) => {
    tagMaps = selectTagsLabel(appStore.tagList, val);
  }
)

watch(() => props.isCollected, (newVal) => {
  isCollect.value = newVal;
});

watch(() => props.isLike, (newVal) => {
  isLiked.value = newVal;
});
</script>
