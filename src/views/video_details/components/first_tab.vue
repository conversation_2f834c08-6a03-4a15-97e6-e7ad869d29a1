<template>
  <div class="px-3">
    <van-pullRefresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        v-model:error="error"
        :finished="finished"
        :finished-text="finishedText"
        @load="onLoad"
        :immediate-check="false"
      >
        <div class="grid grid-cols-2 gap-2.5">
          <MiddleVideoCover
            v-for="val in list"
            :key="val.id"
            :title="val.title"
            :tags="val.tags"
            :imgUrl="val.videoCover"
            :views="val.viewTimes"
            :time="val.videoDuration"
            :vid="val.id"
            :type="val.type"
          />
        </div>
      </van-list>
    </van-pullRefresh>
  </div>
</template>

<script setup>
import { useList } from "@/hooks";
import { editedVideoForOneTheme } from "@/api/home";
import MiddleVideoCover from "@/components/video/MiddleVideoCover";

const {
  query: { tid, cid },
} = useRoute();

const listParams = reactive({
  themeId: parseInt(tid),
  categoryId: parseFloat(cid),
});

const {
  list,
  refreshing,
  loading,
  error,
  finished,
  finishedText,
  onRefresh,
  onLoad,
} = useList({
  serverHandle: editedVideoForOneTheme,
  immediateParams: listParams,
});

onMounted(() => {
  onRefresh()
})

</script>