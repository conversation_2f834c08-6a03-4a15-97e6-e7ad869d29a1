<template>
  <div class="flex-initial van-safe-area-top">
    <div class="bg-[var(--van-black)] aspect-w-16 aspect-h-9 relative">
      <div class="flex absolute inset-0 w-full h-full overflow-hidden">
        <video ref="videoRef" :id="`player-container-${vid}`" class="w-full h-full" x5-playsinline="true" playsinline webkit-playsinline="true"></video>
        <div class="flex items-center absolute top-50% left-50% touch-control" style="transform: translate(-50%, -50%)">
          <button class="h-11 w-11 text-center border-0 rounded-full bg-black/[0.5]" @click.stop="onPlayTime(-15)">
            <img src="@/assets/icons/<EMAIL>" alt="" />
          </button>
          <button class="h-11 w-11 text-center border-0 p-2.5 rounded-full bg-black/[0.5] mx-4" @click.stop="onToggle">
            <svg-icon :name="state.play ? 'pause' : 'play_arrow'"></svg-icon>
          </button>
          <button class="h-11 w-11 text-center border-0 rounded-full bg-black/[0.5]" @click.stop="onPlayTime(15)">
            <img src="@/assets/icons/<EMAIL>" alt="" />
          </button>
        </div>
      </div>

      <div v-if="!renderTp" class="absolute top-0 left-2 inline-flex items-center h-10 w-3/6 z-10" @click.stop="onClickLeft">
        <van-icon name="arrow-left" color="#fff" size="20" />
        <span class="text-white text-xs line-clamp-1">{{ title }}</span>
      </div>

      <div v-if="renderLg" class="flex absolute w-full h-full items-center justify-center">
        <div class="flex flex-col items-center justify-center w-full h-full bg-black/[0.8]">
          <div class="text-xs text-white mb-4">
            <p>升级VIP可观看视频，每日无限观影~</p>
          </div>
          <van-space>
            <van-button type="primary" round size="small" class="w-[120px]" :to="{ path: '/invite' }" plain>推广送VIP</van-button>
            <van-button type="primary" round size="small" class="w-[120px]" :to="{ path: '/vip' }">升级VIP</van-button>
          </van-space>
        </div>
      </div>
      <VideoBefore v-if="renderTp" @click="clickHandle" @ended="endedHandle"></VideoBefore>
    </div>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/store/user';
import VideoBefore from './video_before.vue';
import ReactiveVideo from '@/hooks/video';
import { durationSeconds } from '@/utils';
import { uActions } from '@/api/user';
import { key, iv } from '@/constant/img';
import { generatepsign } from '@/utils/psign';
import { getImageBase64 } from '@/utils/decrypto';

const props = defineProps({
  vid: Number,
  type: Number,
  mediaType: Number,
  cover: String,
  title: String,
  isAllowLong: Boolean,
  isAllowShort: Boolean,
  isCloudMode: Boolean,
  cloudFileId: String,
  playUrl: String,
  cloudUrl: String
});
const router = useRouter();
const userStore = useUserStore();
const { vipLevel } = storeToRefs(userStore);

const playerContainerId = computed(() => `player-container-${props.vid}`);
const videoRef = ref(null);
// 播放时长跟踪变量
let totalPlayedTime = 0;
let lastTrackedTime = 0;
let player = null;
const TIME_JUMP_THRESHOLD = 2; // 超过 2 秒的跳跃视为快进

const [play, toggle] = useToggle(false);

const state = reactive({
  play: false,
  pause: true
});

const current = ref(0);

const duration = ref(0);

const percentage = ref(0);
// 是否全屏
const fullscreen = ref(false);
// 视频加载完成
const loaded = ref(false);
// 操作栏
const renderControl = ref(true);
// 显示广告
const renderTp = ref(true);
// 是否无次数播放
const renderLg = ref(false);

const onClickLeft = () => history.back();

const onToggle = () => {
  if (state.play) {
    player?.pause();
  } else {
    player?.play();
  }
};

const setCurrentTime = (time) => {
  const current = player.currentTime();
  const duration = player.duration();
  let _time = current + time;

  if (_time < 0) _time = 0;
  if (_time > duration) _time = duration - 1;

  player?.currentTime(_time);
};

const onPlayTime = (time) => {
  setCurrentTime(time);
};

const clickHandle = () => {
  if (vipLevel.value > 0) {
    endedHandle();
  } else {
    router.push({ path: '/vip' });
  }
};

const endedHandle = () => {
  renderTp.value = false;
  nextTick(() => {
    const allow = props.mediaType === 1 ? props.isAllowLong : props.isAllowShort;
    if (vipLevel.value > 0 || allow) {
      videoLicense();
    } else {
      renderLg.value = true;
    }
  });
};

const videoLicense = () => {
  try {
    const opts = {
      licenseUrl: import.meta.env.VITE_GLOB_APP_PLAY_LICENSE_URL,
      bigPlayButton: false,
      autoplay: true,
      controlBar: {
        playToggle: true,
        progressControl: true,
        volumePanel: false,
        currentTimeDisplay: true,
        durationDisplay: true,
        timeDivider: true,
        playbackRateMenuButton: false,
        fullscreenToggle: true,
        QualitySwitcherMenuButton: false
      }
    };

    if (props.isCloudMode) {
      opts.fileID = props.cloudFileId;
      opts.appID = import.meta.env.VITE_APP_PLAY_APP_ID;
      opts.psign = generatepsign(props.cloudFileId, props.playUrl);
    } else {
      opts.sources = [{ src: props.cloudUrl }];
    };

    if (player) {
      console.log(player);
      player.dispose();
      player = null;
    };

    player = window.TCPlayer(playerContainerId.value, opts);

    player.on('loadedmetadata', () => {
      //
    });

    player.on('loadeddata', () => {
      console.log('loadeddata');
      player.play();
    });

    player.on('timeupdate', () => {
      //
    });

    player.on('play', () => {
      state.play = true;
      state.pause = false;
      lastTrackedTime = player?.currentTime();
    });

    player.on('fullscreenchange', (val) => {
      fullscreen.value = player?.isFullscreen();
    });

    player.on('pause', () => {
      state.play = false;
      state.pause = true;
    });

    player.on('ended', () => {
      //
    });

    player.on('timeupdate', () => {
      const current = player.currentTime();
      const delta = Math.abs(current - lastTrackedTime);

      if (delta <= TIME_JUMP_THRESHOLD) {
        // 用户正常观看
        totalPlayedTime += delta;
      } else {
        // 快进了，忽略这段时间
        console.log(`[快进] 跳过 ${delta.toFixed(2)} 秒`);
      }

      lastTrackedTime = current;
    });

    getImageBase64(props.videoCover, key, iv)
      .then((res) => {
        player.poster(res);
      })
      .catch(() => {
        player.poster('');
      });
  } catch (error) {
    console.log(error);
  }
};

defineExpose({
  play: () => player?.play(),
  pause: () => player?.pause(),
});

onBeforeUnmount(() => {
  const watchingTime = Math.floor(totalPlayedTime);
  if (watchingTime) uActions({ actionType: props.mediaType === 1 ? 3 : 4, eventId: props.vid, watchingTime });
});

onUnmounted(() => {
  console.log('onUnmounted');
  player?.dispose();
  player = null;
});

</script>

<style lang="less">
.touch-control {
  opacity: 1;
  visibility: visible;
  transition:
    visibility 1s,
    opacity 1s;
}

.vjs-has-started.vjs-user-inactive.vjs-playing + .touch-control {
  opacity: 0;
  visibility: hidden;
}
</style>
