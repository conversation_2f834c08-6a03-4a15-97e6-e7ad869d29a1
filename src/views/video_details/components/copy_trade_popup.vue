<script setup>
import { ref, computed, watch, watchEffect, nextTick, toRaw } from 'vue';
import { showToast } from 'vant';
import useClipboard from 'vue-clipboard3';
import { useListExtra, useLotteryClaimant, useFollowPlanWaysConfigQuery, useFollowPlanBetDataQuery, useLottery } from '@/hooks';
import { GetLotteryBetRecordAPI, FollowPlanBetDataAPI, FollowPlanWaysConfigAPI } from '@/api/game';
import { formatter, parseDate } from '@/utils';
import CopyTradePopupGameInfo from '../children/copy_trade_popup_game_info.vue';
import CopyTradePopupList from '../children/copy_trade_popup_list.vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  game_id: {
    type: Number,
    required: true
  },
  isStop: {
    type: Boolean,
    default: false
  },
  time: {
    type: Number,
    default: 0
  },
  lastPeriod: {
    type: Object,
    default: () => ({})
  },
  gameInfo: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:show']);

const { toClipboard } = useClipboard();

// Reactive data
const selectedBetType = ref('总和大小');
const selectedBetTypeValue = ref('total_size');
const copyTradeList = ref([]);
// Create a reactive game_id that the composable can watch
const reactiveGameId = computed(() => props.game_id);
const selectedBetOption = ref(null);

// Reactive parameters for bet data query
const playWayCode = computed(() => {
  return selectedBetOption.value?.code;
});
const pageSortKey = ref('');

const { onSelect } = useLottery({
  game_id: props.game_id
});

// Use configuration query composable to fetch available options
const {
  configOptions,
  isLoading: configOptionsLoading,
  isError: configOptionsError,
  error: configOptionsErrorDetails
} = useFollowPlanWaysConfigQuery(reactiveGameId);

// Use bet data query composable to fetch copy trade betting data
const {
  betData,
  isLoading: betDataLoading,
  isError: betDataError,
  error: betDataErrorDetails,
  refetch: refetchBetData
} = useFollowPlanBetDataQuery(reactiveGameId, playWayCode, pageSortKey);

const betList = computed(() => {
  return betData.value?.lists || [];
});

const runningPeriod = computed(() => {
  return betData.value?.lists?.[0];
});

watch(
  () => configOptionsErrorDetails,
  (error) => {
    if (error) {
      showToast(error);
    }
  }
);

// Watch for bet data errors
watch(
  () => betDataErrorDetails.value,
  (error) => {
    if (error) {
      console.error('Bet data fetch error:', error);
      showToast(error.message || 'Failed to fetch bet data');
    }
  }
);

watch(
  () => configOptions.value,
  (options) => {
    const rawOptions = toRaw(options);
    if (rawOptions && rawOptions.length > 0) {
      selectedBetOption.value = rawOptions[0];
    }
  },
  { immediate: true }
);

// Methods
const onModelValueUpdated = (val) => {
  emit('update:show', val);
};

const finishHandle = () => {
  refetchBetData();
};

const stopHandle = () => {
  refetchBetData();
};

const onCopyBet = (betItem) => {
  onSelect({
    type_id: betItem.gameTypeId,
    how_id: betItem.gameHowId
  });
  emit('update:show', false);
};

const onCopyRevertBet = (betItem) => {
  onSelect({
    type_id: betItem.reverseGameType,
    how_id: betItem.gameHowId
  });
  emit('update:show', false);
};

</script>

<template>
  <van-popup
    :show="show"
    position="bottom"
    teleport="body"
    destroy-on-close
    safe-area-inset-bottom
    :overlay-style="{ background: 'transparent' }"
    style="background: rgba(0, 0, 0, 0.9)"
    @update:show="onModelValueUpdated"
  >
    <div class="bg-[#333333]/[.6] rounded-t-2xl max-h-[90vh] flex flex-col text-white van-safe-area-bottom">
      <!-- Header -->
      <div class="flex items-center bg-[#07b4df] px-4 py-2 rounded-t-2xl relative">
        <van-icon name="arrow-left" size="18" class="!absolute left-[10px] mr-4 text-white" @click="onModelValueUpdated(false)" />
        <div class="w-full text-center">
          <span class="font-medium text-white">跟单计划</span>
        </div>
      </div>
      <!-- Content -->
      <div class="p-2 overflow-y-auto">
        <!-- Game Info Section -->
        <CopyTradePopupGameInfo
          :game_id="reactiveGameId"
          :game-info="gameInfo"
          :last-period="lastPeriod"
          :current-period="runningPeriod"
          :time="time"
          :is-stop="isStop"
          :isLoadingOptions="configOptionsLoading"
          :errorOptionsMessage="configOptionsErrorDetails?.message"
          :options="configOptions"
          :finish="finishHandle"
          :stop="stopHandle"
          v-model:selectedOption="selectedBetOption"
        />

        <div class="mb-2"></div>

        <!-- Data Table -->
        <CopyTradePopupList
          :bet-list="betList"
          :loading="betDataLoading"
          :error="betDataError"
          @copy-bet="onCopyBet"
          @copy-revert-bet="onCopyRevertBet"
        />
      </div>
    </div>
  </van-popup>
</template>

<style scoped></style>
