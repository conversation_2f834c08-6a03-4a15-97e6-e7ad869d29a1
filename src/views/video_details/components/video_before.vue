<template>
  <div v-if="option" class="flex absolute w-full h-full">
    <div class="w-full h-full" @click.stop="goRedirect">
      <ImgComponents :imgUrl="option.picUrl" @load="onLoad" />
    </div>
    <div
      v-if="isActive"
      @click="clickHandle"
      class="absolute right-4 top-2 flex items-center justify-center rounded-full h-8 w-20 border-2 border-white bg-black text-white"
    >
      <span class="text-xs">
        <template v-if="isActive">VIP跳过{{ t }}s</template>
        <template v-else>&#10005;</template>
      </span>
    </div>
  </div>
</template>

<script setup>
import { uActions } from '@/api/user';
import { useNavigate } from '@/hooks';
import { useAppStore } from '@/store/app';
import { isEmpty } from '@/utils';

const props = defineProps({
  position: String,
  name: String,
});

const emit = defineEmits(['ended', 'click']);

const { navigateTo } = useNavigate();

const t = ref(5);

const { pause, isActive } = useIntervalFn(() => {
  if (t.value > 1) t.value -= 1;
  else pause();
}, 1000);

const appStore = useAppStore();

const option = computed(() => {
  const ad = appStore.adItem(7);

  if (!isEmpty(ad)) {
    const len = ad.length;
    return ad[Math.floor(Math.random() * len)];
  }
});

watch(isActive, (val) => {
  if (!val) emit('ended');
});

const clickHandle = () => {
  emit('click');
}

const goRedirect = async () => {
  navigateTo(option.value.content, option.value.jumpType);
  await uActions({ actionType: 11, eventId: option.value.id });
};

const onLoad = async () => {
  await uActions({ actionType: 5, eventId: option.value.id });
};
</script>
