<template>
  <div class="h-135px overflow-hidden mb-2.5 rounded-md">
    <div class="h-full overflow-auto">
      <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
        <ResultItemComponent
          v-for="val in list"
          :key="val.period"
          :period="val.period"
          :res="val.res"
          :result="val.res_tips"
          :gid="String(game_id)"
        />
      </van-list>
    </div>
  </div>
</template>

<script setup>
import { useListExtra } from '@/hooks';
import { GetTicketRecordAPI } from '@/api/game';

import ResultItemComponent from '../components/ResultItem';

const props = defineProps({
  game_id: Number
});

const { list, refreshing, onRefresh, loading, error, finished, onLoad } = useListExtra({
  serverHandle: GetTicketRecordAPI,
  implementationGetParams: () => ({ game_id: props.game_id }),
  limit: 10
});

defineExpose({
  onRefresh: onRefresh
})

</script>