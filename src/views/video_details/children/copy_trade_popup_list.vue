<script setup>
import { useLottery } from '@/hooks';

const props = defineProps({
  betList: {
    type: Array,
    default: () => []
  },
  loading: Boolean,
  error: <PERSON><PERSON><PERSON>
});

const emit = defineEmits(['copy-bet', 'copy-revert-bet']);

// Tag color functions for van-tag components
const getBetItemTagColor = (item) => {
  if (item === '大' || item === '单') return '#3b82f6'; // blue-500
  if (item === '小' || item === '双') return '#ef4444'; // red-500
  return '#6b7280'; // gray-500
};

const getResultTagColor = (result) => {
  switch (result) {
    case 'Waiting':
    case 'Win':
      return '#ff0000';
    case 'Lose':
      return '#07b4df';
    case 'Drawing':
      return '#00ff00';
    default:
      return 'transparent';
  }
};

const getResultText = (result) => {
  switch (result) {
    case 'Waiting':
      return '等开';
    case 'Win':
      return '赢';
    case 'Lose':
      return '输';
    case 'Drawing':
      return '开奖中';
    default:
      return '';
  }
};

// Helper function to get period digits for display
const getPeriodDigits = (period) => {
  // Extract last 4 digits from period for display
  const periodStr = String(period);
  const lastFour = periodStr.slice(-4);
  return lastFour.split('');
};

const copyBet = (betItem) => {
  emit('copy-bet', toRaw(betItem));
};

const copyRevertBet = (betItem) => {
  emit('copy-revert-bet', toRaw(betItem));
};
</script>

<template>
  <div class="flex-1 overflow-hidden min-h-0">
    <!-- Table Header -->
    <div class="border border-[#9d9d9d] rounded-t-lg border-solid flex items-center text-xs text-white sticky top-0 z-10">
      <div class="flex-1 text-center font-medium border-r border-solid border-[#9d9d9d] px-2 py-2">期号</div>
      <div class="flex-1 text-center font-medium border-r border-solid border-[#9d9d9d] px-2 py-2">玩法</div>
      <div class="flex-1 text-center font-medium border-r border-solid border-[#9d9d9d] px-2 py-2">投注项</div>
      <div class="flex-1 text-center font-medium border-r border-solid border-[#9d9d9d] px-2 py-2">结果</div>
      <div class="flex-1 text-center font-medium px-2 py-2">操作</div>
    </div>

    <div class="mb-1"></div>

    <!-- Table Content -->
    <div style="max-height: 260px;" class="border-t border-[#9d9d9d] border-solid flex-1 overflow-auto min-h-0 overflow-y-scroll">
      <!-- Loading State -->
      <div v-if="props.loading && props.betList.length === 0" class="flex items-center justify-center py-8">
        <van-loading size="24px" color="#07b4df">加载中...</van-loading>
      </div>

      <!-- Empty State -->
      <div v-else-if="!props.loading && props.betList.length === 0" class="flex items-center justify-center py-8">
        <van-empty description="暂无跟单数据" />
      </div>

      <!-- Data List -->
      <van-list v-else :loading="props.loading" :error="props.error" :finished="!props.loading">
        <div
          v-for="(item, index) in props.betList"
          :key="item.period"
          class="border border-[#9d9d9d] border-solid border-t-0 flex items-center text-xs"
        >
          <!-- Period -->
          <div class="flex-1 text-center border-r border-solid border-[#9d9d9d] px-1 py-2">
            <div class="flex justify-center">
              <span v-for="digit in getPeriodDigits(item.period)" :key="digit" class="bg-[#ff4444] w-4 h-4 rounded-full text-xs mr-[1px]">
                {{ digit }}
              </span>
            </div>
          </div>

          <!-- Play Method -->
          <div class="flex-1 text-center whitespace-nowrap border-r border-solid border-[#9d9d9d] px-1 py-2">
            <span class="text-white">{{ item.selfDefinePlayWayName }}</span>
          </div>

          <!-- Bet Item -->
          <div class="flex-1 text-center border-r border-solid border-[#9d9d9d] px-1 py-2">
            <span class="font-bold color-[#05f9bd]">{{ item.gameTypeName }}</span>
          </div>

          <!-- Result -->
          <div class="flex-1 text-center border-r border-solid border-[#9d9d9d] px-1 py-2">
            <span :style="{ color: getResultTagColor(item.result) }" class="font-bold color-[#05f9bd]">{{
              getResultText(item.result)
            }}</span>
          </div>

          <!-- Actions -->
          <div class="flex-1 text-center w-[100px] min-w-[100px] px-1 py-2">
            <div v-if="item.result === 'Waiting'" class="flex justify-center space-x-1">
              <van-button
                size="mini"
                color="#ff4444"
                round
                :loading="props.followBetLoading"
                :disabled="props.followBetLoading"
                class="!px-1 !py-0 !text-xs !font-medium !min-w-[40px] !h-[20px] text-white"
                @click="copyBet(item)"
              >
                跟投
              </van-button>
              <van-button
                size="mini"
                color="#fad825"
                round
                class="!px-1 !py-0 !text-xs !font-medium !min-w-[40px] !h-[20px] text-white"
                @click="copyRevertBet(item)"
              >
                反投
              </van-button>
            </div>
          </div>
        </div>
      </van-list>
    </div>
  </div>
</template>
