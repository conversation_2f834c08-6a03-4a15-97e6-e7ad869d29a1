<template>
  <div  class="w-full h-full text-sm overflow-y-auto scrollbar-none" :class="itemClasses(cid)">
    <div
      v-for="(val, i) in list"
      :key="val.id"
      :data-class-id="val.id"
      class="u-bg u-bg-class flex flex-col items-center justify-center font-bold rounded-lg relative active:bg-[#07b4df]/[.6]"
      :class="[
        childClasses(val.id),
        cid === 18 && getLotteryColor(val.name),
        cid === 36 && getRouletteColor(val.name),
      ]"
      @click.stop="selectHandle(val.id)"
    >
      <AreaContentName :nid="val.id" :cid="cid" :code="val.code" :name="val.name"></AreaContentName>
      <div class="text-xs text-align w-6 text-center text-current	">{{ val.odd }}</div>
      <div v-if="findSelect(cid, val.id)" class="absolute top-1 left-1 flex items-center justify-center h-3 rounded-full px-2 text-white  bg-black/[.5]">
        <img :src="preselectSVG" class="h-2.5">
        <span class="text-8px ">{{ findSelect(cid, val.id) }}</span>
      </div>
      <div v-if="findPreSelect(cid, val.id)" class="absolute bottom-1 flex items-center justify-center h-3 rounded-full px-2 text-white  bg-black/[.5]">
        <img :src="preselectSVG" class="h-2.5">
        <span class="text-8px leading-3	">{{ findPreSelect(cid, val.id) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getLotteryColor, getRouletteColor } from '@/utils/game';
import AreaContentName from '@/views/game_lottery/components/AreaContentName.vue';
import preselectSVG from '@/icons/preselect.svg';
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  selected: {
    type: Array,
    default: () => [],
  },
  preselected: {
    type: Array,
    default: () => [],
  },
  gid: [Number, String],
  cid: Number
});

const emit = defineEmits(['select']);

const itemClasses = (classId) => {
  switch (classId) {
    case 1:
    case 8:
    case 9:
    case 10:
    case 14:
    case 15:
      return "grid grid-cols-4 gap-1 !text-2xl";
    case 5:
    case 12:
    case 22:
    case 23:
      return "grid grid-cols-3 gap-1 !text-2xl auto-rows-[minmax(0,_2fr)]";
    case 24:
    case 25:
      return "grid grid-cols-2 gap-1 !text-2xl";
    case 2:
    case 3:
    case 4:
    case 19:
    case 20:
      return "grid grid-cols-3 gap-1 auto-rows-[minmax(0,_2fr)]";
    case 17:
      return "grid grid-cols-4 gap-1 auto-rows-[minmax(0,_2fr)]";
    case 6:
    case 7:
    case 21:
      return "grid grid-cols-5 gap-1 auto-rows-[minmax(0,_2fr)]";
    case 11:
    case 13:
    case 26:
    case 27:
      return "grid grid-cols-6 gap-1 auto-rows-[minmax(0,_2fr)]";
    case 16:
      return "grid grid-cols-7 gap-1 auto-rows-[minmax(0,_2fr)]";
    case 18:
      return "grid grid-cols-7 gap-1 auto-rows-72px";
    default:
      return "grid grid-cols-2 gap-1";
  }
};

const childClasses = (classId) => {
  switch (classId) {
    case 1:
    case 2:
    case 46:
    case 47:
    case 52:
    case 53:
    case 56:
    case 57:
    case 71:
    case 86:
    case 87:
    case 90:
    case 91:
    case 184:
      return "text-green";
    case 3:
    case 4:
    case 19:
    case 22:
    case 25:
    case 48:
    case 49:
    case 50:
    case 51:
    case 54:
    case 55:
    case 84:
    case 85:
    case 88:
    case 89:
    case 189:
      return "text-orange";
    case 17:
    case 20:
    case 23:
    case 70:
    case 187:
    case 191:
    case 192:
    case 186:
      return "text-red";
    case 18:
    case 21:
    case 24:
    case 72:
    case 188:
    case 190:
    case 193:
      return "text-blue";
    case 112:
      return "text-green col-span-2";
    default:
      return "";
  }
};

const selectHandle = (how_id) => {
  emit('select', how_id)
};

const preselectedMap = computed(() => {
  const map = new Map();
  for (const item of props.preselected) {
    if (item && item.bet_data) {
      for (const bet of item.bet_data) {
        map.set(`${item.how_id}_${bet.type_id}`, bet.point);
      }
    }
  }
  return map;
});

const findPreSelect = (how_id, type_id) => {
  return preselectedMap.value.get(`${how_id}_${type_id}`);
};

const selectedMap = computed(() => {
  const map = new Map();
  for (const item of props.selected) {
    if (item && item.bet_data) {
      for (const bet of item.bet_data) {
        map.set(`${item.how_id}_${bet.type_id}`, bet.point);
      }
    }
  }
  return map;
});

const findSelect = (how_id, type_id) => {
  return selectedMap.value.get(`${how_id}_${type_id}`);
}

</script>

<style lang="less" scoped>
.text-green {
  color: #09ff77;
}

.text-red {
  color: #ff286a;
}

.text-orange {
  color: #ffbe22;
}

.text-blue {
  color: #02dbfb;
}

</style>