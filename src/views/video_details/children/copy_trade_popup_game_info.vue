<script setup>
import { ref, computed } from 'vue';
import { showToast } from 'vant';
import LotteryComponent from '@/views/game_lottery/components/TinsLottery';
import LotteryPopupAreaIssueTip from './lottery_popup_area_issue_tip.vue';

const props = defineProps({
  game_id: {
    type: Number,
    required: true
  },
  gameInfo: {
    type: Object,
    default: () => ({})
  },
  lastPeriod: {
    type: Object,
    default: () => ({})
  },
  currentPeriod: {
    type: Object,
    default: () => ({})
  },
  time: {
    type: Number,
    default: 0
  },
  isStop: {
    type: Boolean,
    default: false
  },
  isLoadingOptions: {
    type: Boolean,
    default: false
  },
  errorOptionsMessage: {
    type: String,
    default: ''
  },
  options: {
    type: Array,
    default: () => []
  },
  selectedOption: {
    type: Object,
    default: null
  },
  finish: {
    type: Function,
    required: true
  },
  stop: {
    type: Function,
    required: true
  }
});

const emit = defineEmits(['update:selectedOption']);

const isSelectingOption = ref(false);

const reactiveGameId = computed(() => {
  return props.game_id;
});

// Computed properties for better state management
const hasOptions = computed(() => props.options && props.options.length > 0);
const isOptionsReady = computed(() => !props.isLoadingOptions && hasOptions.value);
const shouldShowDropdown = computed(() => isOptionsReady.value && !props.errorOptionsMessage);

// Loading state computed property
const optionsLoadingState = computed(() => {
  if (props.isLoadingOptions) return 'loading';
  if (props.errorOptionsMessage) return 'error';
  if (!hasOptions.value) return 'empty';
  return 'ready';
});

const onSelectOption = (option) => {
  if (!option) return;

  try {
    emit('update:selectedOption', option);
    isSelectingOption.value = false;
    showToast(`已选择: ${option.name}`);
  } catch (error) {
    console.error('Error selecting option:', error);
    showToast('选择失败，请重试');
  }
};

const currentPeriod = computed(() => {
  return props.currentPeriod?.period || '';
});

const currentPeriodShort = computed(() => {
  return props.currentPeriod?.periodShort;
});

const lastResult = computed(() => {
  if (!props.lastPeriod?.res) return [1, 0, 2]; // Default fallback
  return props.lastPeriod.res.split('-').map(Number);
});

const getNumberColor = (num) => {
  if (num === 0) return 'bg-green-500';
  if ([1, 3, 7, 9].includes(num)) return 'bg-red-500';
  return 'bg-blue-500';
};

const onBetTypeChange = (value) => {
  selectedBetTypeValue.value = value;
  // Refresh data based on new bet type
  onRefresh();
};

const onCountDownFinish = () => {
  if (props.isStop) {
    props?.stop();
  } else {
    props?.finish();
  }
};

const onCountDownStop = () => {
  props.stop();
};

// Handle error retry for options loading
const onRetryLoadOptions = () => {
  // This would typically trigger a refetch from the parent component
  // For now, we'll just show a toast
  showToast('正在重新加载选项...');
};

// Check if an option is currently selected
const isOptionSelected = (option) => {
  if (!props.selectedOption || !option) return false;

  // Compare by code first, then by value, then by name as fallback
  return (
    (option.code && props.selectedOption.code && option.code === props.selectedOption.code) ||
    (option.value && props.selectedOption.value && option.value === props.selectedOption.value) ||
    (option.name && props.selectedOption.name && option.name === props.selectedOption.name)
  );
};
</script>

<template>
  <div class="border border-[#9d9d9d] rounded-lg border-solid px-2 pt-2">
    <!-- Game name and period -->
    <div class="mb-1 flex items-center justify-between">
      <div class="flex items-center font-bold">
        <span class="text-white">{{ props.gameInfo?.name }}</span>
        <span class="mx-1 text-white">第</span>
        <span class="text-[#f5c400]">{{ currentPeriod }}</span>
        <span class="ml-1 text-white">期</span>
      </div>
      <div class="flex items-center text-xs">
        <van-icon name="clock-o" size="14" class="mr-1 text-white" />
        <span>{{ props.isStop ? '本期封盘' : '本期截止' }}:&nbsp;</span>
        <van-count-down
          millisecond
          format="mm:ss"
          @finish="props.finish"
          :time="props.time * 1000"
          :class="['font-medium', { '!text-red-500': isStop }]"
        ></van-count-down>
      </div>
    </div>

    <!-- Lottery Result Display -->
    <div class="mb-1 flex items-center justify-between flex-wrap gap-[4px]">
      <!-- Result balls -->
      <div class="flex flex-col items-start gap-[4px]">
        <LotteryComponent :res="props.lastPeriod.res" :gid="String(reactiveGameId)" :animated="props.isStop"></LotteryComponent>
        <LotteryPopupAreaIssueTip :result="props.lastPeriod?.res_tips"></LotteryPopupAreaIssueTip>
      </div>

      <!-- Dropdown for bet type -->
      <div class="relative">
        <!-- Loading state for options -->
        <div v-if="props.isLoadingOptions" class="px-2 py-1 border-none rounded bg-[#07b4df] w-[100px] flex items-center justify-center">
          <van-loading size="16px" color="white" />
          <span class="ml-1 text-white text-sm">加载中</span>
        </div>

        <!-- Error state for options -->
        <div
          v-else-if="props.errorOptionsMessage"
          class="px-2 py-1 border-none rounded bg-red-500 w-[100px] flex items-center justify-center cursor-pointer hover:bg-red-600 transition-colors"
          @click="onRetryLoadOptions"
          :title="props.errorOptionsMessage"
        >
          <van-icon name="warning-o" size="16" color="white" />
          <span class="ml-1 text-white text-sm">重试</span>
        </div>

        <!-- Normal dropdown when options are loaded -->
        <van-popover v-else-if="props.options && props.options.length > 0" v-model:show="isSelectingOption">
          <div
            :class="[
              'px-1 py-1 text-white w-[100px] text-center transition-colors cursor-pointer',
              isOptionSelected(option)
                ? 'bg-[#045a73] border-l-2 border-[#f5c400]'
                : 'bg-[#07b4df] hover:bg-[#0599c2]'
            ]"
            v-for="option in props.options"
            :key="option.code || option.value"
            @click="onSelectOption(option)"
          >
            <span :class="{ 'font-semibold': isOptionSelected(option) }">
              {{ option.name }}
            </span>
            <van-icon
              v-if="isOptionSelected(option)"
              name="success"
              size="14"
              color="#f5c400"
              class="ml-1 inline-block"
            />
          </div>
          <template #reference>
            <button
              size="small"
              :class="[
                'px-1 py-1 border-none rounded w-[100px] transition-colors',
                props.selectedOption
                  ? 'bg-[#045a73] hover:bg-[#034a63] border-l-2 border-[#f5c400]'
                  : 'bg-[#07b4df] hover:bg-[#0599c2]'
              ]"
              :disabled="props.isLoadingOptions"
            >
              <span class="flex justify-center items-center gap-1 text-white text-xs">
                <span :class="{ 'font-semibold': props.selectedOption }">
                  {{ props.selectedOption?.name }}
                </span>
                <van-icon
                  size="20"
                  :name="isSelectingOption ? 'arrow-up' : 'arrow-down'"
                  :class="{ 'text-[#f5c400]': props.selectedOption }"
                />
              </span>
            </button>
          </template>
        </van-popover>

        <!-- Empty state when no options available -->
        <div v-else class="px-2 py-1 border-none rounded bg-gray-500 w-[100px] flex items-center justify-center">
          <span class="text-white text-sm">暂无选项</span>
        </div>
      </div>
    </div>
    <div class="flex justify-center">
      <div class="bg-[#07b4df] text-white px-2 py-1 rounded-t-[10px] text-xs">总和大小 {{ currentPeriodShort }} 期：大</div>
    </div>
  </div>
</template>
