<template>
  <p>
    <span class="bg-[#1372fc] text-white px-1.5 py-0.5 rounded-md mr-1 inline" style="font-size: 12px"> 系统 </span>
    {{ content }}
    <span class="bg-[red] text-white px-1.5 py-0.5 rounded-md ml-1 inline-flex items-center mt-0.5">
      <span class="inline" style="font-size: 12px"> 跟投 </span>
      <van-icon name="arrow-left" style="transform: rotate(180deg)" color="#fff" size="14px" @click="handleCopyBet" />
    </span>
  </p>
</template>

<script setup lang="ts">
import { useLottery } from '@/hooks';
import type { BettingWsMessage } from '@/types/comment';
import { computed } from 'vue';

interface Props {
  comment: BettingWsMessage;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  switchToGame: [];
}>();

// Extract data from WebSocket message
const gameId = computed(() => props.comment.data.data.game_id);
const content = computed(() => `${props.comment.data.data.user_name} 在 ${props.comment.data.data.game_name} 已成功下注了 ${props.comment.data.data.money}`);
const betOptions = computed(() => {
  const betData = props.comment.data.data.bet[0];
  return betData.bet_data.map((item) => ({
    how_id: betData.how_id,
    type_id: item.type_id,
    amount: item.point || props.comment.data.data.money
  }));
});

const { onSelect } = useLottery({
  game_id: gameId.value
});

const handleCopyBet = () => {
  for (const betOption of betOptions.value) {
    onSelect({
      type_id: betOption.type_id,
      how_id: betOption.how_id,
      amount: betOption.amount
    });
  }
  emit('switchToGame');
}
</script>

<style scoped></style>
