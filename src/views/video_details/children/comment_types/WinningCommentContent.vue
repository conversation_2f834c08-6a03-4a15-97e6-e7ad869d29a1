<template>
  <p>
    <span class="bg-[#ff2b71] text-white px-1.5 py-0.5 rounded-md mr-1 inline" style="font-size: 12px"> 中奖 </span>
    <span class="text-[#ffd631] mr-1 font-bold">{{ userName }}</span>
    在
    <span class="text-[#ffd631]">{{ gameName }}</span>
    中了
    <span class="text-[#ffd631]">{{ money }}</span>
    元
  </p>
</template>

<script setup lang="ts">
import type { WinningWsMessage } from '@/types/comment';
import { computed } from 'vue';

interface Props {
  comment: WinningWsMessage;
}

const props = defineProps<Props>();

// Extract data from WebSocket message
const gameName = computed(() => props.comment.data.data.game_name);
const money = computed(() => props.comment.data.data.money);
const userName = computed(() => props.comment.data.data.user_name);
</script>

<style scoped></style>
