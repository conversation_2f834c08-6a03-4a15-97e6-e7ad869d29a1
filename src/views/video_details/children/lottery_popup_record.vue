<template>
  <div class="h-135px flex flex-col overflow-hidden mb-2.5 bg-black/[.3] rounded-md">
    <div class="flex items-center py-1.5 text-center text-xs van-hairline--bottom">
      <div class="flex-1">投注时间</div>
      <div class="flex-1">游戏</div>
      <div class="flex-1">下注</div>
      <div class="flex-1">注单</div>
      <div class="flex-1">投注金币</div>
      <div class="flex-1">状态</div>
    </div>
    <div class="flex-1 overflow-auto">
      <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
        <ul>
          <li v-for="val in list" class="flex items-center py-1.5 text-center text-xs">
            <div class="flex-1">
              <div>{{ formatter(val.created_at, 'HH:mm:ss') }}</div>
            </div>
            <div class="flex-1 van-ellipsis">
              <div>{{ val.name }}</div>
            </div>
            <div class="flex-1">
              <div>{{ val.play_name }}</div>
            </div>
            <div class="flex-1">
              <div>{{ val.period.split('-')?.[1] }}</div>
            </div>
            <div class="flex-1">
              <div>{{ val.point }}</div>
            </div>
            <div class="flex-1">
              <div :class="[val.is_win === 1 && 'text-red']">
                {{ val.is_win === 1 ? '中奖' : val.is_win === 0 ? '未中奖' : '待开奖' }}
              </div>
            </div>
          </li>
        </ul>
      </van-list>
    </div>
    <div class="flex items-center justify-between text-xs h-[25.5px] mx-2.5 mb-2.5 px-2.5 rounded bg-[#07b4df]/[.3] border border-solid border-[#07b4df]">
      <p>投注<span class="text-[#00fefb] px-2">{{ statistics?.win_num }}</span>注</p>
      <p>中奖<span class="text-[#fad825] pl-1">{{ statistics?.win_money }}</span></p>
      <p>盈利<span class="text-[#fad825] pl-1">{{ statistics?.win_point }}</span></p>
    </div>
  </div>
</template>

<script setup>
import { useListExtra } from '@/hooks';
import { GetLotteryBetRecordAPI } from '@/api/game';
import { parseDate, formatter } from '@/utils';

const props = defineProps({
  game_id: Number
})
const format = 'YYYY-MM-DD HH:mm:ss';

const statistics = reactive({
  win_num: 0,
  win_money: '0.00',
  win_point: '0.00'
});

const implementationGetParams = () => {
  return {
    game_id: props.game_id,
    time_start: formatter(parseDate(new Date()).startOf('day'), format),
    time_end: formatter(parseDate(new Date()).add(1, 'day').startOf('day'), format)
  };
};

const implementationFetched = ({ win_num, win_money, win_point }) => {
  statistics.win_num = win_num;
  statistics.win_money = win_money;
  statistics.win_point = win_point;
};


const { list, isFetching, refreshing, onRefresh, loading, error, finished, onLoad } = useListExtra({
  serverHandle: GetLotteryBetRecordAPI,
  implementationGetParams,
  implementationFetched,
  pagination: { data: 'data.lists' }
});

defineExpose({
  onRefresh: onRefresh
})

</script>