<template>
  <van-popup
    :show="show"
    @update:show="onModelValueUpdated"
    position="bottom"
    teleport="body"
    round
    :overlay-style="{ background: 'transparent' }"
    style="background: white"
  >
    <div class="flex flex-col items-center justify-center text-black">
      <h2 class="text-xl py-6">温馨提示</h2>
      <p class="text-sm">新的一期已开始，是否清空预选</p>
      <p class="text-xs flex items-center py-4"><van-checkbox v-model="checked" icon-size="14px" shape="square"></van-checkbox><span class="pl-1">记住我的操作，今后不在提醒</span></p>

      <div class="flex items-center justify-center pb-4">
        <van-button round plain type="primary" size="small" class="w-125px" style="margin-right: 12px" @click="onClear">
          清空
        </van-button>
        <van-button round type="primary" size="small" class="w-125px" @click="onConfirm"> 保留 </van-button>
      </div>

    </div>
  </van-popup>
</template>

<script setup>
const props = defineProps({
  show: Boolean
});
const emit = defineEmits(['update:show', 'clear', 'save']);


const onModelValueUpdated = (val) => {
  emit('update:show', val)
};

const checked = ref(false);

const onConfirm = () => {
  onModelValueUpdated(false);
  emit('save', checked)
};

const onClear = () => {
  onModelValueUpdated(false);
  emit('clear', checked);
}

</script>