<template>
  <van-popup
    :show="show"
    @update:show="onModelValueUpdated"
    safe-area-inset-bottom
    round
    closeable
    position="bottom"
    teleport="body"
    :overlay-style="{ background: 'transparent' }"
    style="background-color: rgb(51 51 51 / 0.6); --van-popup-close-icon-color: white"
  >
    <div class="h-11 flex items-center justify-center">
      <div class="van-picker__title van-ellipsis">筹码设置</div>
    </div>

    <div class="text-sm text-center mb-3 text-[#d4f5ff]">请至少选择4个常用筹码</div>

    <div class="mx-4 grid grid-cols-5 gap-2.5">
      <div
        v-for="val in normalChips"
        :key="val.key"
        class="flex items-center justify-center aspect-square rounded box-border border-2 border-solid relative"
        :class="localChips.includes(val.key) ? 'border-[#18c8ad]' : 'border-[#71a39b]'"
        @click.stop="selectNormal(val)"
      >
        <img :src="useAssetsFile(`video_chips/skin_icon_chips_${val.active}.png`)" class="w-10 h-10" />
        <img v-if="localChips.includes(val.key)" src="@/assets/game_img/chip_check.png" class="w-14px h-14px absolute top-0 right-0" />
      </div>
    </div>

    <div class="text-sm text-center my-3 text-[#d4f5ff]">自定义筹码</div>
    <div class="mx-4 grid grid-cols-5 gap-2.5">
      <div class="flex flex-col" v-for="val in customChips" :key="val.key">
        <div
          class="w-full flex items-center justify-center aspect-square rounded box-border border-2 border-solid relative"
          @click="selectCustom(val)"
          :class="localChips.includes(val.key) ? 'border-[#18c8ad]' : 'border-[#71a39b]'"
          
        >
          <div class="w-10 h-10 bg-cover bg-no-repeat	bg-center	flex items-center justify-center" :style="{ backgroundImage: `url(${useAssetsFile(`video_chips/skin_icon_chips_${val.point ? val.active : val.inactive}.png`)})` }">
            <span class="text-xs">{{ val.point }}</span>
          </div>
          <img v-if="localChips.includes(val.key)" src="@/assets/game_img/chip_check.png" class="w-14px h-14px absolute top-0 right-0" />
        </div>

        <div class="w-full py-3px mt-1 text-xs box-border text-center rounded border-2 border-[#71a39b] border-solid" @click="selectCustomBtn(val)">
          <span>{{ val.point ? '修改' : '新增' }}</span>
        </div>
      </div>
    </div>

    <div class="w-200px mx-auto mt-6 mb-1">
      <van-button round block type="primary" @click="submitHandle">确定</van-button>
    </div>
  </van-popup>

  <lottery_popup_chips_dialog
    v-model:show="showDigit"
    v-model:digit="digit"
    :digitKey="digitKey"
    :cancelHandle="cancelHandle"
    :confirmHandle="confirmHandle"
  />
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useAssetsFile } from '@/hooks';
import { useVideoStore } from '@/store/video';
import lottery_popup_chips_dialog from './lottery_popup_chips_dialog.vue';

const props = defineProps({
  show: Boolean
});

const videoStore = useVideoStore();
const emit = defineEmits(['update:show']);

const { chip, chips, normalChips, customChips } = storeToRefs(videoStore);

const minSelected = ref(4);
const localChips = ref([]);
const digit = ref('');
const digitKey = ref(0);
const showDigit = ref(false);

const onModelValueUpdated = (val) => {
  emit('update:show', val);
};

const selectNormal = ({ key }) => {
  const index = localChips.value.indexOf(key);
  if (index > -1) {
    localChips.value.splice(index, 1);
  } else {
    localChips.value.push(key);
  }
};

const selectCustom = ({ point, key }) => {
  if (point) {
    const index = localChips.value.indexOf(key);
    if (index > -1) {
      localChips.value.splice(index, 1);
    } else {
      localChips.value.push(key);
    }
  } else {
    digitKey.value = key;
    showDigit.value = true;
  }
};

const selectCustomBtn = ({ point, key }) => {
  if (point) {
    digit.value = point;
  }
  digitKey.value = key;
  showDigit.value = true;
}

const cancelHandle = () => {
  showDigit.value = false;
  digit.value = '';
};

const confirmHandle = (point, key) => {
  const chip = customChips.value.find(o => o.key === key);
  if (chip) {
    chip.point = parseInt(point);
    chips.value.push(key);
  };
  showDigit.value = false;
};


const submitHandle = () => {
  if (localChips.value.length < minSelected) {
    showToast({
      position: 'top',
      message: '请至少选择4个常用筹码'
    });
    return;
  }

  if (localChips.value.indexOf(chip.value) === -1) {
    videoStore.setChip(localChips.value[0])
  }
  chips.value = [...localChips.value];
  onModelValueUpdated(false);
};


watch(() => props.show, (val) => {
  if (val) {
    localChips.value = [...chips.value];
  }
});

</script>
