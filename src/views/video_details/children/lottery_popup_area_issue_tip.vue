<template>
  <div class="flex items-center justify-center">
    <div
      v-for="(val, i) in resultArr"
      :key="i"
      :data-text="val"
      class="u-issue flex justify-center items-center h-5 min-w-5 px-0.5 ml-1 rounded-sm text-xs box-border bg-white text-red-500"
    >
      <span>{{ val }}</span>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  result: String
});

const resultArr = computed(() => {
  if (props.result) {
    return props.result.split('-');
  }

  return [];
});
</script>

<style lang="less">
.u-issue[data-text='大'],
.u-issue[data-text='和大'],
.u-issue[data-text='和单'],
.u-issue[data-text='总大'],
.u-issue[data-text='总单'],
.u-issue[data-text='单'],
.u-issue[data-text='质'],
.u-issue[data-text='蓝'],
.u-issue[data-text='龙'],
.u-issue[data-text='黑'] {
  color: white;
  background-color: var(--van-blue);
}

.u-issue[data-text='和小'],
.u-issue[data-text='和双'],
.u-issue[data-text='总小'],
.u-issue[data-text='总双'],
.u-issue[data-text='小'],
.u-issue[data-text='双'],
.u-issue[data-text='合'],
.u-issue[data-text='红'],
.u-issue[data-text='虎'] {
  color: white;
  background-color: var(--van-red);
}

.u-issue[data-text='绿'],
.u-issue[data-text='和'] {
  color: white;
  background-color: var(--van-green);
}
</style>
