<template>
  <van-dialog
    :show="show"
    @update:show="onModelValueUpdated"
    :overlay-style="{ background: 'transparent' }"
    title="新增筹码金额"
    teleport="body"
    @closed="onClosed"
    style="--van-dialog-background: white; --van-text-color: black"
  >
    <div
      class="mx-3 py-5 text-center"
      style="
        --van-cell-vertical-padding: 24px;
        --van-cell-horizontal-padding: 24px;
        --van-cell-background: #f1f5ff;
        --van-field-input-text-color: black;
      "
    >
      <van-field v-model="currentDigit" type="digit" :border="false" input-align="center" autocomplete="off" placeholder="请输入" />

      <div class="mt-5 text-gray-6 text-sm">自定义范围1-200,000</div>
    </div>

    <template #footer>
      <div class="flex items-center justify-center pb-4">
        <van-button round plain type="primary" size="small" class="w-125px" style="margin-right: 12px" @click="cancelHandle?.()">
          取消
        </van-button>
        <van-button round type="primary" size="small" class="w-125px" @click="onConfirm"> 确定 </van-button>
      </div>
    </template>
  </van-dialog>
</template>

<script setup>

const props = defineProps({
  show: Boolean,
  digit: [String, Number],
  digitKey: Number,
  cancelHandle: Function,
  confirmHandle: Function
});

const emit = defineEmits(['update:show', 'update:digit']);

const onModelValueUpdated = (val) => {
  emit('update:show', val);
};

const currentDigit = ref(props.digit);

const onClosed = () => {
  currentDigit.value = "";
};


const onConfirm = () => {
  if (!currentDigit.value) {
    showToast({
      position: 'top',
      message: '请输入'
    })
  } else {
    if (!/^(?:[1-9]|[1-9]\d{1,4}|1\d{5}|200000)$/.test(currentDigit.value)) {
      showToast({
        position: 'top',
        message: '自定义范围1-200,000'
      })
    } else {
      props.confirmHandle?.(currentDigit.value, props.digitKey);
    }
  }
};

watch(
  () => props.digit,
  (val) => {
    currentDigit.value = val;
  }
)

</script>