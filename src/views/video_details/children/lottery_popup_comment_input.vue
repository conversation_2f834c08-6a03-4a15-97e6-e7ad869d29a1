<template>
  <div class="comment-input-container p-2">
    <div class="comment-input bg-[#000000]/[.3] rounded-md px-1 flex items-center">
      <img :src="chatIcon" class="w-4 h-4 absolute left-5" />
      <van-field
        ref="inputRef"
        v-model="localComment"
        type="text"
        placeholder="说点什么~"
        :rows="1"
        :autosize="{ maxHeight: 100, minHeight: 40 }"
        :maxlength="maxLength"
        :border="false"
        class="comment-textarea !text-xs"
        @keydown="handleKeydown"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      <!-- Send Button -->
      <div v-if="isFocused || localComment.length > 0" class="flex-shrink-0">
        <van-button
          :disabled="!canSubmit"
          :loading="isSubmitting"
          size="small"
          :color="canSubmit ? '#ffd631' : '#555'"
          class="send-button transition-all duration-200"
          :class="{
            'send-button--active': canSubmit,
            'send-button--loading': isSubmitting
          }"
          @click="handleSubmit"
        >
          <template v-if="!isSubmitting">
            发送
          </template>
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { showToast } from 'vant';
import chatIcon from '@/assets/video/chat.png';
import { useKeyboardDetector } from '@/utils/keyboard-detector';

const props = defineProps({
  comment: {
    type: String,
    default: ''
  },
  isSubmitting: {
    type: Boolean,
    default: false
  },
  maxLength: {
    type: Number,
    default: 500
  },
  hideKeyboard: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:comment', 'submit', 'keyboard-open', 'keyboard-close']);

// Local state
const localComment = ref(props.comment);
const isFocused = ref(false);
const inputRef = ref(null);

// Keyboard detection
const keyboardDetector = useKeyboardDetector();

// Computed properties
const canSubmit = computed(() => {
  return localComment.value.trim().length > 0 && localComment.value.length <= props.maxLength && !props.isSubmitting;
});

// Watch for external comment changes
watch(
  () => props.comment,
  (newValue) => {
    localComment.value = newValue;
  }
);

// Watch for local changes and emit
watch(localComment, (newValue) => {
  // Add length validation here instead of in handleInput
  if (newValue.length > props.maxLength) {
    localComment.value = newValue.slice(0, props.maxLength);
    showToast(`评论不能超过${props.maxLength}字`);
    return;
  }
  emit('update:comment', newValue);
});

const handleKeydown = (event) => {
  // Handle Ctrl+Enter or Cmd+Enter to submit
  if (event.key === 'Enter') {
    event.preventDefault();
    handleSubmit();
  }
};

const handleFocus = () => {
  isFocused.value = true;
  // Force check keyboard state after focus
  setTimeout(() => {
    keyboardDetector.checkKeyboardState();
  }, 300);
};

const handleBlur = () => {
  isFocused.value = false;
  // Force check keyboard state after blur
  setTimeout(() => {
    keyboardDetector.checkKeyboardState();
  }, 300);
};

const handleSubmit = () => {
  if (!canSubmit.value) {
    if (localComment.value.trim().length === 0) {
      showToast('请输入评论内容');
    } else if (localComment.value.length > props.maxLength) {
      showToast(`评论不能超过${props.maxLength}字`);
    }
    return;
  }

  emit('submit');
};

// Focus input when component mounts (optional)
const focusInput = () => {
  // This could be called from parent if needed
  const textarea = document.querySelector('.comment-textarea textarea');
  if (textarea) {
    textarea.focus();
  }
};

// Keyboard event handlers
const handleKeyboardOpen = () => {
  emit('keyboard-open');
};

const handleKeyboardClose = () => {
  emit('keyboard-close');
};

watch(
  () => props.hideKeyboard,
  (val) => {
    if (val) {
      inputRef.value.blur();
    }
  }
);

// Setup keyboard detection
onMounted(() => {
  keyboardDetector.on('onKeyboardOpen', handleKeyboardOpen);
  keyboardDetector.on('onKeyboardClose', handleKeyboardClose);
});

onUnmounted(() => {
  keyboardDetector.off('onKeyboardOpen', handleKeyboardOpen);
  keyboardDetector.off('onKeyboardClose', handleKeyboardClose);
});

// Expose methods to parent
defineExpose({
  focusInput
});
</script>

<style scoped>
.comment-input-container {
  /* Ensure the input area stays at bottom */
  position: relative;
  z-index: 10;

  /* Prevent input from being affected by keyboard */
  min-height: 60px;
  background: rgba(0, 0, 0, 0.9);

  /* Ensure input stays accessible when keyboard opens */
  transition: none;
}

.comment-input {
  border: solid 1px #ffd63140;
}

.comment-textarea {
  background-color: transparent;
  color: white;
  resize: none;
  padding: 5px;
  margin: 0;
  padding-left: 35px;
  height: 30px;
}

/* Enhanced button styling */
.send-button {
  background: linear-gradient(135deg, #555 0%, #444 100%);
  border: 1px solid #555;
  color: #999;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 50px;
  height: 25px;
}

.send-button--active {
  background: #ffd631 ;
  border: 1px solid #ffd631;
  color: black !important;
  box-shadow: 0 2px 8px rgba(7, 180, 223, 0.3);
  transform: translateY(-1px);
}

.send-button--loading {
  background: linear-gradient(135deg, #696142 0%, #c6bc92 100%) !important;
  border: 1px solid #696142 !important;
}

:deep(.send-button .van-icon) {
  transition: transform 0.2s ease;
}

:deep(.send-button--active .van-icon) {
  transform: translateX(2px);
}

/* Loading state animation */
:deep(.van-button--loading .van-loading__spinner) {
  color: white !important;
}

/* Character count styling */
.comment-input .absolute {
  pointer-events: none;
  user-select: none;
}

/* Keyboard-specific input styling */
.comment-textarea {
  /* Prevent zoom on iOS when focusing input */
  font-size: 16px !important;

  /* Ensure input remains visible and accessible */
  position: relative;
  z-index: 1;
}

/* Prevent viewport zoom on input focus (iOS) */
@media screen and (max-width: 768px) {
  .comment-textarea input,
  .comment-textarea textarea {
    font-size: 16px !important;
    transform-origin: left top;
    transform: scale(1);
  }
}
</style>
