<template>
  <div class="flex flex-col h-full mx-2.5">
    <LotteryPopupAreaIssue
      :loading="loading"
      :isStop="isStop"
      :time="time"
      :info="gameInfo"
      :data="lastPeriod"
      :gid="game_id"
      :animated="isStop"
      @finish="finishHandle"
      @stop="stopHandle"
      @switch="switchCallHandle"
      :copyTradeHandle="copyTradeHandle" 
    />

    <lottery_popup_record ref="RopupRecordRef" v-if="active === 'record'" :game_id="game_id"></lottery_popup_record>
    <lottery_popup_result ref="RopupResultRef" v-if="active === 'result'" :game_id="game_id"></lottery_popup_result>

    <div class="flex h-8 box-border text-sm u-bg rounded-t-lg relative">
      <div
        v-for="(val, index) in list"
        :key="val.id"
        class="h-8 flex flex-1 items-center justify-center text-[#d9d9d9]"
        :class="[{ 'bg-[#07b4df] text-white rounded-t-lg': activeIndex === index }]"
        @click.stop="classHandler(index)"
      >
        <span>{{ val.name }}</span>
      </div>
    </div>
    <div class="h-148px my-2 overflow-hidden">
      <LotteryPopupAreaContent
        v-for="(item, i) in list"
        :list="item.lists"
        v-show="activeIndex === i"
        :key="item.id"
        :gid="game_id"
        :cid="item.id"
        :selected="selected"
        :preselected="preselected"
        @select="(type_id) => onSelect({ type_id, how_id: item.id })"
      />
    </div>
    <div class="grid grid-cols-4 gap-x-1 text-sm h-9">
      <div
        class="h-full flex items-center justify-center u-bg relative box-border rounded-l-lg"
        :class="!lastTimeBet && 'text-white/[.25]'"
        @click="onLast"
      >
        上次下注
      </div>
      <div
        class="h-full flex items-center justify-center u-bg relative box-border"
        :class="preselected.length === 0 && 'text-white/[.25]'"
        @click="onMultiple"
      >
        X2
      </div>
      <div
        class="h-full flex items-center justify-center u-bg relative box-border"
        :class="preselected.length === 0 && 'text-white/[.25]'"
        @click="onCancel"
      >
        清空
      </div>
      <div
        class="h-full flex items-center justify-center u-bg relative box-border rounded-r-lg"
        :class="preselected.length === 0 && 'text-white/[.25]'"
        @click="handleSubmit"
      >
        <span :class="isStop && 'text-red-500'">{{ isStop ? '封盘中' : '确定' }}</span>
      </div>
    </div>
    <div class="flex items-center h-10 my-1.5">
      <div class="h-9 w-113px bg-black/[.3] rounded-full flex items-center justify-between px-1 box-border">
        <img src="@/assets/icons/ba.png" alt="">
        <!-- <van-icon name="replay" @click.stop="refreshHandle" :class="['origin-center', { 'animate-spin': refreshing }]" /> -->
        <p class="text-sm font-bold">{{ wallet?.points }}</p>
        <img :src="PLUS" class="h-22px ml-2" @click.stop="RechargeHandle" />
      </div>
      <div class="h-9 aspect-square mx-0.5" @click.stop="showChips = true">
        <img :src="SE" />
      </div>
      <div class="flex-1 h-full overflow-hidden">
        <div class="h-full flex items-center overflow-x-auto">
          <div
            v-for="val in displayChips"
            :key="val.key"
            class="w-10 h-10 flex items-center justify-center shrink-0 -mr-1"
            :class="val.key === chip && 'u-active-chip'"
            @click.stop="chipHandle(val.key)"
          >
            <div
              class="h-26px w-26px flex items-center justify-center bg-cover bg-no-repeat bg-center"
              :style="{ backgroundImage: `url(${useAssetsFile(`video_chips/skin_icon_chips_${val.active}.png`)})` }"
            >
              <span class="text-xs">{{ val.custom && val.point }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <PromptComponents
    v-model="show"
    :closeOnClickOverlay="false"
    title="提示"
    content="余额不足，是否前往充值"
    hasCancelBtn
    confirmText="去充值"
    :confirmCall="confirmPointsHandle"
    :cancelCall="() => (show = false)"
  />

  <PromptComponents
    v-model="showNap"
    :closeOnClickOverlay="false"
    title="最后一注"
    :content="String(nap)"
    hasCancelBtn
    :confirmCall="confirmNapHandle"
    :cancelCall="() => (showNap = false)"
  />

  <lottery_popup_chips v-model:show="showChips"></lottery_popup_chips>

  <lottery_popup_keep v-model:show="showKeep" @clear="clearHandle" @save="saveHandle"></lottery_popup_keep>

  <!-- <CopyTradePopup
    v-model:show="isOpenCopyTradePopup"
    :game_id="props.game_id"
    :isStop="isStop"
    :time="time"
    :lastPeriod="lastPeriod"
    :gameInfo="gameInfo"
  /> -->
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useLottery, useAssetsFile, useLotteryClaimant } from '@/hooks';
import { useUserStore } from '@/store/user';
import { useVideoStore } from '@/store/video';
import SE from '@/assets/video/se.png';
import PLUS from '@/assets/game_img/plus.png';
import { TicketClassAPI, GetTicketAPI, SetLotteryBetAPI } from '@/api/game';
import PromptComponents from '@/components/prompt/prompt';
import LotteryPopupAreaContent from './lottery_popup_area_content.vue';
import LotteryPopupAreaIssue from './lottery_popup_area_issue.vue';
// import CopyTradePopup from '../components/copy_trade_popup.vue';

import lottery_popup_record from './lottery_popup_record.vue';
import lottery_popup_result from './lottery_popup_result.vue';
import lottery_popup_chips from './lottery_popup_chips.vue';
import lottery_popup_keep from './lottery_popup_keep.vue';
import Recharge from '@/views/recharge/recharge.vue';

const props = defineProps({
  game_id: Number,
  active: String,
  switchCallHandle: Function,
  explainCallHandle: Function,
  leaveCallHandle: Function
});

const router = useRouter();
const RopupRecordRef = ref();
const RopupResultRef = ref();
const isOpenCopyTradePopup = ref(false);

const userStore = useUserStore();
const videoStore = useVideoStore();

const { wallet } = storeToRefs(userStore);
const { chip, chips, hasHelpKeep, keepLastIssuePreselected } = storeToRefs(videoStore);

const lastTimeBet = computed(() => videoStore.lastTimeBettings[props.game_id]);

const list = ref([]);
const activeIndex = ref(0);
const showChips = ref(false);
const showKeep = ref(false);

const refreshHandle = async () => {
  try {
    refreshing.value = true;
    await userStore.updateUserWalletData();
  } finally {
    refreshing.value = false;
  }
};

const getTicketClass = async () => {
  try {
    const res = await TicketClassAPI({ id: props.game_id });
    list.value = res.data;
  } catch (error) {}
};

const classHandler = (i) => {
  if (activeIndex.value !== i) {
    activeIndex.value = i;
  }
};

const chipHandle = (key) => {
  chip.value = key;
};

const implementationFetched = async () => {
  nextTick(() => {
    props.active === 'record' && RopupRecordRef.value?.onRefresh();
    props.active === 'result' && RopupResultRef.value?.onRefresh();
  })

  selected.value = [];

  if (selected.value.length) {
    refreshHandle();
    selected.value = [];
  }

  if (preselected.value.length) {
    if (!hasHelpKeep.value) {
      showKeep.value = true;
    }
  }

  if (!keepLastIssuePreselected.value && hasHelpKeep.value) {
    preselected.value = []
  }
};

const { loading, next, isStop, time, closedTime, gameInfo, lastPeriod, period, claimant } = useLotteryClaimant({
  game_id: props.game_id,
  explainCallHandle: (info) => props.explainCallHandle?.(info),
  implementationFetched
});

const stopHandle = () => {
  isStop.value = true;
  time.value = closedTime.value;
};

const { refreshing, show, nap, showNap, selected, preselected, displayChips, pickSelect, onSelect, onMultiple, onCancel, onSubmit } = useLottery({
  game_id: props.game_id,
  period,
});

// Watch for changes in preselected array to automatically switch tabs
watch(
  () => preselected.value,
  (newPreselected) => {
    const newPreselectedValue = toRaw(newPreselected);
    const listValue = toRaw(list.value);
    // Only proceed if we have valid data structures
    if (!Array.isArray(newPreselectedValue) || !Array.isArray(listValue) || list.length === 0) {
      return;
    }

    if (newPreselectedValue.length > 0) {
      // Get the most recent betting selection (last item in array)
      const latestSelection = newPreselectedValue[newPreselectedValue.length - 1];

      // Validate the selection structure
      if (latestSelection && typeof latestSelection.how_id === 'number') {
        // Find the corresponding tab index in the list array
        const targetTabIndex = listValue.findIndex(tab => tab.id === latestSelection.how_id);

        // Switch to the target tab if found and different from current
        if (targetTabIndex !== -1 && targetTabIndex !== activeIndex.value) {
          activeIndex.value = targetTabIndex;
        } else if (targetTabIndex === -1) {
          console.warn(`Tab not found for how_id: ${latestSelection.how_id}. Available tabs:`, listValue.map(tab => ({ id: tab.id, name: tab.name })));
        }
      }
    }
  },
  {
    deep: true, // Watch for deep changes in the array
    immediate: false // Don't trigger on initial mount
  }
);

const RechargeHandle = () => {
  props.leaveCallHandle?.();
  router.push({ path: '/cz' });
};

const finishHandle = () => {
  claimant();
};

const clearHandle = (checked) => {
  if (checked.value) {
    videoStore.setHasHelpKeep(true)
  };

  preselected.value = [];
  videoStore.setKeepLastIssuePreselected(false)
};

const saveHandle = (checked) => {
  if (checked.value) {
    videoStore.setHasHelpKeep(true)
  };
  videoStore.setKeepLastIssuePreselected(true)
};

const onLast = () => {
  if (
    Array.isArray(lastTimeBet.value) &&
    lastTimeBet.value.every(
      (item) =>
        typeof item.how_id === 'number' &&
        Array.isArray(item.bet_data) &&
        item.bet_data.every((bet) => typeof bet.type_id === 'number' && typeof bet.point === 'number')
    )
  ) {

    preselected.value = lastTimeBet.value;
  } else {
    // 不是期望格式，可以给个提示或者清空
    preselected.value = [];
    // 也可以弹窗提示
    // showError('上次下注数据格式不正确');
  }
};

const confirmPointsHandle = () => {
  show.value = false;
  props.leaveCallHandle?.();
  router.push({ path: '/cz' });
};

const confirmNapHandle = () => {
  showNap.value = false;
  pickSelect(nap.value);
};

const handleSubmit = () => {
  onSubmit(period.value);
};

const copyTradeHandle = () => {
  isOpenCopyTradePopup.value = true;
};

onMounted(() => {
  refreshHandle();
  getTicketClass();
  claimant();
});

onUnmounted(() => {
  stop();
});
</script>

<style lang="less">
.u-bg {
  border: solid 0.5px #9d9d9d;
}
.u-bg::after {
  content: '';
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  // border-radius: 5px;
  background: linear-gradient(0deg, #000f21 0%, rgba(20, 32, 47, 0.75) 19%, rgba(104, 104, 104, 0.5) 100%),
    linear-gradient(#000000, #000000);
};



.u-active-chip {
  background: url(@/assets/video_chips/skin_chips_sel.png) no-repeat center/cover;
}
</style>
