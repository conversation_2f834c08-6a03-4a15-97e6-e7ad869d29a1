<template>
  <div
    :class="`${isOwnMessage(comment) ? 'bg-[#ffd631]/[.19] border border-solid border-width-0.5 border-[#ffd631]' : 'bg-[#000000]/[.3]'} comment-item mx-2 mb-2 rounded-md p-1 w-fit text-xs max-w-[70%]`"
  >
    <UserCommentContent v-if="isUserMessage(comment)" :comment="comment" />
    <WinningCommentContent v-else-if="isWinningMessage(comment)" :comment="comment" />
    <BettingCommentContent v-else-if="isBettingMessage(comment)" :comment="comment" @switch-to-game="$emit('switchToGame')" />
  </div>
</template>

<script setup lang="ts">
import type { WsMessage } from '@/types/comment';
import { isUserWsMessage, isBettingWsMessage, isWinningWsMessage } from '@/utils/websocket-message-parser';

// Import comment type components
import WinningCommentContent from './comment_types/WinningCommentContent.vue';
import BettingCommentContent from './comment_types/BettingCommentContent.vue';
import UserCommentContent from './comment_types/UserCommentContent.vue';

interface Props {
  comment: WsMessage;
  userId: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  switchToGame: [];
}>();

// Helper functions to determine message type
const isUserMessage = (message: WsMessage) => isUserWsMessage(message);
const isBettingMessage = (message: WsMessage) => isBettingWsMessage(message);
const isWinningMessage = (message: WsMessage) => isWinningWsMessage(message);
const isOwnMessage = (message: WsMessage) => {
  if (!props.userId) {
    return false;
  }
  return (
    (isUserWsMessage(message) && message.data.user_info.user_id === props.userId) ||
    (isBettingWsMessage(message) && message.data.data.user_id === props.userId) ||
    (isWinningWsMessage(message) && message.data.data.user_id === props.userId)
  );
};
</script>

<style scoped>
.comment-item {
  transition: background-color 0.2s ease;
}
</style>
