<template>
  <div class="h-90px flex flex-col items-center">
    <div class="w-full flex items-center justify-between">
      <div class="overflow-hidden w-12 h-12 rounded-full" @click="switchHandle">
        <ImgComponents v-if="info.icon" :imgUrl="info.icon" />
      </div>
      <div class="flex-1 flex justify-end h-16">
        <LotteryComponent :res="data.res" :gid="String(gid)" :animated="isStop"></LotteryComponent>
      </div>

    </div>
    <div class="w-full flex items-center justify-between">
      <div class="flex items-center text-sm">
        <span>{{ isStop ? '本期封盘' : '本期截止' }}:&nbsp;</span>
        <van-count-down millisecond format="mm:ss" @finish="onFinish" :time="time * 1000" :class="['font-medium', { '!text-red-500': isStop }]"></van-count-down>
        <!-- <button
          class="mx-.5 px-2.5 text-xs rounded-lg bg-[#07b4df]/[.3] border border-solid border-[#07b4df] cursor-pointer hover:bg-[#07b4df]/[.5] transition-colors"
          @click="copyTradeHandle"
        >
          跟单计划
        </button> -->
      </div>
      <LotteryPopupAreaIssueTip v-if="visibleTtp" :result="data?.res_tips"></LotteryPopupAreaIssueTip>
    </div>
  </div>
</template>

<script setup>

import LotteryComponent from "@/views/game_lottery/components/TinsLottery";
import LotteryPopupAreaIssueTip from "./lottery_popup_area_issue_tip.vue";

const props = defineProps({
  loading: Boolean,
  isStop: Boolean,
  time: Number,
  gid: Number,
  data: {
    type: Object,
    default: () => ({}),
  },
  info: {
    type: Object,
    default: () => ({}),
  },
  copyTradeHandle: Function
});


const emit = defineEmits(['finish', 'stop', 'switch']);

const visibleTtp = computed(() => {
  return !props.isStop && ['1', "2", "3", "4", "5", "6", "10", "12", "-20", "-101", "-102", '-401', '-402'].includes(
    String(props.gid)
  );
})

const switchHandle = () => {
  emit('switch')
};

const onFinish = () => {
  if (props.isStop) {
    emit('finish');
  } else {
    emit('stop');
  }
}
</script>