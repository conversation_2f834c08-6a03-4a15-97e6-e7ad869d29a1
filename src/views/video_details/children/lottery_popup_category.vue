<template>
  <van-popup
    :show="show"
    @update:show="onModelValueUpdated"
    safe-area-inset-bottom
    round
    position="bottom"
    teleport="body"
    :overlay-style="{ background: 'transparent' }"
    @open="openHandle"
    style="background-color: rgb(51 51 51 / .6); height: 50%;"
  >
    <div class="flex flex-col" style="--van-tabs-nav-background: transparent;">
      <van-tabs v-model:active="active" :line-width="20" swipeable>
        <van-tab v-for="val in list" :key="val.id" :title="val.name">
          <div class="grid grid-cols-4 gap-3 pt-3">
            <div v-for="ch in val.lists" :key="ch.id" class="flex flex-col items-center">
              <div class="overflow-hidden w-12 h-12" @click="switchHandle(ch)">
                <ImgComponents v-if="ch.icon" :imgUrl="ch.icon" />
              </div>
              <p class="text-xs mt-1">{{ ch.name }}</p>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>
    
  </van-popup>
</template>

<script setup>
import { GetGameVideoCategoryAPI } from '@/api/game';
const props = defineProps({
  show: Boolean,
});

const emit = defineEmits(['update:show', 'switch']);

const active = ref(0);
const list = ref([]);
const onModelValueUpdated = (val) => {
  emit('update:show', val);
};

const openHandle = async () => {
  try {
    const res = await GetGameVideoCategoryAPI();
    list.value = res.data;
  } catch (error) {}
};


const switchHandle = ({ is_category, id, link, name, platform_code }) => {
  if (link) {

  } else {
    onModelValueUpdated(false);
    emit('switch', id);
  }
}

</script>
