<template>
  <van-popup
    round
    :show="show"
    destroy-on-close
    @update:show="onModelValueUpdated"
    :style="{ background: 'transparent' }"
    :close-on-click-overlay="true"
    teleport="body"
  >
    <div class="prize-popup-container">
      <!-- Header Section with Background Image -->
      <div class="header-section">
        <img src="@/assets/game_img/bg_game_winning_header.png" class="header-bg-image" alt="Header Background" />
        <div class="header-text">恭喜中奖</div>
      </div>

      <!-- Main Content Section -->
      <div class="main-content-section">
        <img src="@/assets/game_img/bg_game_winning.png" class="main-bg-image" alt="Main Background" />
        <div class="prize-content">
          <div class="prize-amount">{{ prizeAmount }}</div>
        </div>
      </div>
    </div>
    <!-- Close Button -->
    <div class="gift">
      <img src="@/assets/game_img/game_winning_gift.png" class="mr-1 w-45px" alt="gift Background" />
    </div>
  </van-popup>
</template>

<script setup>
const props = defineProps({
  show: Boolean,
  prizeAmount: {
    type: [String, Number],
    default: '0'
  }
});

const emit = defineEmits(['update:show']);

const onModelValueUpdated = (val) => {
  emit('update:show', val);
};
</script>

<style scoped>
.prize-popup-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-section {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: -38px;
  z-index: 2;
}

.header-bg-image {
  width: 150px;
  height: auto;
  display: block;
}

.header-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 1px;
}

.main-content-section {
  position: relative;
  width: 100vw;
  display: flex;
  justify-content: center;
}

.main-bg-image {
  width: 100%;
  max-width: 320px;
  height: auto;
  display: block;
  border-radius: 16px;
}

.prize-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #ffffff;
  z-index: 1;
}

.prize-amount {
  margin-top: 30px;
  font-size: 36px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 4px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6);
  color: #ffffff;
}

.prize-currency {
  font-size: 14px;
  font-weight: 500;
  color: #e0e0e0;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
}

.gift {
  position: absolute;
  bottom: 8px;
  right: 8px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  z-index: 3;
}

/* Responsive adjustments */
@media (max-width: 375px) {
  .header-bg-image {
    width: 180px;
  }

  .header-text {
    font-size: 16px;
  }

  .prize-amount {
    font-size: 32px;
  }

  .main-bg-image {
    max-width: 260px;
  }
}

@media (min-width: 414px) {
  .header-bg-image {
    width: 220px;
  }

  .main-bg-image {
    max-width: 300px;
  }

  .prize-amount {
    font-size: 40px;
  }
}
</style>
