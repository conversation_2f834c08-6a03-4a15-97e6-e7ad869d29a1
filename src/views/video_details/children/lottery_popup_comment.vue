<template>
  <div class="h-374px flex flex-col" :class="{ 'keyboard-open': isKeyboardOpen }">
    <!-- Comment List -->
    <div ref="commentListRef" class="comment-list flex-1 overflow-scroll relative pt-2">
      <!-- Comments Container -->
      <div ref="commentsContainerRef" class="comments-container">
        <!-- Flexible Spacer for Bottom Alignment -->
        <div v-if="totalCommentCount <= 12" class="comment-spacer h-300px"></div>

        <!-- Warning Message -->
        <div class="mb-6">
          <NoticeComments :notices="notices" />
        </div>
        <lottery_popup_comment_item
          v-for="comment in comments"
          :key="getMessageKey(comment)"
          :comment="comment"
          :userId="props.user_id"
          @switch-to-game="$emit('switchToGame')"
        />
      </div>

      <!-- Scroll to Bottom Button -->
      <div v-if="showScrollButton" class="fixed right-4 z-10 scroll-button-container" :style="{ bottom: '85px' }">
        <div class="relative">
          <van-button
            round
            size="small"
            color="#ffd631b0"
            class="!w-10 !h-10 !p-0 shadow-lg scroll-button"
            @click="
              () => {
                scrollToBottom(true, true);
                newMessagesCount = 0;
              }
            "
          >
            <van-icon name="arrow-down" size="16" color="black" />
          </van-button>

          <!-- New messages badge -->
          <van-badge
            v-if="newMessagesCount > 0"
            :content="newMessagesCount > 99 ? '99+' : newMessagesCount.toString()"
            class="absolute -top-1 -right-1 message-badge"
          />
        </div>
      </div>
    </div>

    <!-- Comment Input -->
    <lottery_popup_comment_input
      v-model:comment="newComment"
      :is-submitting="isSubmitting"
      @submit="handleSubmitComment"
      :hide-keyboard="showPrizePopup"
    />
    <prize_popup v-model:show="showPrizePopup" :prizeAmount="prizeAmount" />
  </div>
</template>

<script setup lang="ts">
import { GetGroupNotice, GetRecentMsg, SendMessage } from '@/api/chat';
import { CONNECT_READY, GetWS, WSSend } from '@/sdk';
import type { RecentMessage, WsMessage, UserWsMessage } from '@/types/comment';
import { filterWebSocketMessages, isPrizeWsMessage } from '@/utils/websocket-message-parser';
import { showToast } from 'vant';
import { computed, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { useKeyboardDetector } from '@/utils/keyboard-detector';
import lottery_popup_comment_input from './lottery_popup_comment_input.vue';
import lottery_popup_comment_item from './lottery_popup_comment_item.vue';
import prize_popup from './prize_popup.vue';
import NoticeComments from './comment_types/NoticeComments.vue';

interface Props {
  game_id: number;
  user_id: number;
  active: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  game_id: 0
});

const emit = defineEmits<{
  switchToGame: [];
}>();

// State
const newComment = ref('');
const comments = reactive<WsMessage[]>([]);
const commentsLength = ref(0);
const waitingDisplayComment = ref<WsMessage[]>([]);
const notices = ref<string[]>([]);
const timmerMock = ref(null);
const isSubmitting = ref(false);
const commentListRef = ref<HTMLElement>();
const commentsContainerRef = ref<HTMLElement>();
const showScrollButton = ref(false);
const isUserNearBottom = ref(true);
const userScrolledManually = ref(false);
const lastScrollTop = ref(0);
const newMessagesCount = ref(0);
const showPrizePopup = ref(false);
const prizeAmount = ref('0');

// Keyboard detection state
const isKeyboardOpen = ref(false);
const keyboardDetector = useKeyboardDetector();

// Convert game_id to reactive ref for the hook
const gameId = computed(() => props.game_id);

// Calculate total comment count for spacer logic
const totalCommentCount = computed(() => {
  // Count notices + all comment items
  return notices.value.length + comments.length;
});

// Helper function to get unique key for WebSocket messages
const getMessageKey = (message: WsMessage): string => {
  if ('uuid' in message && message.uuid) {
    return message.uuid;
  }
  if ('data' in message && message.data && 'rid' in message.data) {
    return `${message.data.rid}_${message.data.send_time || Date.now()}`;
  }
  return `${Date.now()}_${Math.random()}`;
};

// Helper function to check if user is near bottom
const checkIfNearBottom = () => {
  if (!commentListRef.value) return false;

  const { scrollTop, scrollHeight, clientHeight } = commentListRef.value;
  const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
  return distanceFromBottom <= 100;
};

// Helper function to update user scroll state
const updateScrollState = () => {
  if (!commentListRef.value) return;

  const currentScrollTop = commentListRef.value.scrollTop;

  // Check if user is near bottom
  isUserNearBottom.value = checkIfNearBottom();

  // Detect manual scrolling (user initiated scroll)
  // Only consider it manual if there's a significant scroll change
  const scrollDifference = Math.abs(currentScrollTop - lastScrollTop.value);
  if (scrollDifference > 5) {
    // If user scrolled up significantly, mark as manual
    if (currentScrollTop < lastScrollTop.value - 10) {
      userScrolledManually.value = true;
    }

    // Reset manual scroll flag and new messages count if user scrolls back to bottom
    if (isUserNearBottom.value) {
      userScrolledManually.value = false;
      newMessagesCount.value = 0;
    }
  }

  lastScrollTop.value = currentScrollTop;
};

// Auto scroll to bottom function with smooth animation
const scrollToBottom = async (smooth = true, isProgrammatic = false) => {
  await nextTick();
  if (!commentListRef.value) return;

  // Mark this as a programmatic scroll to avoid triggering manual scroll detection
  if (isProgrammatic) {
    userScrolledManually.value = false;
  }

  if (smooth && 'scrollTo' in commentListRef.value) {
    // Use smooth scrolling if supported
    commentListRef.value.scrollTo({
      top: commentListRef.value.scrollHeight,
      behavior: 'smooth'
    });

    // Update state after scroll completes
    setTimeout(() => {
      updateScrollState();
    }, 350); // Slightly longer than animation duration
  } else {
    // Fallback to custom smooth animation
    const targetScrollTop = commentListRef.value.scrollHeight;
    const startScrollTop = commentListRef.value.scrollTop;
    const distance = targetScrollTop - startScrollTop;
    const duration = 300; // 300ms animation
    const startTime = performance.now();

    const animateScroll = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function (ease-out)
      const easeOut = 1 - Math.pow(1 - progress, 3);

      if (commentListRef.value) {
        commentListRef.value.scrollTop = startScrollTop + distance * easeOut;
      }

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      } else {
        // Update state after animation completes
        updateScrollState();
      }
    };

    requestAnimationFrame(animateScroll);
  }
};

const handleSubmitComment = async () => {
  if (!newComment.value.trim()) {
    showToast('请输入评论内容');
    return;
  }

  isSubmitting.value = true;

  try {
    const res: any = await SendMessage({
      gameId: props.game_id,
      content: newComment.value.trim(),
      type: 0
    });

    const msg = res.msg;

    if (msg === 'ok') {
      newComment.value = '';

      // Always scroll to bottom when user submits their own comment
      setTimeout(() => {
        scrollToBottom(true, true); // isProgrammatic = true
      }, 150);
    } else {
      showToast(msg);
    }
  } catch (error) {
    // showToast('评论发布失败，请重试');
  } finally {
    isSubmitting.value = false;
  }
};

// Keyboard event handlers
const handleKeyboardOpen = () => {
  isKeyboardOpen.value = true;
};

const handleKeyboardClose = () => {
  isKeyboardOpen.value = false;
};

// Will auto close after 3 seconds
watch(
  () => showPrizePopup.value,
  (val) => {
    if (val) {
      setTimeout(() => {
        showPrizePopup.value = false;
      }, 3000);
    }
  }
);

// Watch for new comments and intelligently auto scroll
watch(
  () => commentsLength.value,
  (newLength, oldLength) => {
    // Only proceed if comments were actually added (not initial load)
    if (oldLength === undefined || newLength <= oldLength) return;

    // Calculate how many new comments were added
    const newCommentsAdded = newLength - oldLength;

    // Check if we should auto-scroll based on user's current position and behavior
    const shouldAutoScroll = isUserNearBottom.value && !userScrolledManually.value;

    if (shouldAutoScroll) {
      // Hide scroll button and reset new messages count when auto-scrolling
      showScrollButton.value = false;
      newMessagesCount.value = 0;

      // Add a small delay to allow the new comment to render
      setTimeout(() => {
        scrollToBottom(true, true); // isProgrammatic = true
      }, 100);
    } else {
      // User is reading older comments, don't interrupt them
      // Increment new messages count and update scroll button visibility
      newMessagesCount.value += newCommentsAdded;

      setTimeout(() => {
        updateScrollState();
      }, 100);
    }
  }
);

// Handle scroll events to show/hide scroll button and track user behavior
const handleScroll = () => {
  if (!commentListRef.value) return;

  // Update scroll state and user behavior tracking
  updateScrollState();

  // Show/hide scroll button based on position and comment count
  showScrollButton.value = !isUserNearBottom.value && comments.length > 0;
};

const handleRecentMsg = (messages: RecentMessage[]) => {
  // As @morgan99886 confirm the recent message only contains user message
  messages.forEach((message) => {
    if (message.type == 0 || message.type == 1) {
      // Convert RecentMessage to UserWsMessage format
      const userWsMessage: UserWsMessage = {
        client_id: 'recent',
        data: {
          content: message.content,
          isRoomManager: message.isRoomManager,
          rid: message.userId,
          room_id: 0,
          send_time: message.send_time,
          skin: false,
          tenant_id: 0,
          type: message.type,
          uid: message.userId,
          user_id: message.userId,
          user_info: {
            avatar: message.avatar,
            isnoble: false,
            level: message.level,
            levelIcon: message.levelIcon,
            nickname: message.nickname,
            sex: 0,
            sex_text: '',
            user_id: message.userId
          }
        },
        event_type: 6,
        uuid: `recent_${message.userId}_${message.send_time}`
      };
      comments.push(userWsMessage);
      commentsLength.value = comments.length;
      nextTick(() => {
        scrollToBottom(false, true); // isProgrammatic = true
      });
    }
  });
};

const handleWsEvent = (event: WsMessage) => {
  // show prize popup if user win
  if (isPrizeWsMessage(event, props.user_id)) {
    showPrizePopup.value = true;
    prizeAmount.value = event.data.data.money;
  }
  comments.push(event);
  commentsLength.value += 1;
  // keep the last 100 messages
  if (comments.length > 100) {
    comments.splice(0, comments.length - 100);
  }
};

const handleWsEvents = (events: WsMessage[]) => {
  console.log('events', events);
  const filteredMessages = filterWebSocketMessages(events);
  // show prize popup if user win
  if (filteredMessages.length) {
    const prizeMessage = filteredMessages.find((item) => isPrizeWsMessage(item, props.user_id));
    if (prizeMessage) {
      showPrizePopup.value = true;
      prizeAmount.value = prizeMessage.data.data.money;
    }
  }
  console.log('filteredMessages', filteredMessages);
  waitingDisplayComment.value.push(...filteredMessages);
};

const fillRecentMsg = async () => {
  try {
    const res = await GetRecentMsg({ gameId: props.game_id, number: 5 });
    // sort the message by send_time
    res.data.sort((a: any, b: any) => {
      return a.send_time.localeCompare(b.send_time);
    });
    handleRecentMsg(res.data);
  } catch (error) {
    console.log(error, 'error');
  }
};

const getGroupNotice = async () => {
  try {
    const res = await GetGroupNotice();
    const list = res.data.list || [];
    notices.value = list.map((item: any) => item.content);
    nextTick(() => {
      scrollToBottom(false, true); // isProgrammatic = true
    });
  } catch (error) {
    console.log(error, 'error');
  }
};

// groupName will be used for ws subscribe and unsubscribe, it format is player-{game_id}
const groupName = computed(() => `player_${gameId.value}`);

const initialSmoothPushWaitingComment = () => {
  setInterval(() => {
    if (waitingDisplayComment.value.length) {
      const comment = waitingDisplayComment.value.shift();
      handleWsEvent(comment);
    }
  }, 10);
};

const subscribeWs = async () => {
  // call subscribe to notice BE to start sending message
  const ws = GetWS();
  if (!ws) {
    setTimeout(subscribeWs, 1000);
    return;
  }
  ws.on('onMessage', handleWsEvents);

  // in case websocket is connected before we listen to the event
  if (ws.connected) {
    try {
      // event_type 2: subscribe
      await WSSend({ data: groupName.value, event_type: 2 });
      console.log('Connected to WebSocket');
    } catch (error) {
      console.log('error', error);
    }
  }

  // in case ws is not connected, or reconnect
  ws.on(CONNECT_READY, async () => {
    // make sure send message success
    try {
      // event_type 2: subscribe
      await WSSend({ data: groupName.value, event_type: 2 });
      console.log('Connected to WebSocket');
    } catch (error) {
      const willReconnect =
        error.message &&
        (error.message.includes(`Failed to execute 'send' on 'WebSocket'`) || error.message.includes('WebSocket is not connected'));
      if (willReconnect) {
        setTimeout(subscribeWs, 1000);
        console.log('will reconnect after 1 second');
        return;
      }
      console.log('error', error);
    }
  });
};

const unsubscribeWs = () => {
  // TODO: remove the event listener
  // call unsubscribe to notice BE to stop sending message
  // event_type 3: unsubscribe
  WSSend({ data: groupName.value, event_type: 3 });
};

watch(
  () => props.active,
  (val) => {
    if (val) {
      nextTick(() => {
        scrollToBottom(false, true); // isProgrammatic = true
      });
    }
  }
);

// Initialize data when component mounts
onMounted(() => {
  // testing betting event
  // setTimeout(() => {
  //   setInterval(() => {
  //     handleWsEvents(Array.from({length: 20}).map((_, i) => ({"client_id":"0","data":{"content":`哈哈哈 ${i} - ${Math.random()}`,"isRoomManager":0.0,"rid":93868.0,"room_id":297703.0,"send_time":"2025-06-16 21:10:09","skin":false,"tenant_id":10013.0,"type":0.0,"uid":2809115.0,"user_id":2727.0,"user_info":{"avatar":"https://hw1660tup.uuuoon.cn/uploads/0/nft/43.jpg","isnoble":false,"level":5.0,"levelIcon":"https://hw1660tup.uuuoon.cn/uploads/0/activity/20230730/95cb1203db94.png","nickname":"喜呲郎","sex":1.0,"sex_text":"男","user_id":2809115.0}},"event_type":6,"uuid":""})));
  //   }, 100);
  //   setInterval(() => {
  //     handleWsEvents([{"client_id":"Game","data":{"code":0.0,"data":{"anchor_id":2727.0,"bet":[{"bet_data":[{"name":"大","odd":1.97,"point":10.0,"type_id":1.0}],"how_id":1.0,"name":"两面"}],"game_id":1.0,"game_name":"一分快三","last_period":{},"liveId":"297703","money":"10.00","points":10.0,"target":"2727","user_id":2809115.0,"user_name":"喜呲郎"},"game_id":1.0,"game_type":"USER_BET","liveId":"297703","target":"2727"},"event_type":6,"uuid":"1934600060496089088"}]);
  //   }, 200);
  // }, 10000)
  // testing winning event
  // setTimeout(() => {
  //   handleWsEvents([{"client_id":"Game","data":{"anchor_id":"","code":0.0,"data":{"game_name":"一分快三","money":"19.70","target":"2727","user_id":2809115.0,"user_name":"喜呲郎"},"game_id":1.0,"game_type":"WIN_MESSAGE","liveId":"297703","msg":"success","target":"2727"},"event_type":6,"uuid":"1934600597685751808"}]);
  // }, 2000);

  subscribeWs();
  fillRecentMsg();
  getGroupNotice();
  initialSmoothPushWaitingComment();

  // Setup keyboard detection
  keyboardDetector.on('onKeyboardOpen', handleKeyboardOpen);
  keyboardDetector.on('onKeyboardClose', handleKeyboardClose);

  // Add scroll listener and initialize scroll state
  nextTick(() => {
    if (commentListRef.value) {
      commentListRef.value.addEventListener('scroll', handleScroll);

      // Initialize scroll state
      updateScrollState();
      lastScrollTop.value = commentListRef.value.scrollTop;
    }
  });
});

onUnmounted(() => {
  clearInterval(timmerMock.value);
  unsubscribeWs();

  // Remove scroll listener
  if (commentListRef.value) {
    commentListRef.value.removeEventListener('scroll', handleScroll);
  }

  // Cleanup keyboard detection
  keyboardDetector.off('onKeyboardOpen', handleKeyboardOpen);
  keyboardDetector.off('onKeyboardClose', handleKeyboardClose);
});
</script>

<style scoped>
/* Enhanced scrolling behavior */
.comment-list {
  scroll-behavior: smooth;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

/* Comments container */
.comments-container {
  display: flex;
  flex-direction: column;
  min-height: 0; /* Allow shrinking */
}

/* Flexible spacer for bottom alignment */
.comment-spacer {
  flex-shrink: 0; /* Prevent shrinking */
  width: 100%;
}

/* Empty state positioning */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Custom scrollbar styling */
.comment-list::-webkit-scrollbar {
  width: 4px;
}

.comment-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.comment-list::-webkit-scrollbar-thumb {
  background: rgba(7, 180, 223, 0.6);
  border-radius: 2px;
  transition: background 0.2s ease;
}

.comment-list::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 180, 223, 0.8);
}

/* Custom scrollbar for comment list */
:deep(.van-list) {
  background-color: #1a1a1a;
}

:deep(.van-pull-refresh) {
  background-color: #1a1a1a;
}

:deep(.van-pull-refresh__track) {
  background-color: #1a1a1a;
}

:deep(.van-loading__text) {
  color: #999;
}

:deep(.van-list__finished-text) {
  color: #666;
  font-size: 12px;
}

:deep(.van-list__loading-text) {
  color: #999;
}

/* Smooth scroll animation for new comments */
:deep(.comment-item) {
  animation: slideInFromBottom 0.3s ease-out;
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scroll to bottom button container with delayed entrance */
.scroll-button-container {
  animation: delayedFadeIn 0.8s ease-out;
  /* Immediate exit when hiding */
  transition:
    opacity 0.2s ease-out,
    visibility 0.2s ease-out;
}

/* Scroll button with bounce animation after delay */
.scroll-button {
  animation: delayedBounceIn 0.8s ease-out;
  transition: all 0.2s ease;
}

.scroll-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px #ffd63155;
}

/* Message badge with delayed entrance */
.message-badge {
  animation: delayedBadgeIn 0.8s ease-out;
}

/* Delayed fade in for container */
@keyframes delayedFadeIn {
  0%,
  62.5% {
    opacity: 0;
    visibility: hidden;
  }
  63% {
    opacity: 0;
    visibility: visible;
  }
  100% {
    opacity: 1;
    visibility: visible;
  }
}

/* Delayed bounce in animation with 500ms delay */
@keyframes delayedBounceIn {
  0%,
  62.5% {
    opacity: 0;
    transform: scale(0.3);
    visibility: hidden;
  }
  63% {
    opacity: 0;
    transform: scale(0.3);
    visibility: visible;
  }
  75% {
    opacity: 1;
    transform: scale(1.05);
  }
  87.5% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Delayed badge entrance */
@keyframes delayedBadgeIn {
  0%,
  62.5% {
    opacity: 0;
    transform: scale(0.5);
    visibility: hidden;
  }
  63% {
    opacity: 0;
    transform: scale(0.5);
    visibility: visible;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Keyboard detection styles */
.keyboard-open {
  /* Maintain original height when keyboard opens */
  height: 374px !important;
  max-height: 374px !important;
  min-height: 374px !important;

  /* Prevent the component from being pushed up */
  position: relative;

  /* Ensure the component stays in its original position */
  transform: none !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.keyboard-open .comment-list {
  /* Maintain scrollable area when keyboard is open */
  height: calc(374px - 60px) !important; /* Subtract input area height */
  max-height: calc(374px - 60px) !important;
  overflow-y: auto;

  /* Ensure smooth scrolling continues to work */
  scroll-behavior: smooth;
}

.keyboard-open .comment-input-container {
  /* Keep input at bottom and accessible */
  position: sticky;
  bottom: 0;
  z-index: 20;
  background: rgba(0, 0, 0, 0.9);

  /* Ensure input doesn't get cut off */
  min-height: 60px;
}

/* Prevent viewport scaling issues on mobile */
@supports (height: 100dvh) {
  .keyboard-open {
    /* Use dynamic viewport height if supported */
    height: min(374px, 100dvh) !important;
  }
}

/* iOS specific fixes */
@supports (-webkit-touch-callout: none) {
  .keyboard-open {
    /* iOS Safari specific handling */
    -webkit-overflow-scrolling: touch;
  }

  .keyboard-open .comment-list {
    /* Prevent iOS bounce scroll from affecting layout */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}
</style>
