<template>
  <SecondPageLayout>
    <div class="flex items-center justify-center w-[345px] mx-auto h-[58px] bg-[var(--van-black-500)] rounded-md text-[15px]">
      <p>已邀请:<span class="text-[var(--van-red)]">{{ count }}</span>位好友</p>
    </div>
    <div class="h-full box-border">
      <van-pullRefresh v-model="refreshing" @refresh="onRefresh"  class="min-h-full">
        <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
          <div v-for="item in list" :key="item.id" class="flex flex-col w-[346px] mx-auto py-4 van-hairline--bottom">
            <div class="text-[15px] mb-2">成功邀请<span class="text-[var(--van-red)]">“{{ item.invitedAc }}”</span>注册登录</div>
            <div class="w-full text-xs flex items-center justify-between">
              <div>免费观看次数<span class="text-[var(--van-red)]">+{{ item.viewTimes }}</span></div>
              <span>{{ formatter(item.createTime) }}</span>
            </div>
          </div>
        </van-list>
        <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />
      </van-pullRefresh>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useList } from '@/hooks';
import { formatter } from '@/utils';
import { inviteList } from "@/api/user";
import EmptyPage from '@/components/empty/empty';

const { list, count, isFetching, refreshing, loading, error, finished, finishedText, onRefresh, onLoad } = useList({
  serverHandle: inviteList
});
</script>
