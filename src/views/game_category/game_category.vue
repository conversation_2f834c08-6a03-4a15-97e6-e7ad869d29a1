<template>
  <SecondPageLayout :title="title">
    <template #navbar>
      <van-nav-bar safe-area-inset-top left-arrow :title="title" @click-left="() => $router.go(-1)" :border="false" />
      <van-tabs v-model:active="listParams.is_hot" :ellipsis="false" @change="onRefresh">
        <van-tab title="全部" :name="0"></van-tab>
        <van-tab title="热门" :name="1"></van-tab>
      </van-tabs>
    </template>

    <div class="py-2 px-3 h-full box-border">
      <van-pullRefresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model:loading="loading" v-model:error="error" :finished="finished" :finished-text="finishedText" @load="onLoad">
          <FourOneModule :list="list" itemClass="h-24" imgClass="w-16" />
        </van-list>
      </van-pullRefresh>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useListExtra } from '@/hooks';
import { GetGameCategoryDetailAPI } from '@/api/game';
import FourOneModule from '@/views/games/components/4_1_module';

const {
  query: { column_id, title }
} = useRoute();

const listParams = reactive({
  is_hot: 0,
  id: parseInt(column_id)
});

const { list, refreshing, loading, error, finished, finishedText, onRefresh, onLoad } = useListExtra({
  serverHandle: GetGameCategoryDetailAPI,
  immediateParams: listParams
});
</script>
