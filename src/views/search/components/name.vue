<template>
  <div style="--van-button-mini-height: 28px" class="flex flex-row items-center justify-between p-0.5 pl-4 bg-white/[.1] rounded-2xl mx-3">
    <input
      type="text"
      v-model="name"
      autocomplete="off"
      id="player-search-input"
      class="text-white text-xs bg-transparent block w-full border-0 resize-none"
      placeholder="请输入搜索关键词"
      @keyup.enter="onSearch"
    />

    <div class="">
      <van-button type="primary" class="w-60px" round size="mini" @click="onSearch">搜索</van-button>
    </div>
  </div>
  <div class="flex flex-col" v-if="!isEmpty(storedData)">
    <div class="flex justify-between items-center text-white text-sm px-4 my-2">
      <div>历史记录</div>
      <div role="button" class="text-xs flex items-center h-9 px-4" @click="handleClear">
        <img :src="clearIcon" alt="" class="h-3" />
      </div>
    </div>

    <div class="mx-3 block relative">
      <div class="grid grid-cols-5 gap-1.5">
        <div
          v-for="item in storedData"
          :key="item.timestamp"
          class="text-xs text-white text-center py-2.5 border border-[#333] border-solid rounded-md"
          @click.stop="handleSearchKey(item, false)"
        >
          <span>{{ item.word }}</span>
        </div>
      </div>
    </div>
  </div>

  <div class="flex justify-between items-center text-white text-sm px-3 my-2">
    <div>关键词</div>
    <div role="button" class="text-xs flex items-center bg-[#ffd631]/[.08] h-9 rounded-full px-4" @click="refreshHandler">
      <span class="text-[#ffd631] mr-1">换一批</span>
      <img src="@/assets/webp/refresh.webp" alt="" class="h-3" />
    </div>
  </div>

  <div class="mx-3 block relative">
    <div class="grid grid-cols-5 gap-1.5">
      <div
        v-for="item in keys"
        :key="item.id"
        class="text-xs text-white text-center py-2.5 border border-[#333] border-solid rounded-md"
        @click="handleSearchKey(item)"
      >
        <span>{{ item.word }}</span>
      </div>
    </div>
  </div>

  <div class="flex flex-col mx-3">
    <h3 class="text-base my-3 text-[var(--van-primary-color)]">为你精选</h3>

    <van-list v-model:loading="loading" v-model:error="error" :finished="finished" :finishedText="finishedText" @load="onLoad">
      <div class="grid grid-cols-2 gap-2.5">
        <MiddleVideoCover
          v-for="val in list"
          :key="val.id"
          :title="val.title"
          :imgUrl="val.videoCover"
          :tags="val.tags"
          :views="val.viewTimes"
          :time="val.videoDuration"
          :vid="val.id"
          :type="val.type"
          :isWatched="val.isWatched"
        />
      </div>
      <template #loading>
        <LoadingPage />
      </template>
    </van-list>
  </div>
</template>

<script setup>
import clearIcon from '@/assets/webp/clrear.webp';
import MiddleVideoCover from '@/components/video/MiddleVideoCover';
import LoadingPage from '@/components/loadingPage/LoadingPage';
import { getLocalStorage, setLocalStorage, removeLocalStorage, isEmpty } from '@/utils';
import { useList } from '@/hooks';
import { SearchKeyAPI, increaseClickTimes } from '@/api/discover';
import { GetVideoRecommendApi } from '@/api/video';

function getLocalStorageData() {
  const tmp = getLocalStorage(PLAYER_MOBILE_SEARCH);
  return tmp ? JSON.parse(tmp) : [];
}

const PLAYER_MOBILE_SEARCH = 'PLAYER_MOBILE_SEARCH';

const router = useRouter();

const storedData = ref(getLocalStorageData());

function saveToLocalStorage(data) {
  setLocalStorage(PLAYER_MOBILE_SEARCH, JSON.stringify(data));
}

function addItem(item) {
  storedData.value.push(item);
  // 检查数据长度，如果超过10个，则移除最前面的数据项
  if (storedData.value.length > 10) {
    storedData.value.shift(); // 删除数组的第一个元素
  }
  saveToLocalStorage(storedData.value);
}

const name = ref('');

const keys = ref([]);

const GetSearchKey = () => {
  SearchKeyAPI().then(({ data }) => {
    keys.value = data.list || [];
  });
};

const refreshHandler = () => {
  GetSearchKey();
};

const onSearch = () => {
  if (name.value === '') {
    showToast('请输入搜索内容');
    return;
  }

  addItem({
    word: name.value,
    timestamp: Date.now()
  });

  router.push({ path: '/search/result', query: { name: name.value } });
};

const handleSearchKey = async ({ word, id }, flag = true) => {
  if (flag) {
    await increaseClickTimes({ id });
    addItem({ word, timestamp: Date.now() });
  }
  router.push({ path: '/search/result', query: { name: word } });
};

const keydownHandler = (e) => {
  if (e.keyCode) {
    onSearch();
  }
};

const handleClear = () => {
  removeLocalStorage(PLAYER_MOBILE_SEARCH);
  storedData.value = [];
};

const { list, loading, error, finished, finishedText, onRefresh, onLoad } = useList({
  serverHandle: GetVideoRecommendApi
});

watch(
  storedData,
  (newData) => {
    saveToLocalStorage(newData);
  },
  { deep: true }
);

onMounted(() => {
  GetSearchKey();
});
</script>
