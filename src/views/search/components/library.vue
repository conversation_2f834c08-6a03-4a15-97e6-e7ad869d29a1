<template>
    <div class="flex flex-col">
      <div
        v-for="menu in menus"
        :key="menu.code"
        class="flex overflow-x-scroll mb-1 px-3"
      >
        <div
          v-for="tag in menu.children"
          :key="tag.code"
          class="min-w-16 h-9 px-1 mr-1 flex justify-center items-center border border-solid box-border rounded-md text-xs"
          :class="[
            (
              selected[menu.code]
                ? selected[menu.code] === tag.code
                : tag.code === 0
            )
              ? 'text-[var(--van-primary-color)] border-transparent	bg-yellow-200/10'
              : 'border-[var(--van-cell-border-color)] text-white',
          ]"
          style="flex: 0 0 auto"
          @click="selectHander(tag, menu)"
        >
          <span>{{ tag.name }}</span>
        </div>
      </div>
    </div>

    <div class="flex flex-col grow py-2 px-3">
      <van-list
        v-model:loading="loading"
        v-model:error="error"
        :finished="finished"
        :finishedText="finishedText"
        @load="onLoad"
      >
        <div class="grid grid-cols-2 gap-2.5">
          <MiddleVideoCover
            v-for="val in list"
            :key="val.id"
            :title="val.title"
            :imgUrl="val.videoCover"
            :tags="val.tags"
            :views="val.viewTimes"
            :time="val.videoDuration"
            :vid="val.id"
            :type="val.type"
            :isWatched="val.isWatched"
          />
        </div>
        <template #loading>
          <LoadingPage />
        </template>
      </van-list>
    </div>
</template>

<script setup>
import { useList } from "@/hooks";
import { GetTagListApi, labelSearch } from "@/api/video";
import LoadingPage from '@/components/loadingPage/LoadingPage';
import MiddleVideoCover from "@/components/video/MiddleVideoCover";

defineOptions({
  name: 'Library'
});

const menus = ref([]);

const selected = reactive({});

const getMenus = async () => {
  const { data } = await GetTagListApi();

  const { parentTags, tags } = data;

  let tmp = [];

  parentTags.forEach((o) => {
    if (tags[o.code]) {
      tmp.push({
        ...o,
        children: [{ code: 0, name: "全部类型" }, ...tags[o.code]],
      });
    }
  });

  menus.value = tmp;
};

getMenus();

const listParams = reactive({
  tags: [],
  videoDuration: 0,
});

const { list, loading, error, onRefresh, finished, finishedText, onLoad } =
  useList({
    serverHandle: labelSearch,
    immediateParams: listParams,
  });

const selectHander = (tag, menu) => {
  selected[menu.code] = tag.code;
  if (menu.code === 'Duration') {
    listParams.videoDuration =  tag.code;
  } else {
    listParams.tags = Object.keys(selected)
    .map((code) => selected[code])
    .filter(Boolean);
  }
  onRefresh();
  
};
</script>
