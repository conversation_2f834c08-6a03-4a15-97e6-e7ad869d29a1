<template>
  <SecondPageLayout>
    <template #navbar>
      <van-nav-bar safe-area-inset-top left-arrow @click-left="onClickLeft" :border="false">
        <template #title>
          <van-tabs v-model:active="active" :ellipsis="false" :line-height="0" class="search-tabs" @change="onChange">
            <van-tab title="搜一搜" name="name" />
            <van-tab title="片库" name="library" />
          </van-tabs>
        </template>
      </van-nav-bar>
    </template>
    <keep-alive>
      <NameComponent v-if="active === 'name'" />
      <LibraryComponent v-else-if="active === 'library'" />
    </keep-alive>
  </SecondPageLayout>
</template>

<script setup>
import NameComponent from './components/name.vue';
import LibraryComponent from './components/library.vue';

defineOptions({
  name: 'Search'
});


const route = useRoute();
const router = useRouter();
const active = ref(route.query.type || 'name');

const onClickLeft = () => history.back();


const onChange = (name) => {
  router.replace({ path: '/search', query: { type: name } });
};

watch(
  () => route.query.type,
  (val) => {
    active.value = val || 'name';
  }
);

</script>

<style lang="less">
.search-tabs .van-tab--grow {
  padding: 0 16px;
}
</style>