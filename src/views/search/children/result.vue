<template>
  <SecondPageLayout>
    <template #navbar>
      <div class="van-safe-area-top">
        <van-search
          v-model="keyWord"
          show-action
          shape="round"
          placeholder="请输入搜索关键词"
          autocomplete="off"
          @clear="onClear"
          @search="onSearch"
          @cancel="onClickLeft"
          @update:model-value="onModelValueUpdated"
        >
        </van-search>
      </div>
    </template>

    <div class="py-2 px-3 h-full box-border">
      <van-pullRefresh v-model="refreshing" @refresh="onRefresh">
        <div v-if="isFetching && keyWord" class="flex justify-center items-center text-white text-sm p-4">
          <div>
            <span v-if="useBackup && list.length">未</span>搜索到关于‘<span class="text-[#ff6630]">{{ keyWord }}</span>’的内容
          </div>
        </div>
        <h3 v-if="useBackup && list.length" class="text-base my-3 text-[var(--van-primary-color)]">为你推荐</h3>
        
        <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
          <div class="grid grid-cols-2 gap-2.5">
            <MiddleVideoCover
              v-for="val in list"
              :key="val.id"
              :title="val.title"
              :imgUrl="val.videoCover"
              :tags="val.tags"
              :views="val.viewTimes"
              :time="val.videoDuration"
              :vid="val.id"
              :type="val.type"
            />
          </div>

          <template #loading>
            <LoadingPage />
          </template>

        </van-list>
      </van-pullRefresh>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useListExtra } from '@/hooks';
import { blurSearch } from '@/api/discover';
import { recommendVideoList } from '@/api/home';
import LoadingPage from '@/components/loadingPage/LoadingPage';
import MiddleVideoCover from '@/components/video/MiddleVideoCover';

const { query: { name } } = useRoute();
const onClickLeft = () => history.back();

const keyWord = ref(name);


const implementationGetParams = () => {
  return {
    keyWord: keyWord.value
  }
};


const implementationFetched = async ({ list }) => {
  if (list.length === 0) {
    // const res = await recommendVideoList()
  }
};

const {
  list,
  isFetching,
  refreshing,
  loading,
  error,
  finished,
  useBackup,
  onRefresh,
  onLoad,
} = useListExtra({
  serverHandle: blurSearch,
  backupServerHandle: recommendVideoList,
  implementationGetParams,
  implementationFetched,
  pagination: { page: 'pageIndex', limit: 'pageSize', data: 'data.list' },
});

const onClear = () => {
  list.value = [];
};

const onSearch = () => {
  if (keyWord.value === '') {
    showToast('请输入搜索内容');
    return;
  }
  list.value = [];
  onRefresh();
};

const onModelValueUpdated = (value) => {
  isFetching.value = false;
  if (value === '') {
    list.value = [];
  }
};

</script>
