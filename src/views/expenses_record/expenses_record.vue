<template>
  <SecondPageLayout>
    <div class="h-full py-2 px-3 box-border">
      <van-pullRefresh
        v-model="refreshing"
        @refresh="onRefresh"
      >
        <van-list
          v-model:loading="loading"
          v-model:error="error"
          :finished="finished"
          :finished-text="finishedText"
          @load="onLoad"
        >
          <div class="grid grid-cols-2 gap-2.5">
            <MiddleVideoCover
              v-for="val in list"
              :key="val.id"
              :title="val.title"
              :imgUrl="val.videoCover"
              :tags="val.tags"
              :views="val.viewTimes"
              :time="val.videoDuration"
              :vid="val.id"
              :type="val.type"
              :isWatched="val.isWatched"
            />
          </div>
        </van-list>

        <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />

      </van-pullRefresh>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useListExtra } from "@/hooks";
import { isEmpty } from "@/utils";
import { uWatchLongVideoHistory } from "@/api/user";
import EmptyPage from '@/components/empty/empty';
import MiddleVideoCover from "@/components/video/MiddleVideoCover";

const {
  list,
  isFetching,
  refreshing,
  loading,
  error,
  finished,
  finishedText,
  onRefresh,
  onLoad,
} = useListExtra({
  serverHandle: uWatchLongVideoHistory,
  pagination: { page: 'pageIndex', limit: 'pageSize', data: 'data.list' }
});
</script>
