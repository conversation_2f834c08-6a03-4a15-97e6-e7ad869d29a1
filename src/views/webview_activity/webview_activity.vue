<template>
  <SecondPageLayout :title="data.title">
    <template #navbar-right>
      <div class="text-sm" v-if="data.csLink" @click="navigatorHandler(data.csLink)">客服</div>
    </template>
    <div class="flex flex-col flex-1 overflow-y-scroll">
      <div v-if="data.contentPicUrl" class="w-full">
        <ImgComponents :imgUrl="data.contentPicUrl" :hasBackground="false" objectFit=""></ImgComponents>
      </div>
      <div v-else-if="data.content" class="w-full" v-html="data.content"></div>
    </div>

    <div v-if="data.jumpUrl" class="bg-[var(--van-black)] m-3 van-safe-area-bottom">
      <van-button block type="primary" @click="navigatorHandler(data.jumpUrl)">立即参与</van-button>
    </div>
    
  </SecondPageLayout>
  
</template>

<script setup>
import { listActivityDetail } from '@/api/activity';
import { useService, useNavigate } from "@/hooks";


const { navigateTo } = useNavigate();
const { query: { activity_id } } = useRoute();

const data = ref({});

const onRefresh = async () => {
  try {
    const res = await listActivityDetail({
      activity_id: Number(activity_id),
    });
    data.value = res.data;
  } catch (error) {
    //
  } finally {
  }
};

const navigatorHandler = (content) => {
  navigateTo(content, 1);
};

onRefresh()

</script>