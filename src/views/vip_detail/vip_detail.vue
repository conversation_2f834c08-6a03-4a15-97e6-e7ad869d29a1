<template>
  <SecondPageLayout>
    <div class="relative w-345px mx-auto rounded-lg bg-[var(--van-black-500)]">
      <div class="flex items-center justify-center py-3">
        <img src="@/assets/image/vip_title_l_icons.png" class="w-56px mr-3">
        <span class="text-[var(--van-primary-color)]">等级详情</span>
        <img src="@/assets/image/vip_title_r_icons.png" class="w-56px ml-3">
      </div>
      <p class="text-xs mx-6 text-[#baa48d]">荷尔蒙平台全新VIP等级上线，享受尊贵体验，累计存款达到一定标准，即可升级，每升一级即可获得对应的晋级礼金以及观影次数，晋级礼金累计可领取31260元。</p>

      <div class="mx-3 my-4">
        <table cellspacing="0" cellpadding="0" border="0" class="w-full text-10px text-center">
          <thead>
            <tr class="bg-[#baa48d]">
              <th class="py-2">等级</th>
              <th class="py-2">充值金额</th>
              <th class="py-2">奖励彩金</th>
              <th class="py-2">观影次数</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in list" :key="item.id">
              <td class="py-2">{{ item.name }}</td>
              <td class="py-2">{{ item.top_up }}</td>
              <td class="py-2">{{ item.reach_award_amount }}</td>
              <td class="py-2">{{ item.video_award_Num }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <p class="text-xs mx-6 text-[#baa48d] pb-5">例如：荷尔蒙用户A累计存款达到300000元，即可成为尊贵的VIP10用户，累计可获得760元晋级礼金以及永久免费观影资格。</p>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { VIPListAPI } from '@/api/user';

const list = ref([]);

const getVipList = async () => {
  try {
    const res = await VIPListAPI();
    list.value = res.data.vip_list;
  } catch (error) {
    console.log(error, 'error');
  }
};

onMounted(() => {
  getVipList();
})


</script>

<style lang="less" scoped>
table, th, td {
    border: 1px solid #baa48d;
    border-collapse: collapse;
}


</style>