<template>
  <div class="grid grid-cols-3 gap-1.5">
    <ShortVideoCover
      v-for="val in list"
      :key="val.id"
      :title="val.title"
      :imgUrl="val.videoCover"
      :tags="val.tags"
      :views="val.viewTimes"
      :time="val.videoDuration"
      :vid="val.id"
      :type="val.type"
      size="middle"
      wrapperClass="mb-3"
    />
  </div>
</template>

<script setup>

import ShortVideoCover from '@/components/video/ShortVideoCover';
const props = defineProps({
  list: {
    type: Array,
    default: () => []
  }
})

</script>