<template>
  <SecondPageLayout :title="isNormal ? title : '猜你喜欢'">
    <div class="w-1/2 mx-auto">
      <van-tabs v-model:active="listParams.mediaType" :ellipsis="false" :line-width="20" @change="onChange">
        <van-tab v-for="val in mediaTypeList" :key="val.key" :title="val.title" :name="val.key"></van-tab>
      </van-tabs>
    </div>

    <div v-if="isNormal" style="--van-tab-font-size: 12px; --van-tabs-line-height: 36px">
      <van-tabs v-model:active="listParams.orderBy" :line-height="0" @change="onChangeType">
        <van-tab v-for="val in TypeList" :key="val.key" :title="val.title" :name="val.key"></van-tab>
      </van-tabs>
    </div>

    <div class="flex-1 px-3 pt-3 overflow-y-auto">
      <van-pullRefresh v-model="refreshing" @refresh="onRefresh" class="min-h-full">
        <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
          <FocusLongModule v-if="listParams.mediaType === 1" :list="list"></FocusLongModule>
          <FocusShortModule v-else-if="listParams.mediaType === 2" :list="list"></FocusShortModule>
        </van-list>
        <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />
      </van-pullRefresh>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useList } from '@/hooks';
import { isEmpty } from '@/utils';
import { GetVideoTagApi } from '@/api/video';
import EmptyPage from '@/components/empty/empty';
import FocusLongModule from './components/focus_long_video.vue';
import FocusShortModule from './components/focus_short_video.vue';

const mediaTypeList = [
  { title: '影视', key: 1 },
  { title: '短视频', key: 2 }
];

const TypeList = [
  { title: '热度最高', key: 'Newest' },
  { title: '最新上架', key: 'Hottest' },
  { title: '收藏最多', key: 'Collect' }
];

const {
  params: { tagId },
  query: { title }
} = useRoute();
const isNormal = ref(true);

const listParams = reactive({
  orderBy: 'Newest',
  mediaType: 1,
  tagId: parseInt(tagId)
});

const implementationFetched = ({ isTagData }) => {
  isNormal.value = isTagData;
};

const { list, isFetching, refreshing, loading, error, finished, finishedText, onRefresh, onLoad } = useList({
  immediateParams: listParams,
  serverHandle: GetVideoTagApi,
  implementationFetched
});

const onChange = () => {
  list.value = [];
  onRefresh();
};

const onChangeType = (name) => {
  list.value = [];
  onRefresh();
}

</script>
