<template>
  <van-popup round :show="props.modelValue" @update:show="onModelValueUpdated" :style="{ width: '94%' }" :key="popupKey" :close-on-click-overlay="false">
    <div class="flex flex-col">
      <div class="flex">
        <img src="@/assets/image/popup_head.png" class="w-full" />
      </div>
      <div class="flex items-stretch bg-white h-18rem box-border pt-3 mt-[-1px]">
        <div class="grid grid-flow-row auto-rows-max gap-y-2.5 mx-2.5">
          <div
            v-for="(item, i) in list"
            :key="item.id"
            :class="[
              'flex items-center justify-center text-xs w-[5.125rem] h-[2.1875rem] rounded-full border border-solid	',
              {
                'bg-[var(--van-primary-color)] text-[#633507] border-[var(--van-primary-color)]': active === i,
                'bg-yellow-20 text-[#917151] border-yellow-150': active !== i
              }
            ]"
            @click.stop="active = i"
          >
            <span>{{ item.name }}</span>
          </div>
        </div>
        <div class="flex-1 pr-2.5 overflow-y-scroll scrollbar-none">
          <div v-html="html"></div>
        </div>
      </div>
      <div class="flex items-center px-3 py-4 bg-white tab-popup-action">
        <button class="flex-1 py-3 text-xs border-0 text-[#633507] rounded-full bg-[var(--van-primary-color)]" @click.stop="onHidden">
          不再提示
        </button>
        <button
          class="flex-1 py-3 text-xs border-0 text-rose-500 rounded-full bg-[#cd2b2b]/[.2] ml-3"
          @click.stop="onModelValueUpdated(false)"
        >
          关闭
        </button>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
const { VITE_HIDDEN_TAB } = import.meta.env;

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: true
  },
  list: {
    type: Array,
    default: () => []
  }
});

const popupKey = ref(0);

const emit = defineEmits(['update:modelValue', 'closed']);

const onModelValueUpdated = (val) => {
  emit('update:modelValue', val);
  emit('closed');
  popupKey.value += 1;
};

const onHidden = () => {
  onModelValueUpdated(false);
  emit('closed');
  sessionStorage.setItem(VITE_HIDDEN_TAB, '1')
};

const active = ref(0);

const html = computed(() => props.list[active.value]?.content);
</script>
