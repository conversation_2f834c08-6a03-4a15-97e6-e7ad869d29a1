<template>
  <div class="px-3 h-full overflow-scroll scroll-container">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        v-model:error="error"
        :finished="finished"
        :finished-text="finishedText"
        @load="onLoad"
      >
        <ThemeSort
          v-model:desc="listParams.isDesc"
          v-model:order="listParams.orderBy"
          @change="onRefresh"
        />

        <div class="h-[112px]" style="flex: 0">
          <Banner :options="adItem(11)" />
        </div>
        <div className="grid grid-cols-2 gap-2.5 pt-2">
          <MiddleVideoCover
            v-for="val in list"
            :key="val.id"
            :vid="val.id"
            :title="val.title"
            :imgUrl="val.videoCover"
            :tags="val.tags"
            :views="val.viewTimes"
            :time="val.videoDuration"
            :type="val.type"
            :tid="val.themeId"
            :cid="val.categoryId"
            :isWatched="val.isWatched"
          />
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useList } from "@/hooks";
import { useAppStore } from "@/store/app";
import ThemeSort from "@/components/ThemeSort";
import Banner from "@/components/banner/banner.vue";
import MiddleVideoCover from "@/components/video/MiddleVideoCover";
import { editedVideoForOneTheme } from "@/api/home";

const props = defineProps({
  nid: Number,
  tabIndex: Number,
  categoryId: Number,
  themeId: Number,
});

const appStore = useAppStore();

const { adItem } = storeToRefs(appStore);

const listParams = reactive({
  categoryId: props.categoryId,
  themeId: props.themeId,
  orderBy: "Newest",
  isDesc: false,
});

const {
  list,
  refreshing,
  loading,
  error,
  finished,
  finishedText,
  onRefresh,
  onLoad,
} = useList({
  serverHandle: editedVideoForOneTheme,
  immediateParams: listParams,
});
</script>
