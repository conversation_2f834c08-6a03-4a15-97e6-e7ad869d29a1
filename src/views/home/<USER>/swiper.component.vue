<template>
  <Swiper
    style="height: 100%;"
    observer
    css-mode
    @swiper="onSwiper"
    @slideChange="onSlideChange"
  >
    <SwiperSlide v-for="item in list" :key="item.key" v-slot="{ isActive }">
      <ChannelLayout
        :nid="item.id"
        :tabIndex="tabIndex"
        :isActive="isActive"
      />
    </SwiperSlide>
  </Swiper>
</template>

<script setup>
import { Swiper, SwiperSlide } from "swiper/vue";
import "swiper/css";
import ChannelLayout from "@/views/home/<USER>/ChannelLayout.vue";
import PageLayout from "@/views/home/<USER>/PageLayout.vue";

const props = defineProps({
  list: {
    type: Array,
    default: [],
  },
  tabIndex: [Number, String],
});


const { data, updateData } = inject('ProviderContent');

const swiperRef = ref(null);

const swiperIndex = ref(0)

const onSwiper = (swiper) => {
  swiperRef.value = swiper;
};

const onSlideChange = (swiper) => {
  swiperIndex.value = swiper.activeIndex;
  updateData({ tabIndex: swiper.activeIndex.toString(), isClick: false })
}

watch(
  () => data.tabIndex,
  (val, be) => {
    if (swiperRef.value?.slideTo && data.isClick) {
      swiperRef.value.slideTo(parseInt(data.tabIndex), 0);
      updateData({ isClick: false })
    }
  },
);

defineExpose({
  slideTo: (index) => {
    swiperRef.value?.slideTo(index, 0);
  },
  update: () => {
    swiperRef.value?.update();
  }
})
</script>
