<template>
  <div class="px-15px h-full overflow-scroll scroll-container" ref="container">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :immediate-check="false" :finished="finished" :finished-text="finishedText" @load="onLoad">
        <div class="h-120px my-2" style="flex: 0" v-if="banners && banners.length">
          <Banner :options="banners" />
        </div>

        <CellBanner :options="adItem(2)" />

        <div className="flex flex-col">
          <FourOneModule
            v-for="(val, index) in list"
            :key="val.themeId"
            :title="val.themeName"
            :list="val.videos"
            :id="val.themeId"
            :cid="nid"
            :ad="adThemes?.[index % adThemes?.length]"
          />
        </div>

        <template #loading>
          <LoadingPage />
        </template>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useList } from '@/hooks';
import { useAppStore } from '@/store/app';
import LoadingPage from '@/components/loadingPage/LoadingPage';
import Banner from '@/components/banner/banner.vue';
import CellBanner from '@/components/banner/cell-banner.vue';
import FourOneModule from '../component/4_1_module';
import { editedVideoList } from '@/api/home';

const props = defineProps({
  isActive: Boolean,
  nid: Number,
  tabIndex: [Number, String]
});

const appStore = useAppStore();
const container = ref(null)
const top = ref(0);
const { adItem } = storeToRefs(appStore);
const banners = computed(() => appStore.adItem(3));
const adThemes = computed(() => appStore.adItem(5));

const hasLoaded = ref(false);
const listParams = reactive({
  categoryId: props.nid
});

const implementationFetched = () => {
  hasLoaded.value = true;
};

onBeforeRouteLeave((to, from) => {
  top.value = container.value.scrollTop
});

const { list, refreshing, loading, error, finished, onRefresh, onLoad, finishedText } = useList({
  serverHandle: editedVideoList,
  immediateParams: listParams,
  limit: 5,
  implementationFetched
});

watch(
  () => props.isActive,
  (val) => {
    if (val && !hasLoaded.value) {
      onRefresh();
    }
  }
);

onActivated(() => {
  container.value.scrollTop = top.value
});
</script>
