/* 
    4 + 1 视频模块
*/

export default defineComponent({
  props: {
    title: String,
  },
  emits: ['more'],
  setup(props, { emit }) {
    return () => (
      <div className="flex items-center justify-between py-3">
        <div className='text-4 leading-6 text-[var(--van-primary-color)]'>{props.title}</div>
        <div className='flex items-center text-xs text-[var(--van-primary-color)]' onClick={() => emit('more')}>
          <span className='mr-1'>更多</span>
          <van-icon name="arrow"></van-icon>
        </div>
      </div>
    )
  }
})
