<template>
  <PageLayout>
    <div class="h-full">
      <div class="flex flex-col overflow-hidden h-full overflow-scrolling">
        <div class="h-full flex relative flex-col flex-1 home-container">
          <div class="flex items-center justify-center z-100 h-65px flex-none px-3 bg-cover bg-[url(@/assets/image/home_nav_bg.png)]">
            <!-- <img src="@/assets/image/logo-1.png" class="w-99px"> -->
            <div class="flex flex-col items-center justify-center relative w-99px h-45px overflow-hidden bg-cover bg-[url(@/assets/image/logo-bg.png)]">
              <div className="w-78px h-28px overflow-hidden" v-if="merchantLogo">
                <ImgComponents :imgUrl="merchantLogo?.long_video_logo" />
              </div>
            </div>
            

            <div
              class="flex-1 relative z-1 w-full h-[var(--van-tabs-line-height)] overflow-hidden"
              style="--van-tabs-nav-background: transparent;"
            >
              <van-tabs v-if="tabList.length > 0" v-model:active="data.tabIndex" :ellipsis="false" :line-height="0" @change="onChangeTab">
                <van-tab v-for="val in tabList" :key="val.id" :title="val.name" :name="val.key" />
              </van-tabs>
            </div>
            <div class="flex" @click.stop="searchNavigateHandle">
              <div class="w-7 h-7 ml-2">
                <img src="@/assets/icons/search-b.png" />
              </div>
            </div>
          </div>

          <div class="w-full flex-1 relative z-1 flex flex-col overflow-hidden" style="transform: translate3d(0, 0, 0)">
            <div class="w-full h-full overflow-hidden max-h-full inset-0 overflow-scrolling">
              <SwiperComponents ref="swiperRef" :list="tabList" :tabIndex="tabIndex" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageLayout>

  <homeAdPopup />
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { onMountedOrActivated } from '@vant/use';
import homeAdPopup from './component/homeAdPopup';
import SwiperComponents from './component/swiper.component.vue';
import { userLogged } from '@/hooks';
import { uVistor } from '@/api/user';
import { NavigationBarAPI } from '@/api/home';
import { useAppStore } from '@/store/app';
import { useUserStore } from '@/store/user';
import { getLocalStorage } from '@/utils';

defineOptions({
  name: 'Video'
});
const router = useRouter();

const appStore = useAppStore();
const userStore = useUserStore();

const { id } = storeToRefs(userStore);

const merchantLogo = computed(() => appStore.appConfig?.merchantLogo);

const tabList = ref([]);

const tabKey = 0;
const initTabKey = sessionStorage.getItem('TAB_KEY');
const tabIndex = ref(initTabKey ? initTabKey : tabKey.toString());

const { data, updateData } = inject('ProviderContent');

const swiperRef = ref(null);

const searchNavigateHandle = () => {
  router.push({ path: '/search', query: { type: 'name' } });
};

const onChangeTab = (key) => {
  tabIndex.value = key;
  updateData({ tabIndex: key, isClick: true });
};

const getNavigationBar = () => {
  try {
    let sessionTabList = sessionStorage.getItem('TAB_LIST');
    if (sessionTabList) {
      if (tabList.value.length <= 0) {
        tabList.value = sessionTabList ? JSON.parse(sessionTabList) : [];
      }
      return;
    }

    NavigationBarAPI({ mediaType: 1 }).then((res) => {
      let tabListArr = res.data.map((val, index) => {
        val.key = (index + tabKey).toString();

        if ((index + tabKey).toString() === initTabKey) {
          tabIndex.value = initTabKey;
        }
        return val;
      });

      tabList.value = tabListArr;
      sessionStorage.setItem('TAB_LIST', JSON.stringify(tabListArr));
    });
  } catch (error) {
    console.log(error, 'error');
  }
};

onMountedOrActivated(() => {
  const loged = userLogged();
  // if (loged) {
  //   uVistor({
  //     uuid: localStorage.getItem('deviceId'),
  //     device: 3,
  //     uid: id.value || 0
  //   });
  // }
});

onMounted(() => {
  getNavigationBar();
});

onActivated(() => {
  swiperRef.value.update();
  appStore.SetPlatedIDAction(0);
});
</script>


<style lang="less">
.home-container {
  .van-tab--grow {
    padding: 0 8px;
  }
  .van-tab--active {
    font-size: 18px;
  }
}
</style>