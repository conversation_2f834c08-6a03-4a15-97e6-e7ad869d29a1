/* 
    4 + 1 视频模块
*/

import PageHeader from "./pageHeader";
import BigVideoCover from "@/components/video/BigVideoCover";
import MiddleVideoCover from "@/components/video/MiddleVideoCover";

import { uActions } from "@/api/user";
import { useNavigate } from "@/hooks";

export default defineComponent({
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    title: String,
    id: Number,
    cid: Number,
    ad: Object,
  },
  setup(props) {
    
    const router = useRouter();

    const { navigateTo } = useNavigate();

    const more = () => {
      router.push({
        path: "/more_video",
        query: { id: props.id, cid: props.cid, title: props.title },
      });
    };

    const navigateHandle = async ({ content, jumpType, id }) => {
      navigateTo(content, jumpType);
      await uActions({ actionType: 11, eventId: id });
    };

    return () => (
      <div className="flex flex-col">
        <PageHeader title={props.title} onMore={more}></PageHeader>
        <div className="grid grid-cols-2 gap-2.5">
          {props.list.length > 0 &&
            props.list.map((val, index) => {
              if (index === 0) {
                return (
                  <BigVideoCover
                    key={val.id}
                    title={val.title}
                    imgUrl={val.videoCover}
                    tags={val.tags}
                    views={val.viewTimes}
                    time={val.videoDuration}
                    vid={val.id}
                    type={val.type}
                    tid={props.id}
                    cid={props.cid}
                    isWatched={val.isWatched}
                  />
                );
              } else {
                return (
                  <MiddleVideoCover
                    key={val.id}
                    title={val.title}
                    imgUrl={val.videoCover}
                    tags={val.tags}
                    views={val.viewTimes}
                    time={val.videoDuration}
                    vid={val.id}
                    type={val.type}
                    tid={props.id}
                    cid={props.cid}
                    isWatched={val.isWatched}
                  />
                );
              }
            })}
        </div>
        {props.ad && (
          <div
            class="mt-3 h-15 w-full rounded overflow-hidden"
            onClick={() => navigateHandle(props.ad)}
          >
            <ImgComponents imgUrl={props.ad.picUrl} object-fit="fill"></ImgComponents>
          </div>
        )}
      </div>
    );
  },
});
