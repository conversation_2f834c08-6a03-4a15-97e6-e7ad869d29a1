import icon from "@/assets/icons/<EMAIL>";
import Libraryicon from "@/assets/icons/skin_video_library.png";
export default defineComponent({
  name: 'SearchComponent',
  setup() {
    const router = useRouter();

    const navigateHandle = () => {
      router.push({ path: "/search" });
    };

    const goLibrary = () => {
      router.push({ path: "/library" });
    };

    return () => (
      <div className="flex flex-row flex-none items-center bg-[var(--van-black)] py-3 px-3">
        <div
          className="flex flex-row flex-1 items-center justify-between px-4 bg-white/[.1] h-8 rounded-2xl"
          onClick={navigateHandle}
        >
          <span className="text-xs text-[#808080]">请输入关键词</span>
          <img src={icon} className="h-4" />
        </div>
        <div role="button" className="text-white text-sm ml-2" onClick={goLibrary}>
          <img src={Libraryicon} className="h-5" />
        </div>
      </div>
    );
  },
});
