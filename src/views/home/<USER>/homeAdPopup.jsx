import { storeToRefs } from 'pinia';
import { useAppStore } from '@/store/app';
import { GetAdListAPI } from '@/api/home';
import { getLocalStorage } from '@/utils';
import HomePopup from './home_popup.vue';
import HomePopupBanner from './home_popup_banner.vue';

export default defineComponent({
  props: {},
  name: 'homeAdPopup',
  setup(props) {
    const appStore = useAppStore();
    const { hasNeedAd, homePopupVisible, homePopupBannerVisible } = storeToRefs(appStore);
    const homePopupList = ref([]);
    const homePopupBannerList = ref([]);

    const closedHandle = () => {
      appStore.SetHomePopupBannerVisibleAction(true);
    };

    const getHomePagePopup = async () => {
      try {
        const res = await GetAdListAPI({ app_index_arr: [6, 14] });

        homePopupList.value = res.data.find((val) => val.id === 14)?.adList;
        homePopupBannerList.value = res.data.find((val) => val.id === 6).adList;

        const userToken = getLocalStorage(import.meta.env.VITE_TOKEN_KEY);
        const isShow = sessionStorage.getItem('HOME_POPUP');
        const hiddenTab = sessionStorage.getItem(import.meta.env.VITE_HIDDEN_TAB);

        if (userToken) {
          if (!isShow) {
            if (!hiddenTab) {
              if (homePopupList.value && homePopupList.value.length > 0) {
                appStore.SetHomePopupVisibleAction(true);
              } else {
                closedHandle();
              }
            } else {
              closedHandle();
            }
          }
          sessionStorage.setItem('HOME_POPUP', '1');
        }
      } catch (error) {}
    };

    onMounted(() => {
      getHomePagePopup()
    });

    return () => (
      hasNeedAd.value && (
        <div>
          {
            homePopupList.value && homePopupList.value?.length > 0 && (
              <HomePopup v-model={homePopupVisible.value} list={homePopupList.value} onClosed={() => {
                if (homePopupBannerList && homePopupBannerList.value.length > 0) {
                  appStore.SetHomePopupBannerVisibleAction(true);
                }
              }} />
            )
          }
          <HomePopupBanner v-model={homePopupBannerVisible.value} list={homePopupBannerList.value} onClosed={() => {
            sessionStorage.setItem('HOME_POPUP', '1');
          }} />
        </div>
      )
    );
  }
});
