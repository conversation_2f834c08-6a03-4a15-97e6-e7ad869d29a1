<template>
  <SecondPageLayout>
    <div class="flex flex-col items-center w-11/12  box-border mt-4 py-3 mx-auto relative text-sm">
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-4">持卡人</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm">
            <input
              v-model="params.realname"
              type="text"
              name="realname"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请输入持卡人姓名"
            />
          </div>
        </div>
      </div>
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-4">银行卡号</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm">
            <input
              v-model="params.bank_card"
              type="text"
              name="bank_card"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请输入银行卡号"
            />
          </div>
        </div>
      </div>
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-3">开户银行</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm" @click="showPicker = true">
            <input
              v-model="params.bank_name"
              type="text"
              readonly
              name="bank_name"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请选择开户银行"
            />
          </div>
          <div><van-icon name="arrow-down" class="text-[var(--van-primary-color)]" /></div>
        </div>
      </div>
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-3">开户支行</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm">
            <input
              v-model="params.bank_address"
              type="text"
              name="bank_address"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请输入开户支行"
            />
          </div>
        </div>
      </div>
      
    </div>

    <div class="flex flex-col my-8 w-10/12 mx-auto">
      <van-button type="primary" round block @click="handleSubmit">确认绑定</van-button>
      <p className="text-xs text-center my-3">请妥善填写银行卡信息，绑定后不可更改</p>
    </div>

    <van-popup v-model:show="showPicker" round position="bottom">
      <van-picker
        :columns="list"
        @cancel="showPicker = false"
        @confirm="onConfirm"
        :columns-field-names="{ text: 'bank_name', value: 'id' }"
      />
    </van-popup>

    <PromptComponents
      v-model="show"
      title="温馨提示"
      :content="content"
      :hasCancelBtn="true"
      :confirmCall="submitHandler"
      :cancelCall="cancelHandler"
    />

  </SecondPageLayout>
</template>

<script setup>
import { GetBankList, BindBankCard } from '@/api/finance';
import PromptComponents from "@/components/prompt/prompt";

const router = useRouter()
const { query: { wallet_type_id } } = useRoute();

const show = ref(false);
const showPicker = ref(false);
const content = ref('');

const params = reactive({
  bank_card: "",
  bank_name: "",
  bank_address: "",
  realname: ""
});

const list = ref([])

const fetchHandler = () => {
  GetBankList().then(({ data }) => {
    list.value = data || []
  })
}

const onConfirm = ({ selectedOptions }) => {
  showPicker.value = false;
  params.bank_name = selectedOptions[0].bank_name;
}

const handleSubmit = (e) => {
  if (!params.realname) {
    showToast('请输入持卡人姓名')
    return
  }

  if (!params.bank_card) {
    showToast('请输入银行卡号')
    return
  }

  if (!params.bank_name) {
    showToast('请选择开户银行')
    return
  }

  if (!params.bank_address) {
    showToast('请输入开户支行')
    return
  }

  content.value = `<div style="text-align: center;">持卡人：${params.realname}<br>银行卡号：${params.bank_card}<br>开户银行：${params.bank_name}<br>开户支行：${params.bank_address}</div>`
  show.value = true;
  
}

const cancelHandler = () => {
  show.value = false;
}

const submitHandler = () => {
  show.value = false;
  const toast = showLoadingToast({ duration: 0 });
  BindBankCard(params).then(() => {
    router.go(-1)
  }).finally(() => {
    toast.close()
  })
}

fetchHandler()

</script>