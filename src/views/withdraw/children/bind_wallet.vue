<template>
  <SecondPageLayout>
    <div class="flex flex-col items-center w-11/12  box-border mt-4 py-3 mx-auto relative text-sm">
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-3">选择钱包</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm" @click="showPicker = true">
            <input
              v-model="params.wallet_type_name"
              type="text"
              readonly
              name="wallet_type_id"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请选择钱包"
            />
          </div>
          <div><van-icon name="arrow-down" class="text-[var(--van-primary-color)]" /></div>
        </div>
      </div>
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-4">钱包地址</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm">
            <input
              v-model="params.wallet_address"
              type="text"
              name="wallet_address"
              autocomplete="off"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请输入钱包地址"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="flex flex-col my-8 w-10/12 mx-auto">
      <van-button type="primary" round block @click="handleSubmit">确认绑定</van-button>
      <p className="text-xs text-center my-3">请妥善填写钱包地址，绑定后不可更改</p>
    </div>

    <van-popup v-model:show="showPicker" round position="bottom">
      <van-picker
        v-model="params.wallet_type_id"
        :columns="list"
        @cancel="showPicker = false"
        @confirm="onConfirm"
        :columns-field-names="{ text: 'title', value: 'id' }"
      />
    </van-popup>

  </SecondPageLayout>
</template>

<script setup>
import { GetWalletTypeAPI, BindWalletAPI } from '@/api/finance';

const router = useRouter()
const { query: { wallet_type_id } } = useRoute();

const showPicker = ref(false)

const params = reactive({
  wallet_type_id: [],
  wallet_type_name: '',
  wallet_address: ""
});

const list = ref([])

const fetchHandler = () => {
  GetWalletTypeAPI().then(({ data }) => {
    list.value = data || []

    const tmp = data.find(o => o.id === +wallet_type_id)

    if (tmp) {
      params.wallet_type_name = tmp.title;
      params.wallet_type_id = [tmp.id];
    }
  })
}

const onConfirm = ({ selectedOptions }) => {
  showPicker.value = false;
  params.wallet_type_name = selectedOptions[0].title;
}

const handleSubmit = (e) => {
  if (!params.wallet_address) {
    showToast('请输入钱包地址')
    return
  }

  const toast = showLoadingToast({ duration: 0 });

  BindWalletAPI({
    wallet_address: params.wallet_address,
    wallet_type_id: params.wallet_type_id[0]
  }).then(() => {
    router.replace({ path: '/withdraw' })
  }).finally(() => {
    toast.close()
  })
}

fetchHandler()

</script>