<template>
  <SecondPageLayout>
    <div class="px-3 py-4">
      <div
        v-for="item in list"
        :key="item.id"
        class="flex items-center relative overflow-hidden h-20 px-4 mb-4 rounded-lg bg-[var(--van-black-500)]"
        @click="navigatorHandler(item)"
      >
        <div class="flex flex-1 items-center">
          <div class="w-42px h-42px item.oss_img_url rounded-full">
            <ImgComponents :imgUrl="item.oss_img_url"></ImgComponents>
          </div>
          <div class="flex flex-col ml-2.5">
            <p class="text-base">{{ item.title }}</p>
            <p class="text-xs">{{ item.sub_title }}</p>
          </div>
        </div>
        <van-icon name="arrow" size="12"></van-icon>
      </div>
    </div>
  </SecondPageLayout>
</template>
<script setup>
import { GetWithdrawTypeList } from '@/api/finance';
import { setLocalStorage } from '@/utils';

const router = useRouter()
const navigatorHandler = ({ type, id, isBind, title, fee, wallet_type_id, description }) => {
  if (type === 3 && isBind === false) {
    router.push({ path: '/bind_wallet', query: { wallet_type_id } })
  } else {
    setLocalStorage('ss_withdraw_description', description)
    router.push({ path: '/withdraw_details', query: { title: title.replace(/(.*)(\(.*\))/, '$1'), id, type, fee } })
  }
}

const list = ref([])

const getWithdrawType = () => {
  GetWithdrawTypeList().then(({ data }) => {
    list.value = data
  })
}
getWithdrawType()

</script>
