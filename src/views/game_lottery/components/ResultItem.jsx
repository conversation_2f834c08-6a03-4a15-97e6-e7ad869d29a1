import TinsLottery from './TinsLottery';
import renderTip from './TinsLottery/renderTip';
import { slicePeriod } from '../utils';

export default defineComponent({
  name: 'LotteryItem',
  props: {
    period: String,
    res: String,
    resTips: String,
    gid: String
  },

  setup(props) {

    const renderItem1 = () => {
      return (
        <div className="flex items-center bg-white rounded-md mx-2.5 px-2 h-16 my-2">
          <div className="text-xs">{ slicePeriod(props.period) } 期</div>
          <div class={['flex flex-1', props.gid === '7' ? 'justify-end' : 'justify-center']}>
            <TinsLottery res={props.res} gid={props.gid} size="small" />
          </div>
          {
            props.gid !== '7' && (
              <div className="ml-auto">
                <renderTip tips={props.resTips} type="line"></renderTip>
              </div>
            )
          }
        </div>
      )
    }

    const renderItem2 = () => {
      return (
        <div className="flex items-center h-63.5px my-2">
          <div className="text-xs mx-2.5">{ slicePeriod(props.period) }期</div>
          <div className="flex justify-center flex-1 h-full">
            <TinsLottery res={props.res} gid={props.gid} size="small" />
          </div>
        </div>
      )
    }

    const renderItem3 = () => {
      const getRouletteColor = (number) => {
        const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];
        const num = parseInt(number);
        
        if (num === 0) {
          return 'number-traffic-3';
        } else if (redNumbers.includes(num)) {
          return 'number-traffic-1';
        } else {
          return 'number-traffic-4';
        }
      };

      return (
        <div className="flex items-center bg-white rounded-md mx-2.5 px-2 h-16 my-2">
          <div className="text-xs">{ slicePeriod(props.period) }期</div>
          <div className="flex justify-center flex-1">
            <div class={['number-traffic', getRouletteColor(props.res)]}>{ props.res }</div>
          </div>
          <div className="ml-auto">
            <renderTip tips={props.resTips} type="line"></renderTip>
          </div>
        </div>
      )
    }

    const renderItem4 = () => {
      return (
        <div className="flex items-center bg-white rounded-md mx-2.5 px-2 h-16 my-2">
          <div className="text-xs">{ slicePeriod(props.period) } 期</div>
          <div className="flex flex-col flex-1 items-end">
            <TinsLottery res={props.res} gid={props.gid} size="small" />
            <div className="mt-1.5">
              <renderTip tips={props.resTips} type="line"></renderTip>
            </div>
          </div>
        </div>
      )
    }

    const renderItem = () => {
      switch (props.gid) {
        case '-20':
          return <renderItem3></renderItem3>
        case '3':
          return <renderItem4></renderItem4>
        case '8':
        case '9':
        case '-21':
          return <renderItem2></renderItem2>
        default:
          return <renderItem1></renderItem1>
      }
    }
    return () => {
      return (
        <>
          {renderItem()}
        </>
      )
    }
  }
})