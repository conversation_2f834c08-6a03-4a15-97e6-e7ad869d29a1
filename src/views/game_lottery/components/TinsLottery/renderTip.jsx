import './renderTip.less'

export default defineComponent({
  name: 'RenderTip',
  props: {
    tips: String,
    gid: Number,
    type: {
      type: String,
      default: 'card'
    }
  },
  setup(props) {

    return () => {
      const { tips, type } = props;

      const tipsList = tips && tips.split('-') || []
      
      return (
        <div className="flex items-center">
          {
            tipsList.map((t, i) => (<div data-text={t} class={['lottery-tip', `lottery-tip-${type}`]}>{ t }</div>))
          }
        </div>
      )
    }
  }
})