import { useRollingNumbers } from '@/hooks';
const ColorColumns = [
  [1,2],
  [3],
  [1],
  [3],
  [1],
  [3,2],
  [1],
  [3],
  [1],
  [3]
]

export default defineComponent({
  name: 'Render7',
  props: {
    res: String,
    animated: Boolean,
    size: String,
  },
  setup(props) {
    const resultStr = computed(() => props.res);
    const animated = computed(() => props.animated);
    
    const displayRows = useRollingNumbers({
      resultStr,
      animated,
      count: 1,
      min: 1,
      max: 3,
      parseResult: str => ColorColumns[Number(str)]
    });

    return () => {
      return (
        <ul className="flex items-center justify-center">
          { !props.animated && <li className='number-traffic number-traffic-n'>{ props.res }</li> }
          {displayRows.value?.map((val, i) => (
            <li
              class={[
                "number-traffic",
                `number-traffic-${val}`,
                props.size && `number-traffic-${props.size}`,
                props.animated && `number-traffic-animated-${1 + i}`,
              ]}
            ></li>
          ))}
        </ul>
      )
    }
  }
})