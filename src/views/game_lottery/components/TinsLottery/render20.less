@keyframes spin {
  from {
      transform: rotate(0deg);
  }
  to {
      transform: rotate(360deg);
  }
}

.roulette {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  &-bg {
    width: 300px;
    height: 300px;
    background: url(@/assets/game_img/LP-1.png) no-repeat;
    background-size: cover;
    position: absolute;
    top: 0px;
    left: 50%;
    margin-left: -150px;
    transition: transform 4s cubic-bezier(0.33, 1, 0.68, 1);
    transform: rotate(0deg);
    animation-play-state: paused;
    transform-origin: 50% 50%;

    &.animated {
      animation: spin 2s linear infinite;
    }
  }

  &-select {
    width: 31px;
    height: 59px;
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translate(-50%);
    z-index: 1;
  }
  
}