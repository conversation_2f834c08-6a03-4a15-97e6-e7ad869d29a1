.lottery-tip {
  color: #666;
  font-size: 12px;
  line-height: 1.5;
  padding: 0 1.5px;
}

.lottery-tip-card {
  color: white;
  font-size: 14px;
  padding: 0 10px;
  position: relative;
  font-weight: 500;

  &+.lottery-tip-card {
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 7px;
      width: 1px;
      height: 10px;
      background-color: #666;
    }
  }
}

.lottery-tip[data-text="大"],
.lottery-tip[data-text="和大"],
.lottery-tip[data-text="和单"],
.lottery-tip[data-text="总大"],
.lottery-tip[data-text="总单"],
.lottery-tip[data-text="单"],
.lottery-tip[data-text="质"],
.lottery-tip[data-text="蓝"],
.lottery-tip[data-text="龙"],
.lottery-tip[data-text="黑"] {
  color: var(--van-blue);
}


.lottery-tip[data-text="和小"],
.lottery-tip[data-text="和双"],
.lottery-tip[data-text="总小"],
.lottery-tip[data-text="总双"],
.lottery-tip[data-text="小"],
.lottery-tip[data-text="双"],
.lottery-tip[data-text="合"],
.lottery-tip[data-text="红"],
.lottery-tip[data-text="虎"] {
  color: var(--van-red);
}

.lottery-tip[data-text="绿"],
.lottery-tip[data-text="和"] {
  color: var(--van-green);
}