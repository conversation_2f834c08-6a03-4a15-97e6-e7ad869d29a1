// 时时彩
import { useRollingNumbers } from '@/hooks';
export default defineComponent({
  name: "render2",
  props: {
    res: String,
    animated: Boolean,
    size: String,
  },

  setup(props) {
    const resultStr = computed(() => props.res);
    const animated = computed(() => props.animated);
    
    const displayRows = useRollingNumbers({
      resultStr,
      animated,
      count: 5,
      min: 0,
      max: 9,
    });
    return () => {
      return (
        <ul className="flex items-center justify-center">
          {displayRows.value.map((val, i) => (
            <li
              class={[
                "number-ssc",
                `number-ssc-${val}`,
                props.size && `number-ssc-${props.size}`,
                props.animated && `number-ssc-animated-${1 + i}`,
              ]}
            ></li>
          ))}
        </ul>
      );
    };
  },
});
