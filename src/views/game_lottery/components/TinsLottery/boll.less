.forDiceImage(@n, @i: 1) when (@i =< @n) {
  .number-dice-@{i} {
    background: url('@/assets/game_icons/dice_@{i}_b.png') no-repeat center/cover;
  }
  .forDiceImage(@n, (@i + 1));
}

.forDiceImage(6);


.forImage2(@n, @i: 0) when (@i =< @n) {
  .number-ssc-@{i} {
    background: url('@/assets/game_icons/shishi@{i}.png') no-repeat center/cover;
  }
  .forImage2(@n, (@i + 1));
}

.forImage2(11);

.forImage3(@n, @i: 1) when (@i =< @n) {
  .number-pk-@{i} {
    background: url('@/assets/game_icons/car@{i}.png') no-repeat center/cover;
  }
  .forImage3(@n, (@i + 1));
};

.forImage3(10);

.forImage4(@n, @i: 1) when (@i =< @n) {
  .number-six-@{i} {
    background: url('@/assets/game_icons/six_@{i}.png') no-repeat center/cover;
  }
  .forImage4(@n, (@i + 1));
}

.forImage4(49);

.forImage6(@n, @i: 1) when (@i =< @n) {
  .fish_shrimp-@{i} {
    background: url('@/assets/game_icons/fish_shrimp_@{i}.png') no-repeat center/cover;
  }
  .forImage6(@n, (@i + 1));
}

.forImage6(6);

.forImagePoker(@n, @i: 1) when (@i =< @n) {
  .poker-@{i} {
    background: url('@/assets/niuniu/ic_nn_@{i}.png') no-repeat center/cover;
  }
  .forImagePoker(@n, (@i + 1));
}

.forImagePoker(52);


.poker-back {
  background: url('@/assets/niuniu/proker_back.png') no-repeat center/cover;
}


.forImagetraffic(@n, @i: 1) when (@i =< @n) {
  .number-traffic-@{i} {
    background: url('@/assets/game_icons/r@{i}.png') no-repeat center/cover;
  }
  .forImagetraffic(@n, (@i + 1));
}
.forImagetraffic(4);

.bg-gradient-red {
  background-image: linear-gradient(to right, #d8060f 0%, transparent 100%);
  &[data-win='0'] {
    background-image: linear-gradient(to right, #ccc 0%, transparent 100%);
  }
}
.bg-gradient-blue {
  background-image: linear-gradient(to left, #224cb0 0%, transparent 100%);
  &[data-win='0'] {
    background-image: linear-gradient(to left, #ccc 0%, transparent 100%);
  }
}