// 六合彩
import { useRollingNumbers } from '@/hooks';
export default defineComponent({
  name: "render4",
  props: {
    res: String,
    animated: Boolean,
    size: String,
  },

  setup(props) {
    const resultStr = computed(() => props.res);
    const animated = computed(() => props.animated);
    
    const displayRows = useRollingNumbers({
      resultStr,
      animated,
      count: 7,
      min: 1,
      max: 49,
    });
    return () => {
      return (
        <ul className="flex items-center justify-center">
          {displayRows.value.map((val, i) => (
            <li
              class={[
                "number-six",
                `number-six-${parseInt(val)}`,
                props.size && `number-six-${props.size}`,
                props.animated && `number-six-animated-${1 + i}`,
              ]}
            ></li>
          ))}
        </ul>
      );
    };
  },
});
