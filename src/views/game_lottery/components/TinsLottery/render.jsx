import render1 from './render1'
import render2 from './render2'
import render3 from './render3'
import render4 from './render4'
import render5 from './render5'
import render6 from './render6'
import render7 from './render7'
import render8 from './render8'
import render9 from './render9'
import render20 from './render20'
import render21 from './render21'

import './boll.less'
import './render.less'

export default defineComponent({
  name: 'TinsLottery',
  props: {
    gid: String,
    res: String,
    animated: Boolean,
    size: String,
  },
  setup(props) {
    const renderContainer = () => {
      switch (props.gid) {
        case '1':
        case '-101':
        case '-102':
          return <render1 res={props.res} size={props.size} animated={props.animated} />
        case '2':
          return <render2 res={props.res} size={props.size} animated={props.animated} />
        case '3':
          return <render3 res={props.res} size={props.size} animated={props.animated} />
        case '4':
        case '10':
        case '12':
        case '-401':
        case '-402':
          return <render4 res={props.res} size={props.size} animated={props.animated} />
        case '5':
          return <render5 res={props.res} size={props.size} animated={props.animated} />
        case '6':
          return <render6 res={props.res} size={props.size} animated={props.animated} />
        case '7':
          return <render7 res={props.res} size={props.size} animated={props.animated} />
        case '8':
          return <render8 res={props.res} size={props.size} animated={props.animated} />
        case '9':
          return <render9 res={props.res} size={props.size} animated={props.animated} />
        case "-20":
          return <render20 res={props.res} animated={props.animated} />
        case '-21':
          return <render21 res={props.res} size={props.size} animated={props.animated} />
        default:
          return
      }
    }

    return () => {
      return (
        <>
          { renderContainer() }
        </>
      )
    }
  }
})