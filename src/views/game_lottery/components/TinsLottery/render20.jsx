import './render20.less';
import rouletteSelect from '@/assets/game_img/roulette-select.png';
export default defineComponent({
  name: 'Render20',
  props: {
    res: String,
    animated: Boolean
  },
  setup(props) {
    const rouletteNumbers = [0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26];

    const index = rouletteNumbers.indexOf(Number(props.res));
    const anglePerNumber = 360 / rouletteNumbers.length;
    const angle = index * anglePerNumber * -1;

    const styles = {
      transform: `rotate(${angle}deg)`
    }
    return () => {
      return (
        <div className="flex flex-row items-center w-full h-full relative">
          <div className='w-full h-full relative overflow-hidden'>
            <div class={['roulette-bg', props.animated && 'animated']} style={styles}></div>
          </div>
          <div className='roulette-select'>
            <img src={rouletteSelect} alt="" />
          </div>
        </div>
      )
    }
  }
})