// 赛车
import { useRollingNumbers } from '@/hooks';
export default defineComponent({
  name: "render3",
  props: {
    res: String,
    animated: Boolean,
    size: String,
  },

  setup(props) {
    const resultStr = computed(() => props.res);
    const animated = computed(() => props.animated);
    
    const displayRows = useRollingNumbers({
      resultStr,
      animated,
      count: 10,
      min: 1,
      max: 10,
    });
    return () => {
      return (
        <ul className="flex items-center justify-center">
          {displayRows.value.map((val, i) => (
            <li
              class={[
                "number-pk",
                `number-pk-${parseInt(val)}`,
                props.size && `number-pk-${props.size}`,
                props.animated && `number-pk-animated-${1 + i}`,
              ]}
            ></li>
          ))}
        </ul>
      );
    };
  },
});
