
import './render21.less';
import VSIMG from '@/assets/niuniu/VS.png';
export default defineComponent({
  name: 'Render21',
  props: {
    res: String,
    animated: Boolean,
  },
  setup(props) {
    return () => {
      const rows = computed(() => {
        try {

          if (props.animated) return [{ puke_list: [] }, {  puke_list: [] }];

          const tmp = JSON.parse(props.res)
          const [a, b] = tmp;
          let arr = [1, 1]
          if (a.point > b.point) arr = [1, 0]
          else if (a.point < b.point) arr = [0, 1]
          return [
            { ...a, is_win: arr[0] },
            { ...b, is_win: arr[1] }
          ]
          
        } catch {
          return [{ point: 0, puke_list: [] }, { point: 0, puke_list: [] }]
        }
      });

      const renderCards = (cards, j) => {
        const arr = [...cards, '', '', ''].slice(0, 3);
        return arr.map((val, i) => (
          <div className={`card-slot card-slot-placeholder${i === 2 ? ` horizontal-${j}` : ''}`} key={i}>
            { val ? <div className={`w-full h-full poker-${val}`} /> : i !== 2 ? <div className='w-full h-full poker-back' /> : ''}
          </div>
        ))
      }

      const renderResule = (point, is_win, j) => {
        return (
          <div className={`text-sm card-result card-result-${j}${is_win === 0 ? ' lose' : ''}`}>
            <span>{ point } 点</span>
          </div>
        )
      }

      return (
        <div className="w-full h-full flex items-center justify-center relative">
          {
            rows.value.map((val, i) => (
              <div className={`flex flex-col flex-1 h-full relative bg-gradient-${i === 0 ? 'blue' :'red'}`} data-win={val.is_win}>
                <div className={`flex text-sm text-white ${i === 0 ? 'justify-end pr-10' : 'pl-10'}`}>
                  <span>{ i === 0 ? '闲' : '庄' }</span>
                </div>
                <div className={`flex gap-1.5 ${i === 0 ? 'justify-end pr-5' : 'pl-5' }`}>{ renderCards(val.puke_list, i) }</div>
                { !props.animated && renderResule(val.point, val.is_win, i) }
              </div>
            ))
          }

          <div className='vs-slot'>
            <img src={VSIMG} alt="" />
          </div>
        </div>
      )
    }
  }
})