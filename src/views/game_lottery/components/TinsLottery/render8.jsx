import VSIMG from "@/assets/niuniu/VS.png";
export default defineComponent({
  name: "Render8",
  props: {
    res: String,
    animated: Boolean,
    size: String,
  },
  setup(props) {
    const rows = computed(() => {
      try {
        if (props.animated) return [{ puke_list: [] }, { puke_list: [] }];
        const arr = props.res.split("-").map((o) => {
          const [a, b] = o.split("+").map(Number);
          return {
            a,
            puke_list: [(a - 1) * 4 + b],
          };
        });

        let win1, win2;
        if (arr[0].a === arr[1].a) {
          win1 = 1;
          win2 = 1;
        } else {
          win1 = Number(arr[0].a > arr[1].a);
          win2 = Number(arr[1].a > arr[0].a);
        }
        return [
          { ...arr[0], is_win: win1 },
          { ...arr[1], is_win: win2 },
        ];
      } catch (e) {
        return [{ puke_list: [] }, { puke_list: [] }];
      }
    });

    const renderCards = ({ puke_list, is_win }, pos) => {
      const arr = [...puke_list, ""].slice(0, 1);
      return (
        <div
          class={[
            "flex flex-col flex-1 h-full relative",
            pos === 0 ? "bg-gradient-blue" : "bg-gradient-red",
          ]}
          data-win={is_win}
        >
          {arr.map((val, i) => (
            <div className="flex items-center justify-center h-full">
              <div className="card-slot" key={i}>
                {val ? (
                  <div className={`w-full h-full poker-${val}`} />
                ) : (
                  <div className="w-full h-full poker-back" />
                )}
              </div>
            </div>
          ))}
        </div>
      );
    };

    return () => {
      return (
        <div className="w-full h-full flex relative">
          {rows.value.map((val, i) => renderCards(val, i))}

          <div className="vs-slot">
            <img src={VSIMG} alt="" />
          </div>
        </div>
      );
    };
  },
});
