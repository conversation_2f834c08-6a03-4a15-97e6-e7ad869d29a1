// 鱼虾蟹
import { useRollingNumbers } from '@/hooks';
export default defineComponent({
  name: "render6",
  props: {
    res: String,
    animated: Boolean,
    size: String,
  },

  setup(props) {
    const resultStr = computed(() => props.res);
    const animated = computed(() => props.animated);
    
    const displayRows = useRollingNumbers({
      resultStr,
      animated,
      count: 3,
      min: 1,
      max: 6,
    });
    return () => {
      return (
        <ul className="flex items-center justify-center">
          {displayRows.value.map((val, i) => (
            <li
              class={[
                "fish_shrimp",
                `fish_shrimp-${parseInt(val)}`,
                props.size && `fish_shrimp-${props.size}`,
                props.animated && `fish_shrimp-animated-${1 + i}`,
              ]}
            ></li>
          ))}
        </ul>
      );
    };
  },
});
