.row {
  display: flex;
  align-items: center;
  margin-bottom: 3px;
}

.number-dice {
  width: 36px;
  height: 36px;
  &:nth-of-type(2) {
    margin: 0 16px
  };

  &-small {
    margin: 0 2px;
  }

  &-mini {
    width: 20px;
    height: 20px;
    &:nth-of-type(2) {
      margin: 0 4px
    };
  }
}

.number-ssc {
  width: 30px;
  height: 30px;
  margin: 0 4px;

  &-small {
    width: 24px;
    height: 24px;
    margin: 0 2px;
  }

  &-mini {
    width: 22px;
    height: 22px;
    margin: 0 1px;
  }
}

.number-pk {
  width: 25px;
  aspect-ratio: 3 / 4;
  margin: 0 1px;

  &-small,
  &-mini
  {
    width: 20px;
    margin: 0 1px 0 0;
    font-size: 10px;
  }


}

.number-six {
  width: 30px;
  height: 30px;
  margin: 0 2px;

  &-small {
    width: 24px;
    height: 24px;
    margin: 0 1px;
  }

  &-mini {
    width: 24px;
    height: 24px;
    margin: 0 1px;
  }
}

.number-traffic {
  width: 30px;
  height: 30px;
  margin: 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;

  &-n {
    border: 2px solid red;
    border-radius: 50%;
    color: red;
    box-sizing: border-box;
  }
}

.fish_shrimp {
  width: 36px;
  height: 36px;
  margin: 0 8px;

  &-small,
  &-mini {
    width: 24px;
    height: 24px;
    margin: 0 2px;
  }
}


.card-slot {
  width: 27px;
  height: 40px;
  position: relative;

  &-placeholder {
    background: url(@/assets/niuniu/card-placeholder.png) no-repeat center/100% 100%;
  }

  &-inset {
    margin-left: -1.5px;
  }

  &.horizontal-0 {
    order: -1;
    transform: rotate(90deg);
    margin-right: 6px;
  }

  &.horizontal-1 {
    transform: rotate(-90deg);
    margin-left: 6px;
  }
}


.vs-slot {
  position: absolute;
  width: 20px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
