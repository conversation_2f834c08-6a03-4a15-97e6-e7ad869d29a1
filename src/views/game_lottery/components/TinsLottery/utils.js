export const ColorClumns = [
  ["01", "02", "07", "08", "12", "13", "18", "19", "23", "24", "29", "30", "34", "35", "40", "45", "46"],
  ["03", "04", "09", "10", "14", "15", "20", "25", "26", "31", "36", "37", "41", "42", "47", "48"],
  ["05", "06", "11", "16", "17", "21", "22", "27", "28", "32", "33", "38", "39", "43", "44", "49"]
]

// 红：1，紫：2，绿：3
export const ColorColumns = [
  [1,2],
  [3],
  [1],
  [3],
  [1],
  [3,2],
  [1],
  [3],
  [1],
  [3]
]


export const ColorPoint = (t) => {
  return ColorClumns.findIndex(o => o.includes(t))
}