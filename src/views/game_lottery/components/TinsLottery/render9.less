.card9-result {
  position: absolute;
  bottom: 0;
  left: 20px;
  height: 20px;
  width: 130px;
  font-weight: 500;
  text-align: center;
  color: transparent;
  background: linear-gradient(to right, 
  transparent 0%,
  black 45%, 
  black 55%, 
    transparent 100%
  );

  & > span {
    font-weight: 600;
    background-clip: text;
    background-image: linear-gradient(to bottom, #ffa946, #feda10);
  }

  &-0 {
    right: 20px;
    left: auto;
  }

  &.lose {
    & > span {
      background-image: linear-gradient(to bottom, #e5e5e5, #999999);
    }
  }
}