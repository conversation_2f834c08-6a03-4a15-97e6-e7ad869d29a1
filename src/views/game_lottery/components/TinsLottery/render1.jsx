// 快三
import { useRollingNumbers } from '@/hooks';
export default defineComponent({
  name: "render1",
  props: {
    res: String,
    animated: Boolean,
    size: String,
  },

  setup(props) {

    const resultStr = computed(() => props.res);
    const animated = computed(() => props.animated);
    
    const displayRows = useRollingNumbers({
      resultStr,
      animated,
      count: 3,
      min: 1,
      max: 6,
    });

    return () => {
      return (
        <ul className="flex items-center justify-center">
          {displayRows.value.map((val, i) => (
            <li
              class={[
                "number-dice",
                `number-dice-${val}`,
                props.size && `number-dice-${props.size}`,
              ]}
            ></li>
          ))}
        </ul>
      );
    };
  },
});
