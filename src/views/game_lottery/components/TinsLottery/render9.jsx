import './render9.less';
import VSIMG from '@/assets/niuniu/VS.png';
export default defineComponent({
  name: "Render9",
  props: {
    res: String,
    animated: Boolean,
    size: String,
  },
  setup(props) {
    return () => {
      const rows = computed(() => {
        try {
          if (props.animated) return [{ puke_list: [] }, {  puke_list: [] }];
          return JSON.parse(props.res);
        } catch {
          return [{ puke_list: [] }, { puke_list: [] }]
        }
      });

      const renderCards = (list, j) => {
        const arr = [...list, '', '', '', '', ''].slice(0, 5);
        return arr.map((val, i) => (
          <div className="card-slot card-slot-inset" key={i}>
            { val ? <div className={`w-full h-full poker-${val}`} /> : <div className='w-full h-full poker-back' />}
          </div>
        ))
      }

      const renderResule = (size, is_win, j) => {
        return (
          <div className={`text-sm card9-result card9-result-${j}${is_win === 0 ? ' lose' : ''}`}>
            <span>{ size }</span>
          </div>
        )
      }

      const generatorClass = (is_win, i) => {
        return [
          'flex flex-col flex-1 h-full relative',
          i === 0 ? "bg-gradient-blue" : "bg-gradient-red",
        ]
      }

      return (
        <div className="w-full h-full flex items-center justify-center relative">
          {
            rows.value.map((val, i) => (
              <div class={generatorClass(val.is_win, i)} data-win={val.is_win}>
                <div className="flex items-center justify-center h-full">{ renderCards(val.puke_list, i) }</div>
                { !props.animated && renderResule(val.size, val.is_win, i) }
              </div>
            ))
          }
          <div className='vs-slot'>
            <img src={VSIMG} alt="" />
          </div>
        </div>
      );
    };
  },
});
