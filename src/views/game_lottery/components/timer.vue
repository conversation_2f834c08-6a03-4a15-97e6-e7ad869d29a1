<template>
  <div class="flex flex-col relative flex-none items-center py-3">
    <van-count-down
      millisecond
      :time="time - Date.now()"
      format="HH:mm:ss"
      @finish="onFinish"
      :class="[isStop && 'stoping']"
    >
      <template #default="timeData">
        <template v-if="formatLong">
          <span class="time-block">{{ loading ? '' : padStartTime(timeData.hours, 0) }}</span>
          <span class="time-block">{{ loading ? '' : padStartTime(timeData.hours, 1) }}</span>
          <span class="colon">:</span>
        </template>
        <span class="time-block">{{ loading ? '' : padStartTime(timeData.minutes, 0) }}</span>
        <span class="time-block">{{ loading ? '' : padStartTime(timeData.minutes, 1) }}</span>
        <span class="colon">:</span>
        <span class="time-block">{{ loading ? '' : padStartTime(timeData.seconds, 0) }}</span>
        <span class="time-block">{{ loading ? '' : padStartTime(timeData.seconds, 1) }}</span>
      </template>
    </van-count-down>
  </div>
</template>

<script setup>
import { isOfficialLottery } from "../utils";

const props = defineProps({
  loading: Boolean,
  time: Number,
  isStop: Boolean,
  stopCallHandler: Function,
});


const route = useRoute();

const formatLong = computed(() => isOfficialLottery(route.query.game_id));

const padStartTime = (num, pos) => {
  return num.toString().padStart(2, "0")[pos]
}

const onFinish = () => {
  props.stopCallHandler?.();
};

</script>

<style lang="less">
.van-count-down {
  display: flex;
  align-items: center;
  height: 25px;
}
.colon {
  font-size: 24px;
  color: #ffffff;
  margin: 0 4px;
  height: 25px;
}
.time-block {
  box-sizing: border-box;
  display: inline-flex;
  width: 19px;
  padding: 1.5px 0;
  align-items: center;
  justify-content: center;

  border-radius: 3px;
  font-weight: 500;
  line-height: 25px;
  background-image: linear-gradient(0deg, #e5deff 0%, #eee9ff 50%, #f6f4ff 50%);
}
.time-block + .time-block {
  margin-left: 5px;
}

.van-count-down.stoping .time-block {
  color: red;
}
</style>
