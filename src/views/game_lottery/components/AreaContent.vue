<template>
  <div :class="itemClasses(cid)" class="w-full">
    <div
      v-for="(item, i) in list"
      @click="clickHandle(item)"
      :key="item.id"
      :class="[
        (cid === 18 || cid === 36) ? 'action-item-col' : 'action-item-row',
        cid === 18 && getLotteryColor(item.name),
        cid === 36 && getRouletteColor(item.name),
        `cell-${gid}-${cid}`,
        {
          'col-span-4': i === 0 && cid === 36,
          'u-active-item_active': findSelect(item.id)
        }
      ]"
    >
      <AreaContentName :nid="item.id" :cid="cid" :code="item.code" :name="item.name"></AreaContentName>
      <div class="text-xs text-align w-6 text-center text-current	">{{ item.odd }}</div>
    </div>
  </div>
</template>

<script setup>
import { getLotteryColor, getRouletteColor } from '@/utils/game';
import AreaContentName from './AreaContentName.vue';
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  gid: [Number, String],
  cid: Number,
  findSelect: Function
});

const emit = defineEmits(['click']);

const itemClasses = (classId) => {
  switch (classId) {
    case 18:
    case 36:
      return "grid grid-cols-4 gap-2.5";
    default:
      return "grid grid-cols-2 gap-2.5";
  }
};

const clickHandle = ({ id: type_id }) => {
  emit('click', type_id)
};

</script>
