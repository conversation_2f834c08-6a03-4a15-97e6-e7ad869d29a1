<template>
  <van-popover
    :show="show"
    @update:show="onPopoverShowUpdated"
    placement="bottom-end"
    :offset="[10, 2]"
    overlay
    :overlay-style="{ background: 'transparent' }"
    style="width: 97%"
  >
    <van-grid clickable :border="false" style="padding: 8px 0">
      <van-grid-item
        v-for="item in crybetos"
        :key="item.id"
        :text="item.name"
        @click="onClick(item.id)"
      >
        <template #icon>
          <div class="w-14 h-14 overflow-hidden">
            <ImgComponents :imgUrl="item.icon" round />
          </div>
        </template>
      </van-grid-item>
    </van-grid>

    <template #reference>
      <img :src="Img" class="navbar-icon" />
    </template>
  </van-popover>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { useGameStore } from "@/store/game";
import Img from "@/assets/game_img/transfer.png";

defineOptions({
  name: "TinsPopover",
});

const props = defineProps({
  show: <PERSON><PERSON><PERSON>,
});

const emit = defineEmits(["update:show", "change"]);

const store = useGameStore();

const { crybetos } = storeToRefs(store);

const onPopoverShowUpdated = (val) => {
  emit("update:show", val);
};

const onClick = (id) => {
  emit("change", id);
};
</script>
