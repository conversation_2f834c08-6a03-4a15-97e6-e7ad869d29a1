<template>
  <van-popup
    :show="props.modelValue"
    @update:show="onModelValueUpdated"
    teleport="body"
    round
    style="width: 82%; --van-popup-background: white;"
    @closed="onClosed"
  >
    <div class="flex flex-col">
      <div class="pt-6 text-black font-semibold	text-center text-xl">{{ title }}</div>
      <div class="text-center px-3 py-5 text-[var(--van-black)]">
        <slot></slot>
      </div>

      <div class="flex items-center px-4 pb-5 overflow-hidden">
        <van-button
          color="#8b5cf6"
          round
          plain
          block
          size="small"
          @click="onCancel"
          >{{ cancelButtonText }}</van-button>
        <van-button
          color="#8b5cf6"
          round
          block
          size="small"
          @click="onConfirm"
          style="margin-left: 8px;"
        >{{ confirmButtonText }}</van-button>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
defineOptions({
  name: 'TinsDialog'
})

const props = defineProps({
  modelValue: Boolean,
  title: {
    type: String,
    default: '温馨提示'
  },
  confirmButtonText: {
    type: String,
    default: '确认'
  },
  cancelButtonText: {
    type: String,
    default: '取消'
  }
})

const emit = defineEmits(['update:modelValue', 'cancel', 'confirm', 'closed'])

const onModelValueUpdated = (val) => {
  emit('update:modelValue', val)
}



const onCancel = () => {
  onModelValueUpdated(false)
  emit('cancel')
}

const onConfirm = () => {
  emit('confirm')
}

const onClosed = () => emit('closed')

</script>


<style lang="less" scoped>
.tins-dialog {
  // width: 300px;
  // border-radius: 10px;
  // overflow: hidden;
  // background: var(--van-dialog-background);
  // backface-visibility: hidden;
  // transition: var(--van-dialog-transition);
  // transition-property: transform,opacity;
  &__header {
    color: var(--van-black);
    padding-top: var(--van-dialog-header-padding-top);
    font-weight: var(--van-dialog-header-font-weight);
    text-align: center;
    font-size: 21px;
  }

  &__content {
    text-align: center;
    padding: 24px 12px;
    font-size: var(--van-font-size-lg);
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    overflow: hidden;
    user-select: none;
    padding: 0 20px 25px;
  }

  &__button {
    width: 121px;
    height: 43.5px;
  }
}
</style>