<template>
  <van-popup
    :show="props.show"
    @update:show="onModelShowUpdated"
    teleport="body"
    closeable
    round
    safe-area-inset-bottom
    position="bottom"
    style="--van-popup-background: white"
  >
    <div class="chip-popup overflow-hidden">
      <div class="text-lg py-4 text-center text-black">筹码设置</div>
      <div class="grid grid-cols-5 px-5">
        <div
          v-for="chip in selectedChips"
          :key="chip.key"
          class="flex flex-col items-center chip-popup__item relative mb-6"
          :class="[isActive(chip.key) && 'checked']"
        >
          <div class="flex items-center justify-center chip-popup__item--icon" :data-icon="chip.icon" @click="onChange(chip)">
            {{ numFormat(chip.point) || '自定义' }}
          </div>

          <div v-if="chip.custom" class="chip-popup__item--edit" @click="handleEdit(chip.key)">
            <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1028 1024" version="1.1">
              <path
                d="M1018.319924 112.117535q4.093748 9.210934 6.652341 21.492179t2.558593 25.585928-5.117186 26.609365-16.374994 25.585928q-12.281245 12.281245-22.003898 21.492179t-16.886712 16.374994q-8.187497 8.187497-15.351557 14.32812l-191.382739-191.382739q12.281245-11.257808 29.167958-27.121083t28.144521-25.074209q14.32812-11.257808 29.679676-15.863275t30.191395-4.093748 28.656239 4.605467 24.050772 9.210934q21.492179 11.257808 47.589826 39.402329t40.425766 58.847634zM221.062416 611.554845q6.140623-6.140623 28.656239-29.167958t56.289041-56.80076l74.710909-74.710909 82.898406-82.898406 220.038979-220.038979 191.382739 192.406177-220.038979 220.038979-81.874969 82.898406q-40.937484 39.914047-73.687472 73.175753t-54.242167 54.753885-25.585928 24.562491q-10.234371 9.210934-23.539054 19.445305t-27.632802 16.374994q-14.32812 7.16406-41.960921 17.398431t-57.824197 19.957024-57.312478 16.886712-40.425766 9.210934q-27.632802 3.070311-36.843736-8.187497t-5.117186-37.867173q2.046874-14.32812 9.722653-41.449203t16.374994-56.289041 16.886712-53.730448 13.304682-33.773425q6.140623-14.32812 13.816401-26.097646t22.003898-26.097646z"
              />
            </svg>
          </div>
        </div>
      </div>

      <div class="mx-10 my-3">
        <van-button block round color="#8b5cf6" @click="onModelShowUpdated(false)">关闭</van-button>
      </div>
    </div>
  </van-popup>

  <TinsDialog v-model="show" title="修改单注金额" @confirm="onConfirm" @closed="onClosedPoint">
    <van-field
      v-model="digit"
      type="digit"
      :border="false"
      input-align="center"
      autocomplete="off"
      placeholder="请输入"
      class="rounded-lg chip-popup-input"
    />
    <div class="mt-5 text-gray-6">自定义范围1-999,9999</div>
  </TinsDialog>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useGameStore } from '@/store/game';

import TinsDialog from './Popup.vue';

import { numFormat } from '../utils';

const maxSelected = 7;

const show = ref(false);

const activeIndex = ref(-1);

const digit = ref(null);

const store = useGameStore();

const { chip, chips, selectedChips } = storeToRefs(store);

const props = defineProps({
  show: Boolean
});

const emit = defineEmits(['update:show']);

const getChipImage = (icon) => {
  return `/src/assets/chips/${icon}.png?url`;
};

const states = reactive({
  chips: store.chips
});

const onModelShowUpdated = (val) => {
  emit('update:show', val);
};

const isActive = (key) => {
  return chips.value.indexOf(key) > -1;
};

const onChange = ({ custom, point, key }) => {
  if (custom && !point) return;

  activeIndex.value = key;

  const idx = chips.value.indexOf(key);

  if (idx > -1) {
    chips.value.splice(idx, 1);
  } else {
    if (chips.value.length === maxSelected) {
      chips.value.sort((a, b) => a - b).splice(maxSelected - 1, 1, key);
    } else {
      chips.value.push(key);

      chips.value.sort((a, b) => a - b);
    }
  }

  if (chips.value.indexOf(chip.value) === -1) {
    store.setChip(chips.value[0]);
  }
};

const onClosedPoint = () => {
  activeIndex.value = -1;
  digit.value = null;
};

const handleEdit = (i) => {
  activeIndex.value = i;
  show.value = true;
};

const onConfirm = () => {
  if (!digit.value || digit.value > 9999999) {
    showToast('自定义范围1-999,9999');
  } else {
    selectedChips.value.forEach((s, i) => {
      if (s.key === activeIndex.value) {
        selectedChips.value[i].point = +digit.value;
      }
    });
    show.value = false;
  }
};
</script>

<style lang="less" scoped>
.chip-popup {
  background-color: #f1f5ff;

  &__item {
    &--icon {
      width: 50px;
      height: 50px;
      color: white;
      font-size: 11px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;

      @chips: 0, 1, 2, 3, 4, 5, 6, x;

      each(@chips, {
        &[data-icon="@{value}"] {
          background-image: url('@/assets/chips/@{value}.png');
        }
      });
    }

    &--edit {
      position: absolute;
      bottom: -15px;
      background-color: white;
      width: 32px;
      height: 15px;
      border-radius: 7.5px;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon {
        font-size: 10px;
      }
    }

    &.checked {
      &::after {
        position: absolute;
        bottom: 0;
        content: '';
        width: 15px;
        height: 15px;
        background: url(@/assets/game_img/chip_check.png) center / cover no-repeat;
      }
    }
  }

  &-input {
    --van-field-input-text-color: #497ef7;
    background-color: #f1f5ff;
    padding: 24px;
    font-size: 24px;
  }
}
</style>
