<template>
  <div
    class="w-full flex-none bg-white rounded-t-xl h-147px shadow-top"
  >
    <div class="w-full h-9 flex items-center justify-between px-3 box-border">
      <div class="text-sm">已选<span class="text-[#ff006c] px-1">{{ selected.length }}</span>注（总<span>{{ totalAmount }}</span>元）</div>
      <div class="flex items-center justify-center text-sm">
        <img src="@/assets/icons/ba.png" class="mr-1">
        <span class="text-[#8b5cf6] font-medium">{{ balance }}</span>
        <img src="@/assets/game_img/plus.png" class="h-22px ml-3" @click="throttled">
      </div>
    </div>
    <div class="grid grid-cols-3 gap-2 mx-3 my-2.5 text-sm text-[#666]">
      <button
        class="u-button rounded-2xl bg-white border border-solid border-[#f2f2f2] h-34px"
        :class="[selected.length === 0 && 'is-disabled']"
        @click="onDoubleSumbit"
        :disabled="selected.length === 0"
      >
        <span>X2</span>
      </button>
      <button
        class="u-button rounded-2xl bg-white border border-solid border-[#f2f2f2] h-34px"
        :class="[selected.length === 0 && 'is-disabled']"
        @click="onCancel"
        :disabled="selected.length === 0"
      >
        <span>取消</span>
      </button>
      <button
        class="u-button rounded-2xl bg-white border border-solid border-[#f2f2f2] h-34px"
        :class="[
          (selected.length === 0 && 'is-disabled') || 'is-preselected',
          isStop && 'is-stop',
        ]"
        @click="onSubmit"
        :disabled="selected.length === 0"
      >
        <span>{{ isStop ? "封盘中" : "确认" }}</span>
      </button>
    </div>

    <div class="h-12 mx-3 flex items-center justify-center rounded-xl bg-white border border-solid border-[#f2f2f2]">
      <div
        class="tins-chip select-none"
        v-for="item in displayChips"
        :key="item.key"
        :data-flag="item.icon"
        :class="item.key === chip && 'tins-chip-active'"
        @click="onChange(item)"
      >
        <span>{{ numFormat(item.point) }}</span>
      </div>

      <div class="tins-chip tins-chip-customize" data-flag="x" @click.stop="onOpen">
        <span>设置</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { useGameStore } from "@/store/game";
import { numFormat } from '../utils';

const store = useGameStore();

const props = defineProps({
  balance: String,
  selected: Array,
  isStop: Boolean,
  totalAmount: Number
});

const emit = defineEmits(["multiple", "cancel", "submit", 'open', 'plus']);

const {
  chip,
  chips,
} = storeToRefs(store);

const displayChips = computed(() => {
  return chips.value.map(key => store.selectedChips.find(o => o.key === key))
});

const onDoubleSumbit = () => {
  emit("multiple");
};

const onCancel = () => {
  emit("cancel");
};

const onSubmit = () => {
  emit("submit");
};

const onChange = ({ key }) => {
  if (key !== chip.value) {
    store.setChip(key)
  }
}

const onOpen = () => emit('open');


const throttled = () => {
  emit('plus');
}

</script>



<style lang="less">
.u-button {
  &.is-disabled {
    color: #b5b8bb;
  }
  &.is-stop {
    color: red;
  }
}

.tins-chips {
  height: 52px;
  padding: 0 4px;
}

.tins-chip {
  width: 37.5px;
  height: 37.5px;
  margin: 0 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  font-size: 10px;
  color: white;
  opacity: 0.6;

  @chips: 0, 1, 2, 3, 4, 5, 6, x;

  each(@chips, {
    &[data-flag="@{value}"] {
      background-image: url('@/assets/chips/@{value}.png');
    }
  });

  &-active {
    width: 40px;
    height: 40px;
    font-size: var(--van-font-size-sm);
    opacity: 1;
  }
}

</style>