<template>
  <template v-if="cid === 2">
    <div :class="`number-dice-mini number-dice-${code}`"></div>
  </template>
  <template v-else-if="cid === 3">
    <div class="overflow-hidden" v-if="nid === 11">
      <img src="@/assets/webp/d-1.webp" class="h-6 block">
    </div>
    <div class="overflow-hidden" v-else-if="nid === 12">
      <img src="@/assets/webp/d-2.webp" class="h-6 block">
    </div>
    <div class="overflow-hidden" v-else-if="nid === 13">
      <img src="@/assets/webp/d-3.webp" class="h-6 block">
    </div>
    <div class="overflow-hidden" v-else-if="nid === 14">
      <img src="@/assets/webp/d-4.webp" class="h-6 block">
    </div>
    <div class="overflow-hidden" v-else-if="nid === 15">
      <img src="@/assets/webp/d-5.webp" class="h-6 block">
    </div>
    <div class="overflow-hidden" v-else-if="nid === 16">
      <img src="@/assets/webp/d-6.webp" class="h-6 block">
    </div>
  </template>
  <template v-else-if="cid === 16">
    <div class="text-sm">{{ name }}点</div>
  </template>
  <template v-else-if="cid === 17">
    <div class="overflow-hidden" v-if="nid === 106">
      <img src="@/assets/webp/skin_icon_1fk3_leopard_1.webp" class="h-8 block">
    </div>
    <div class="overflow-hidden" v-else-if="nid === 107">
      <img src="@/assets/webp/skin_icon_1fk3_leopard_2.webp" class="h-8 block">
    </div>
    <div class="overflow-hidden" v-else-if="nid === 108">
      <img src="@/assets/webp/skin_icon_1fk3_leopard_3.webp" class="h-8 block">
    </div>
    <div class="overflow-hidden" v-else-if="nid === 109">
      <img src="@/assets/webp/skin_icon_1fk3_leopard_4.webp" class="h-8 block">
    </div>
    <div class="overflow-hidden" v-else-if="nid === 110">
      <img src="@/assets/webp/skin_icon_1fk3_leopard_5.webp" class="h-8 block">
    </div>
    <div class="overflow-hidden" v-else-if="nid === 111">
      <img src="@/assets/webp/skin_icon_1fk3_leopard_6.webp" class="h-8 block">
    </div>
    <div v-if="nid === 112">全豹</div>
  </template>
  <template v-else-if="cid === 19">
    <div :class="`fish_shrimp-small fish_shrimp-${code}`"></div>
  </template>
  <template v-else-if="cid === 20">
    <div v-if="nid === 168" class="fish_shrimp-small fish_shrimp-1"></div>
    <div v-else-if="nid === 169" class="fish_shrimp-small fish_shrimp-2"></div>
    <div v-else-if="nid === 170" class="fish_shrimp-small fish_shrimp-3"></div>
    <div v-else-if="nid === 171" class="fish_shrimp-small fish_shrimp-4"></div>
    <div v-else-if="nid === 172" class="fish_shrimp-small fish_shrimp-5"></div>
    <div v-else-if="nid === 173" class="fish_shrimp-small fish_shrimp-6"></div>
  </template>
  <div v-else>{{ name }}</div>
</template>

<script setup>

const props = defineProps({
  nid: Number,
  cid: Number,
  name: String,
  code: String
})
</script>