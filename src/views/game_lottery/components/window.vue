<template>
  <div
    class="flex flex-col items-center justify-end w-335px flex-none mx-auto rounded-lg h-127px bg-white/[0.3] relative"
  >
    <div
      class="flex items-center justify-center text-xs text-slate-200 bg-[#555555]/[.3] w-140px h-22px rounded-b-md absolute top-0"
    >
      <span>第{{ data.period }}期</span>
    </div>
    <div v-show="!animated && showTip()" class="py-2">
      <renderTip :tips="data.res_tips"></renderTip>
    </div>
    <div v-if="data.res" class="flex items-start justify-center w-full h-2/4">
      <TinsLottery
        :res="data.res"
        :gid="gid"
        :animated="animated"
      ></TinsLottery>
    </div>
  </div>
</template>

<script setup>
import TinsLottery from "./TinsLottery";
import renderTip from "./TinsLottery/renderTip";
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  gid: [Number, String],
  animated: Boolean,
  period: String,
});

const showTip = () => {
  return ["1", "2", "3", "4", "5", "6", "10", "12", "-20", "-101", "-102", '-401', '-402'].includes(
    props.gid
  );
};
</script>
