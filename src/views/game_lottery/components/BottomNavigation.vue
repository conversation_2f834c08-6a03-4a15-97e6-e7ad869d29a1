<template>
  <van-tabbar border v-model="active" :fixed="false" safe-area-inset-bottom route>
    <van-tabbar-item
      v-for="tabbar in tabbars"
      :key="tabbar.icon"
      replace
      safe-area-inset-bottom
      :to="{ name: tabbar.name, query: { game_id: $route.query.game_id } }"
    >
      <span>{{ tabbar.title }}</span>
      <template #icon="props">
        <img :src="props.active ? tabbar.active_icon : tabbar.icon "/>
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>

<script setup>
import a1 from '@/assets/game/home.png';
import a2 from '@/assets/game/home_active.png';
import b1 from '@/assets/game/lottery.png';
import b2 from '@/assets/game/lottery_active.png';
import c1 from '@/assets/game/explain.png';
import c2 from '@/assets/game/explain_active.png';
import d1 from '@/assets/game/order.png';
import d2 from '@/assets/game/order_active.png';

const active = ref(0);
const tabbars = [
  {
    icon: a1,
    active_icon: a2,
    title: "投注",
    name: "LotteryHome",
  },
  {
    icon: b1,
    active_icon: b2,
    title: "开奖",
    name: "LotteryResult",
  },
  {
    icon: c1,
    active_icon: c2,
    title: "说明",
    name: "GameExplain",
  },
  {
    icon: d1,
    active_icon: d2,
    title: "投注记录",
    name: "LotteryRecord",
  },
];
</script>
