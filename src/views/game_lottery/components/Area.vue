<template>
  <div class="flex flex-1 overflow-hidden">
    <div class="flex w-335px mx-auto">
        <div class="flex flex-1 h-full overflow-hidden">
          <div class="w-full overflow-y-scroll py-3 box-border text-[var(--van-gray-7)]">
            <AreaContent 
              v-for="(item, i) in list"
              v-show="activeIndex === i"
              :key="item.id"
              :gid="gid"
              :cid="item.id"
              :list="item.lists"
              :findSelect="(type_id) => findSelect(type_id, item.id)"
              @click="(id) => selectHandle(id, item.id)"
            />
          </div>
        </div>
        <div class="w-90px h-full py-3 box-border overflow-y-scroll overscroll-none">
          <div class="w-75px ml-auto grid grid-cols-1 gap-2">
            <div
              v-for="(item, i) in list"
              :key="item.id"
              :class="['u-tab-item', { 'u-tab-item-active': activeIndex === i }]"
              @click="classHandler(i)"
              :data-length="classLenth(item.id)"
            >
              <span>{{ item.name }}</span>
            </div>
          </div>
        </div>
    </div>
  </div>
</template>

<script setup>
import AreaContent from './AreaContent.vue';
const props = defineProps({
  gid: [Number, String],
  selected: Array,
  list: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['select', 'change']);

const activeIndex = ref(0);

const selectHandle = (type_id, how_id) => {
  emit('select', { type_id, how_id });
}

const classHandler = (i) => {
  if (activeIndex.value !== i) {
    activeIndex.value = i
  }
};

const classLenth = (how_id) => {
  return props.selected.filter(o => o.how_id === how_id)?.length;
}

const findSelect = (type_id, how_id) => {
  return props.selected.findIndex(o => o.how_id === how_id && o.type_id === type_id) > -1
}

</script>

<style lang="less">

.u-tab-item[data-length]:not([data-length="0"])::after {
  content: attr(data-length);
  position: absolute;
  top: -7.5px;
  left: -7.5px;
  width: 15px;
  height: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ff006c;
  color: #fff;
  font-size: 10px;
  border-radius: 50%;
}

</style>