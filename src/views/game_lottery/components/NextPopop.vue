<template>
  <van-popup
    :show="props.show"
    @update:show="onModelShowUpdated"
    :overlay="false"
    teleport="body"
    round
    style="width: 73%"
  >
    <div class="flex flex-col items-center justify-center text-sm">
      <img src="@/assets/game_img/bell.png" class="w-100px my-6">
      <div class="pb-8 text-center">
        <div>{{ title }}-期号</div>
        <div>更新为 {{ period }}</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { watch } from 'vue'

defineOptions({
  name: 'NextPopop'
})

const props = defineProps({
  show: Boolean,
  title: String,
  period: String
})

const emit = defineEmits(['update:show'])

const onModelShowUpdated = (val) => {
  emit('update:show', val)
}

watch(
  () => props.show,
  (val) => {
    if (val) {
      setTimeout(() => {
        onModelShowUpdated(false)
      }, 2000)
    }
  }
)

</script>


<style lang="less" scoped>

</style>