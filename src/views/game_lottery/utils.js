export const RECEIVE_MESSAGE_UPDATE = 'RECEIVE_MESSAGE_UPDATE'
export const COMMON_CHIP_DISPLAYED_STORAGE_KEY = 'COMMON_CHIP_DISPLAYED_STORAGE_KEY'

export function isOfficialLottery (id)  {
  return [10, 11, 12].indexOf(Number(id)) > -1
}

export const reducer = (accumulator, currentValue) => accumulator + currentValue;


export const slicePeriod = (period) => {
  const index = period.indexOf('-');

  if (index > -1) {
    return period.slice(index + 1)
  } else {
    return period
  }
}

// 双倍投注
export const doubleSelect = (preselected, multiple = 2) => {
  preselected.forEach(o => {
    o.point = o.point * multiple
  })
};

// 余额不足，投注剩余额度到第一个玩法
export const doubleSelectWithFirstClass = (preselected, point) => {
  preselected[0].bet_data[0].point = preselected[0].bet_data[0].point + point
}

// 多次投注，合并投注项
export const mergeSelectes = (selected, preselected) => {
  preselected.forEach((p, i) => {
    const index = selected.findIndex(s => s.how_id === p.how_id)
    if (index > -1) {
      p.bet_data.forEach((pc, t) => {
        const tmp = selected[index].bet_data
        const idx = tmp.findIndex(o => o.type_id === pc.type_id)
        if (idx > -1) {
          tmp[idx].point += pc.point
        } else {
          tmp.push(p.bet_data[t])
        }
      })
    } else {
      selected.push(preselected[i])
    }
  })
}

export function numFormat (num) {

  if (!num) {
    return num
  }

  const si = [
    { min: 1, max: 1E4, value: 1, symbol: '' },
    { min: 1E4, max: 1E5, value: 1E3, symbol: 'K' },
    { min: 1E5, max: 1E6, value: 1E4, symbol: 'W' },
  ]

  let i

  for (i = si.length - 1; i > 0; i--) {
    if (num >= si[i].min && num <= si[i].max) {
      break;
    }
  }

  if (num % si[i].min === 0) {
    return (num / si[i].value) + si[i].symbol
  } else {
    return num
  }
}

export const itemClasses = (classId, id) => {
  let classes = `box-${id}-${classId} `
  switch(classId) {
    case 2:
    case 3:
    case 4:
    case 7:
    case 19:
    case 20:
    case 21:
      classes += 'w-6-12 h-2-6'
      break
    case 17:
      classes += 'w-6-12 h-1-4'
    break
    case 6:
    case 11:
    case 13:
    case 16:
    case 26:
    case 27:
      classes += 'w-4-12'
      break
    case 18:
      classes += 'w-3-12'
      break
    default:
      classes += 'w-full'
      break
  }

  return classes
}