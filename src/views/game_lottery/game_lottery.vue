<template>
  <div
    class="flex flex-col h-full"
    style="
      --van-blue: #66d2ff;
      --van-red: #ff76b0;
      --van-green: #00c82f;
      --van-purple: #9810fa;
      --van-gray-7: #808080;
      --van-text-color: #333;
      --van-nav-bar-height: 48px;
      --van-nav-bar-background: transparent;
      --van-nav-bar-title-text-color: white;
      --van-nav-bar-icon-color: white;
      --van-count-down-text-color: #7d51f2;
      --van-count-down-font-size: 20px;
      --van-tabbar-z-index: 0;
      --van-tabbar-background: white;
      --van-tabbar-height: 60px;
      --van-tabbar-item-text-color: #666666;
      --van-tabbar-item-icon-size: 24px;
      --van-tabbar-item-active-color: #333333;
      --van-tabbar-item-active-background: transparent;
      --van-dropdown-menu-background: white;
      --van-dropdown-menu-title-text-color: #666;
      --van-dropdown-menu-title-active-text-color: var(--van-text-color);
      --van-dropdown-menu-shadow: 0 0 #0000;
      --van-popup-background: white;
      --van-calendar-background: white;
      --van-empty-description-color: var(--van-text-color);
      --van-border-color: #e5e5e5;
      color: var(--van-text-color);
    "
  >
    <div class="absolute w-full h-full top-0 left-0 right-0 bottom-0 z-0 app-backgroud"></div>
    <van-nav-bar
      :title="currentLottery?.game_info?.name"
      safe-area-inset-top
      left-arrow
      :border="false"
      @click-left="onClickLeft"
      @click-right="onClickRight"
    >
      <template #right>
        <PopoverComponent
          :show="showPopover"
          @change="onChangeTicket"
        />
      </template>
    </van-nav-bar>

    <div role="main" class="flex flex-col flex-1 overflow-hidden z-1">
      <router-view v-slot="{ Component, route }">
        <keep-alive :include="['LotteryHome', 'LotteryResult']">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
      </router-view>
    </div>

    <BottomNavigation></BottomNavigation>
  </div>
  
</template>
<script setup>
import { storeToRefs } from 'pinia';
import { EventEmitter } from '@mini-code/base-func';
import { useGameStore } from '@/store/game';
import { GetTicketAPI, GetLotteryCategoryAPI } from '@/api/game';
import PopoverComponent from "./components/popover.component.vue";
import BottomNavigation from './components/BottomNavigation.vue';
import { useTimeoutFn } from '@/hooks';
import { RECEIVE_MESSAGE_UPDATE, isOfficialLottery } from './utils';
import './styles/public.less';

defineOptions({
  name: 'GameLottery'
});
const route = useRoute();
const router = useRouter();
const gameStore = useGameStore();
const showPopover = ref(false);

const { isStop, next, currentLottery, currentLotteryPeriod } = storeToRefs(gameStore);

const onClickLeft = () => {
  router.go(-1);
};
const onClickRight = () => {
  showPopover.value = true;
};

const getLotteryCategory = () => {
  GetLotteryCategoryAPI().then((res) => {
    console.log(res, 'getLotteryCategory')
    gameStore.SetGameListAction(res.data)
  })
}

const onChangeTicket = (id) => {
  if (String(id) !== route.query.game_id) {
    gameStore.setCurrentLottery(null);
    gameStore.setCurrentLotteryPeriod(null);
    router.replace({ name: route.name, query: { game_id: id } });
  }
  showPopover.value = false;
};

let refreshCount = 0;

const TimeClaimant = (val) => {
  if (isOfficialLottery(route.query.game_id)) {
    gameStore.setClosedTime(val.end_second);
  } else {
    gameStore.setClosedTime(val.standard_second - val.end_second);
  }

  const t = val.end_time - val.server_time;
  if (t > 0) {
    gameStore.setIsStop(false);
    gameStore.setTime(t);
  } else if (t === 0) {
    gameStore.setIsStop(false);
    gameStore.setTime(1);
  } else {
    gameStore.setIsStop(true);
    if (parseInt(route.query.game_id) <= 9) {
      gameStore.setTime(Math.abs(t));
    } else {
      gameStore.setTime(val.end_second + val.end_time - val.server_time);
    }
  }
};

const claimant = async () => {
  console.log(route.query.game_id)
  try {
    stop();
    gameStore.setLoading(true);
    const { data } = await GetTicketAPI({ game_id: +route.query.game_id });
    console.log(data, 'TicketAPI data');

    gameStore.setCurrentLottery(data.data);
    TimeClaimant(data.data);

    const period = data.data.id;
    const game_id = data.game_id;

    if (isOfficialLottery(game_id)) {
      gameStore.setCurrentLotteryPeriod(period);
      gameStore.setLoading(false);
      const res = data.last_period?.res;
      if (!res) {
        if (!isStop.value) {
          start(30 * 1e3);
        }
      } else {
        stop();
        EventEmitter.emit(RECEIVE_MESSAGE_UPDATE, data.last_period);
      }
    } else {
      if (period === currentLotteryPeriod.value) {
        console.log('未获取到最新信息');
        refreshCount += 1;
        start(refreshCount <= 3 ? refreshCount * 1e3 : 5 * 1e3);
      } else {
        stop();
        refreshCount = 0;
        console.log('已获取到最新信息');
        gameStore.setCurrentLotteryPeriod(period);
        gameStore.setLoading(false);
        gameStore.setNext(false);

        EventEmitter.emit(RECEIVE_MESSAGE_UPDATE, data.last_period);
      }
    }

    
  } catch (error) {
    //
  } finally {
    //
  }
};

const { start, stop } = useTimeoutFn(claimant, {
  immediate: false
});

watch(
  () => route.query.game_id,
  (val) => {
    console.log('route.query.game_id', route.query.game_id);
    if (val) claimant();
  }
);

watch(next, (val) => {
  if (val) claimant();
});

onMounted(() => {
  claimant();
  
})

watchEffect(getLotteryCategory);

onUnmounted(() => {
  stop();
  gameStore.reset();
})

let lastFetch = Date.now() ;
useEventListener(document, 'visibilitychange', () => {
  const now = Date.now();
  if (document.visibilityState === 'visible' && now - lastFetch > 5000) {
    gameStore.setCurrentLotteryPeriod(null);
    lastFetch = now;
    claimant();
  }
})
</script>

<style lang="less">
.app-backgroud {
  background-image: url(@/assets/game_img/bg.png);
  background-repeat: no-repeat;
  background-size: 100% auto;
}

.u-banner {
  height: calc(var(--van-nav-bar-height) + constant(safe-area-inset-top));
  height: calc(var(--van-nav-bar-height) + env(safe-area-inset-top));
}

.u-fixed-bottom {
  padding-bottom: calc(60px + constant(safe-area-inset-bottom));
  padding-bottom: calc(60px + env(safe-area-inset-bottom));
}
</style>
