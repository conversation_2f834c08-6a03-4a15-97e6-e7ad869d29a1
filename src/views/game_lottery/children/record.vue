<template>
    <van-dropdown-menu>
      <van-dropdown-item :title="formatter(pickerValue, 'M月D日')" @open="showDatePopup = true"> </van-dropdown-item>
    </van-dropdown-menu>
    <div class="flex flex-col flex-1 relative overflow-hidden bg-[#dfd9fc]">
      <div class="flex-1 overflow-y-scroll">
        <div class="h-full">
          <div class="flex items-center justify-center h-full" v-if="isFetching && list.length === 0">
            <van-empty
              image="data:image/svg+xml;base64,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"
              description="暂无数据"
            />
          </div>

          <van-pullRefresh v-else v-model="refreshing" @refresh="onRefresh" class="min-h-full">
            <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
              <ul>
                <li class="flex items-center py-3 text-center text-xs">
                  <div class="flex-1">时间</div>
                  <div class="flex-1">游戏</div>
                  <div class="flex-1">期号</div>
                  <div class="flex-1">内容</div>
                  <div class="flex-1">金额</div>
                  <div class="flex-1">状态</div>
                </li>
                <li v-for="val in list" class="flex items-center py-3 text-center text-xs van-hairline--bottom">
                  <div class="flex-1">
                    <div>{{ formatter(val.created_at, 'HH:mm:ss') }}</div>
                  </div>
                  <div class="flex-1">
                    <div class="van-ellipsis">{{ val.name }}</div>
                  </div>
                  <div class="flex-1">
                    <div>{{ slicePeriod(val.period) }}</div>
                  </div>
                  <div class="flex-1">
                    <div>{{ val.play_name }}</div>
                  </div>
                  <div class="flex-1">
                    <div>{{ val.point }}</div>
                  </div>
                  <div class="flex-1">
                    <div :class="[val.is_win === 1 && 'text-red']">
                      {{ val.is_win === 1 ? '已中奖' : val.is_win === 0 ? '未中奖' : '待开奖' }}
                    </div>
                  </div>
                </li>
              </ul>
            </van-list>
          </van-pullRefresh>
        </div>
      </div>
      <div class="w-full flex-none relative bg-white rounded-t-xl h-18 py-3 box-border shadow-top">
        <div class="text-xs flex items-center justify-between px-4 h-full">
          <div class="flex flex-col justify-between h-full">
            <div>
              盈利：<span class="text-green font-bold">{{ statistics?.win_point }}</span>
            </div>
            <div>
              中奖：<span class="text-red font-bold">{{ statistics?.win_money }}</span>
            </div>
          </div>
          <div class="flex flex-col h-full justify-end">
            <div>
              投注：<span class="font-bold">{{ statistics?.win_num }}</span> 注
            </div>
          </div>
        </div>
      </div>
    </div>

  <van-calendar
    v-model:show="showDatePopup"
    :min-date="minDate"
    :max-date="maxDate"
    title="日期选择"
    color="#8b5cf6"
    @confirm="onConfirm"
  />
</template>

<script setup>
import { useListExtra } from '@/hooks';
import { GetLotteryBetRecordAPI } from '@/api/game';
import { slicePeriod } from '../utils';
import { parseDate, formatter } from '@/utils';

const {
  query: { game_id }
} = useRoute();

const showDatePopup = ref(false);

const statistics = reactive({
  win_point: '0.00',
  win_money: '0.00',
  win_num: 0,
});

const pickerValue = ref(new Date());

const minDate = new Date(2025, 0, 1);
const maxDate = new Date();

const format = 'YYYY-MM-DD HH:mm:ss';

const implementationGetParams = () => {
  return {
    game_id: +game_id,
    time_start: formatter(parseDate(pickerValue.value).startOf('day'), format),
    time_end: formatter(parseDate(pickerValue.value).add(1, 'day').startOf('day'), format)
  };
};

const implementationFetched = ({ win_num, win_money, win_point }) => {
  statistics.win_num = win_num;
  statistics.win_money = win_money;
  statistics.win_point = win_point;
};

const { list, isFetching, refreshing, onRefresh, loading, error, finished, onLoad } = useListExtra({
  serverHandle: GetLotteryBetRecordAPI,
  implementationGetParams,
  implementationFetched,
  pagination: { data: 'data.lists' }
});

const onConfirm = (val) => {
  pickerValue.value = val;
  showDatePopup.value = false;
  onRefresh();
};
</script>
