<template>
  <div class="flex flex-col flex-1 h-full overflow-y-scroll relative bg-[#dfd9fc]">
    <div class="h-full">
      <van-pullRefresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
          <ItemComponent
            v-for="val in list"
            :key="val.period"
            :period="val.period"
            :res="val.res"
            :res-tips="val.res_tips"
            :gid="game_id"
          />
        </van-list>
      </van-pullRefresh>
    </div>
  </div>
</template>

<script setup>
import { useListExtra } from '@/hooks';
import { GetTicketRecordAPI } from '@/api/game';
import { RECEIVE_MESSAGE_UPDATE } from '../utils';
import { EventEmitter } from '@mini-code/base-func';
import ItemComponent from '../components/ResultItem';

defineOptions({
  name: 'Lottery'
});

const {
  query: { game_id }
} = useRoute();

const implementationGetParams = () => ({
  game_id: game_id
});

const { list, refreshing, onRefresh, loading, error, finished, onLoad } = useListExtra({
  serverHandle: GetTicketRecordAPI,
  implementationGetParams
});

onActivated(() => {
  EventEmitter.on(RECEIVE_MESSAGE_UPDATE, onRefresh);
});

onDeactivated(() => {
  EventEmitter.rm(RECEIVE_MESSAGE_UPDATE, onRefresh);
});
</script>
