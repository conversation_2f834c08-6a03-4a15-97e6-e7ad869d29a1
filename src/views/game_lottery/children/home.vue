<template>
    <TimerComponent :loading="isLoading" :time="time" :isStop="isStop" :stopCallHandler="stopCallHandler" />
    <WindowComponent :data="currentLottery?.last_period" :gid="route.query.game_id" :animated="isStop" :period="currentLottery?.period" />

    <div class="w-full flex flex-col flex-1 relative rounded-t-xl mt-3 overflow-hidden bg-[#dfd9fc]">
      <AreaComponent :list="list" :gid="Math.abs(route.query.game_id)" :selected="states.selected" @select="onSelect" />
      <ActionComponent
        :balance="wallet.points"
        :selected="states.selected"
        :isStop="isStop"
        :totalAmount="totalAmount"
        @open="showChip = true"
        @multiple="onMultiple"
        @cancel="onCancel"
        @submit="onSubmit"
        @plus="onConfirmBalance"
      />
    </div>

  <ChipPopop v-model:show="showChip" />
  <NextPopop v-model:show="showNext" :title="currentLottery?.game_info?.name" :period="currentLottery?.period" />

  <PopupComponent v-model="show" title="温馨提示" confirmButtonText="去充值" @confirm="onConfirmBalance">
    <div class="tins-dialog__message">余额不足, 是否前往充值</div>
  </PopupComponent>

  <PopupComponent v-model="showNap" title="最后一注" @closed="showNap = false" @confirm="onConfirmNap">
    <div class="tins-dialog__message">
      <div class="nap-point">{{ nap }}</div>
      <div>请确认最后投注金额</div>
    </div>
  </PopupComponent>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useUserStore } from "@/store/user";
import { useGameStore } from '@/store/game';
import { throttle } from 'lodash-es';
import { TicketClassAPI, SetLotteryBetAPI } from '@/api/game';
import TimerComponent from '../components/timer.vue';
import WindowComponent from '../components/window.vue';
import AreaComponent from '../components/Area.vue';
import ActionComponent from '../components/Action.vue';
import { isEmpty } from '@/utils';
import { RECEIVE_MESSAGE_UPDATE } from '../utils';

import PopupComponent from '../components/Popup.vue';
import ChipPopop from '../components/ChipPopop.vue';
import NextPopop from '../components/NextPopop.vue';

import { EventEmitter } from '@mini-code/base-func';

import { reducer, doubleSelect, mergeSelectes } from '../utils';

defineOptions({
  name: 'LotteryHome'
});

const router = useRouter();
const userStore = useUserStore();
const gameStore = useGameStore();
const route = useRoute();
const list = ref([]);
const { wallet } = storeToRefs(userStore);
const { currentLottery, currentLotteryPeriod, balance, chip, time, isStop, isLoading, closedTime, hasHelpKeep, keepLastIssuePreselected } =
  storeToRefs(gameStore);
const displayChip = computed(() => gameStore.getDisplayChip(chip.value));

const stopCallHandler = async () => {
  if (isStop.value) {
    gameStore.setNext(true);
  } else {
    gameStore.setNext(false);
    gameStore.setIsStop(true);
    gameStore.setTime(closedTime.value);
  }
};

const show = ref(false);
const showNap = ref(false);
const showNext = ref(false);
const showChip = ref(false);

const checked = ref(true);

// 孤注一掷金额
const nap = ref(0);

const getTicketClass = async () => {
  try {
    const res = await TicketClassAPI({ id: route.query.game_id });
    list.value = res.data;
  } catch (error) {}
};

const states = reactive({
  selected: [],
  // 点击项
  select: {}
});

const totalAmount = computed(() => {
  return states.selected.reduce((p, t) => {
    p += t.point;
    return p;
  }, 0);
});

const onConfirmNap = () => {
  showNap.value = false;
  handlePickSelect(nap.value);
};

const handlePickSelect = (point) => {
  const { type_id, how_id } = states.select;
  const _index = states.selected.findIndex((o) => o.type_id === type_id);
  if (_index > -1) {
    states.selected.splice(_index, 1);
  } else {
    states.selected.push({ how_id, type_id, point });
  }
};

const onSelect = ({ type_id, how_id }) => {
  states.select = { type_id, how_id };
  // 如果用户金额小于1元最低投注
  if (parseFloat(wallet.value.points) < 1) {
    show.value = true;
    return;
  };


  const _index = states.selected.findIndex((o) => o.type_id === type_id);

  if (_index > -1) {
    states.selected.splice(_index, 1);
  } else {
    
    // 剩余可用金额
    const canPerselectPoint = parseInt(wallet.value.points) - totalAmount.value;

    // 如果可用金额小于1
    if (canPerselectPoint < 1) {
      show.value = true;
      return;
    }

    // 如果剩余可用金额小于所选筹码
    if (canPerselectPoint < displayChip.value.point) {
      nap.value = canPerselectPoint;
      showNap.value = true;
      return;
    };

    states.selected.push({ how_id, type_id, point: displayChip.value.point });

  }
};

// 2 bet
const onMultiple = () => {
  if (states.selected.length === 0) {
    showToast('当前没有投注信息，请先下注');
    return;
  }
  if (parseInt(wallet.value.points) >= totalAmount.value * 2) {
    doubleSelect(states.selected);
  } else {
    const remainingPoint = parseInt(wallet.value.points) - totalAmount.value;

    states.selected[0].point = states.selected[0].point + remainingPoint;
  }
};

//
const onConfirmBalance = () => {
  router.push({ path: '/cz' });
  show.value = false;
};

// 取消按钮
const onCancel = () => {
  states.selected = [];
};

// 确认按钮
const onSubmit = throttle(async () => {
  if (states.selected.length === 0) {
    showToast('当前没有投注信息，请先下注');
    return;
  }

  const toast = showLoadingToast();

  try {
    const result = Object.values(
      states.selected.reduce((acc, cur) => {
        if (!acc[cur.how_id]) {
          acc[cur.how_id] = { how_id: cur.how_id, bet_data: [] };
        }
        acc[cur.how_id].bet_data.push({
          point: cur.point,
          type_id: cur.type_id
        });
        return acc;
      }, {})
    );

    const res = await SetLotteryBetAPI({
      id: currentLotteryPeriod.value,
      game_id: +route.query.game_id,
      points: totalAmount.value,
      bet: result
    });

    if (res) {
      states.select = {};
      states.selected = [];
      showToast('下注成功');
      userStore.updateUserWalletData();
    }
  } catch (e) {
    states.select = {};
  } finally {
    toast.close();
  }
}, 1000, { trailing: false })

const handleUpdate = () => {
  userStore.updateUserWalletData();
  if (!isEmpty(states.selected)) {
    showNext.value = true;
  }

  // if (!isEmpty(states.selected)) {
  //   if (!hasHelpKeep.value) {
  //     showNext.value = true;
  //   }
  // }

  // if (!keepLastIssuePreselected.value && hasHelpKeep.value) {
  //   states.preselected = [];
  // }
};

onMounted(() => {
  getTicketClass();
  userStore.updateUserWalletData();
});

onActivated(() => {
  EventEmitter.on(RECEIVE_MESSAGE_UPDATE, handleUpdate);
});

onDeactivated(() => {
  EventEmitter.rm(RECEIVE_MESSAGE_UPDATE, handleUpdate);
});
</script>
