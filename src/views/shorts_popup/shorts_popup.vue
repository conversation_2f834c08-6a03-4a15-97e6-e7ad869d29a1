<template>
  <PageLayout>
    <div class="flex flex-col flex-1 relative z-[1] h-full">
      <div class="z-50 w-full mx-auto fixed">
        <HeaderComponents
          :similarity="params.similarityType"
          :duration="params.durationType"
          :callBackHandle="callBackHandle"
          @click-right="clickRight"
        />
      </div>
      <DanmuLayer :banners="banners" :swiper-index="swiperIndex" :videoTime="videoTime"></DanmuLayer>
      <div class="flex-1 h-full">
        <van-pull-refresh v-model="loading" @refresh="onRefresh" :disabled="swiperIndex !== 0" class="w-screen h-full">
          <Swiper virtual nested :modules="[Virtual]" direction="vertical" class="h-full" @swiper="onSwiper" @slideChange="onSlideChange">
            <SwiperSlide v-for="(val, index) in states.items" :key="val.id" :virtualIndex="index" v-slot="{ isActive, isNext, isPrev }">
              <div class="short_video_items w-full h-full relative">
                <div class="w-full h-full">
                  <VideoComponents
                    v-if="index === swiperIndex && val.isAd !== true"
                    :vid="val.id"
                    :isCloudMode="val.isCloudMode"
                    :cloudFileId="val.cloudFileId"
                    :playUrl="val.playUrl"
                    :cloudUrl="val.cloudUrl"
                    :videoCover="val.videoCover"
                    :videoDuration="val.videoDuration"
                    :playCall="playHandle"
                    :timeupdateCall="timeupdateHandle"
                    :similarityType="params.similarityType"
                    :durationType="params.durationType"
                    @left="leftHandle"
                  />

                  <div v-else-if="index !== swiperIndex && val.isAd !== true" class="full-wrapper relative w-full h-full overflow-hidden">
                    <ImgComponents :imgUrl="val.videoCover" />
                  </div>

                  <div v-else class="full-wrapper relative w-full h-full overflow-hidden" @click="navigateHandle(val)">
                    <ImgComponents :imgUrl="val.picUrl" />
                  </div>
                </div>

                <div>
                  <LeftContent :title="val.title" :tags="val.tags"></LeftContent>

                  <RightContent
                    v-if="index === swiperIndex && val.isAd !== true"
                    :vid="val.id"
                    :isCollected="val.isCollected"
                    :likeTimes="val.likeTimes"
                    :commentTimes="val.commentTimes"
                    :shareTimes="val.shareTimes"
                  />
                </div>
              </div>
            </SwiperSlide>
          </Swiper>
        </van-pull-refresh>
      </div>
    </div>
    <PromptComponents
      v-model="visible"
      title="温馨提示"
      content="升级VIP可观看视频，每日无限观影~"
      cancelText="推广送VIP"
      confirmText="升级VIP"
      :closeOnClickOverlay="true"
      :hasCancelBtn="true"
      :confirmCall="promptConfirmHandler"
      :cancelCall="promptCancelHandler"
    />
  </PageLayout>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { shortVideoList } from '@/api/video';
import { useAppStore } from '@/store/app';
import { useUserStore } from '@/store/user';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Virtual } from 'swiper/modules';
import 'swiper/css';

import { onMountedOrActivated } from '@vant/use';
import Notice from '@/components/notice/notice.vue';
import DanmuLayer from '@/components/danmuLayer/danmuLayer.vue';
import EmptyPage from '@/components/empty/empty';
import PromptComponents from '@/components/prompt/prompt';
import HeaderComponents from './components/header.vue';
import VideoComponents from './components/video';
import LeftContent from './components/leftContent';
import RightContent from './components/rightContent';
import { uActions } from '@/api/user';
import { useNavigate } from '@/hooks';

defineOptions({
  name: 'Shorts'
});

const router = useRouter();
const appStore = useAppStore();
const userStore = useUserStore();

const notice = computed(() => appStore.adItem(21));
const banners = computed(() => appStore.adItem(26));

const { navigateTo } = useNavigate();

const loading = ref(false);
const visible = ref(false);

const { vipLevel, vipValidTime, shortVideoFreeWatchLeft } = storeToRefs(userStore);

const swiperRef = ref(null);

const showShare = ref(false);

const swiperIndex = ref(0);

const videoTime = ref(0);

const ad = computed(() => appStore.adItem(9));

const clickRight = () => {
  router.push({ path: '/search' });
};

const params = reactive({
  pageIndex: 1,
  pageSize: 20,
  similarityType: 0,
  durationType: 0,
  tags: []
});

const callBackHandle = (val, type) => {
  params[type] = val;
  if (type === 'similarityType') {
    const tmp = states.items[swiperIndex.value];
    if (tmp && tmp.isAd !== true) {
      params.tags = tmp.tags;
    }
  }
  onRefresh(true);
};

const states = reactive({
  items: [],
  list: []
});

function insertAds(list, ads, interval = 5) {
  if (!ads || ads.length === 0) return list;
  const result = [];
  let adIndex = 0;
  for (let i = 0; i < list.length; i++) {
    result.push(toRaw(list[i]));
    if ((i + 1) % interval === 0 && ads.length > 0) {
      result.push({ ...toRaw(ads[adIndex]), isAd: true });
      adIndex = (adIndex + 1) % ads.length;
    }
  }
  return result;
}

const onRefresh = async (flag) => {
  swiperIndex.value = 0;
  params.pageIndex = 1;
  if (flag !== true) {
    params.similarityType = 0;
    params.durationType = 0;
    params.tags = [];
  }
  states.list = [];
  try {
    await getShortVideoList(true);
  } finally {
    loading.value = false;
  }
};

const getShortVideoList = async (isRefresh = false) => {
  try {
    const { data } = await shortVideoList(params);
    const list = data.list;
    if (list.length) {
      if (isRefresh) {
        states.list = list; // 刷新时直接替换
      } else {
        states.list = states.list.concat(list); // 分页时追加
      }
      states.items = insertAds(states.list, ad.value);
    } else {
      states.list = [];
      states.items = [];
    }
  } catch (e) {
    states.list = [];
    states.items = [];
  }
};

const promptCancelHandler = () => {
  visible.value = false;
  router.push({ path: '/invite' });
};

const promptConfirmHandler = () => {
  visible.value = false;
  router.push({ path: '/vip' });
};

const navigateHandle = async ({ content, jumpType, id }) => {
  navigateTo(content, jumpType);
  await uActions({ actionType: 11, eventId: id });
};

const onSwiper = (swiper) => {
  swiperRef.value = swiper;
};

const onSlideChange = (swiper) => {
  swiperIndex.value = swiper.activeIndex;
};

const leftHandle = () => {
  shortVideoFreeWatchLeft.value -= 1;
};

const playHandle = () => {
  if (vipLevel.value > 0 || vipValidTime.value > 0) {
    return true;
  } else {
    if (shortVideoFreeWatchLeft.value < 1) {
      visible.value = true;
      return false;
    } else {
      return true;
    }
  }
};

const timeupdateHandle = (time) => {
  videoTime.value = time;
}

watch(swiperIndex, (val) => {
  if (states.items.length === swiperIndex.value + 1) {
    params.pageIndex += 1;
    setTimeout(() => {
      getShortVideoList();
    }, 200);
  }
});

onMounted(() => {
  getShortVideoList();
});
</script>
