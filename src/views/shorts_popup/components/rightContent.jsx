import love from '@/assets/icons/love.png';
import love_n from '@/assets/icons/love-n.png';
import msg from '@/assets/icons/msg.png';
import share from '@/assets/icons/share.png';
import { uActions } from '@/api/user';
import PopupComponents from './popup';

const RightContent = defineComponent({
  name: 'RightContent',
  props: {
    vid: Number,
    isCollected: Boolean,
    likeTimes: Number,
    commentTimes: Number,
    shareTimes: Number
  },
  setup(props) {

    const visible = ref(false);

    const isCollect = ref(props.isCollected ?? false);
    const likeTimes = ref(props.likeTimes);

    const likeHandle = async (e) => {
      isCollect.value = !isCollect.value
      if (isCollect.value) {
        likeTimes.value += 1;
      } else {
        likeTimes.value -= 1;
      }
      await uActions({ actionType: isCollect.value ? 15 : 16, eventId: props.vid })
    }

    watch(() => props.isCollected, (newVal) => {
      isCollect.value = newVal;
    });

    const shareHandle = () => {
      visible.value = true;
    };

    return () => (
      <div className="rightContent_right_content absolute right-0 bottom-16 z-[100]">
        <div className="flex flex-col justify-center items-center">
          <div className="flex justify-center items-center w-12 h-12" onClick={likeHandle}>
            <img src={isCollect.value ? love_n : love} className="w-7.5 h-7" />
          </div>
          <div className="text-xs font-normal">{ likeTimes.value }</div>
        </div>
        <div className="flex flex-col justify-center items-center">
          <div className="flex justify-center items-center w-12 h-12">
            <img src={msg} className="w-7" />
          </div>
          <div className="text-xs font-normal">{ props.commentTimes }</div>
        </div>
        <div className="flex flex-col justify-center items-center">
          <div className="flex justify-center items-center w-12 h-12" onClick={shareHandle}>
            <img src={share} className="h-6" />
          </div>
          <div className="text-xs font-normal">{ props.shareTimes }</div>
          <PopupComponents v-model={visible.value}></PopupComponents>
        </div>
      </div>
    )
  }
})

export default RightContent;