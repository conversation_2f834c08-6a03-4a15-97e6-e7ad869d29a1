<template>
  <SecondPageLayout>
    <van-tabs v-model:active="listParams.mediaType" :line-width="20" @change="onChange">
      <van-tab v-for="tab in tabs" :key="tab.key" :title="tab.title" :name="tab.key"></van-tab>
    </van-tabs>
    <div class="flex-1 px-3 pt-3 overflow-y-auto">
      <van-pullRefresh v-model="refreshing" @refresh="onRefresh" class="min-h-full">
        <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
          <FocusLongModule v-if="listParams.mediaType === 1" :list="list"></FocusLongModule>
          <FocusShortModule v-else-if="listParams.mediaType === 2" :list="list"></FocusShortModule>
        </van-list>
        <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />
      </van-pullRefresh>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useList } from '@/hooks';
import { isEmpty } from '@/utils';
import { uCollectVideoHistory } from '@/api/user';
import EmptyPage from '@/components/empty/empty';
import FocusLongModule from './components/focus_long_video.vue';
import FocusShortModule from './components/focus_short_video.vue';

const tabs = [
  { title: '长视频', key: 1 },
  { title: '短视频', key: 2 }
];

const listParams = reactive({
  mediaType: 1
});

const { list, isFetching, refreshing, loading, error, finished, finishedText, onRefresh, onLoad } = useList({
  immediateParams: listParams,
  serverHandle: uCollectVideoHistory
});

const onChange = (val) => {
  list.value = [];
  onRefresh();
};
</script>
