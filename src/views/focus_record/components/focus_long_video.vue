<template>

<div class="grid grid-cols-2 gap-1.5">
  <MiddleVideoCover
    v-for="val in list"
    :key="val.id"
    :title="val.title"
    :imgUrl="val.videoCover"
    :tags="val.tags"
    :views="val.viewTimes"
    :time="val.videoDuration"
    :vid="val.id"
    :type="val.type"
    :isWatched="val.isWatched"
  />
</div>
</template>

<script setup>

import MiddleVideoCover from "@/components/video/MiddleVideoCover";
const props = defineProps({
  list: {
    type: Array,
    default: () => []
  }
})

</script>