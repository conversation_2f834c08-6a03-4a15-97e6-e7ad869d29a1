<template>
  <SecondPageLayout>
    <template #navbar>
      <van-nav-bar
        safe-area-inset-top
        left-arrow
        :left-text="$route.meta.title"
        @click-left="onClickLeft"
        @click-right="onClickRight"
        :border="false"
      >
        <template #right>
          <div role="button" class="text-[var(--van-primary-color)]">邀请记录</div>
        </template>
      </van-nav-bar>
    </template>
    <div class="flex-1 overflow-auto">
      <div class="flex flex-col items-center w-[345px] h-100 py-5 box-border mx-auto rounded-lg bg-[var(--van-black-500)]">
        <div class="flex items-center mx-4 mb-6">
          <!-- <img src="/pwa-512x512.png" class="w-[60px] h-[60px] rounded-md"> -->
          <div className="w-15 h-15 overflow-hidden" v-if="merchantLogo">
            <ImgComponents :imgUrl="merchantLogo?.invite_friend_logo" />
          </div>

          <div class="ml-3">
            <p class="text-lg mb-2">{{ title }}</p>
            <p class="text-xs">邀请好友注册,自动叠加无限制1天观看</p>
          </div>
        </div>

        <van-swipe class="w-full" :loop="false" indicator-color="white" @change="changeHandle">
          <van-swipe-item v-for="item in swipers" :key="item.id">
            <div class="w-full flex flex-col items-center justify-center">
              <div class="p-2 bg-white rounded ">
                <div class="w-32 h-32">
                  <img :src="item.qrcode" alt="">
                </div>
              </div>
              <p class="text-sm text-center mt-4">邀请码：{{ inviteCode }}</p>
            </div>
          </van-swipe-item>
        </van-swipe>
        <div class="mt-auto" style="--van-button-default-height: 40px;">
          <van-space>
            <van-button type="primary" class="w-100px" @click="copyHandler">复制链接</van-button>
            <van-button type="primary" class="w-100px">截图保存</van-button>
          </van-space>
        </div>
      </div>
      <div class="pb-12">
        <div class="w-88px h-28px my-4 flex items-center justify-center text-sm rounded-r-full text-[var(--van-black)] bg-[var(--van-primary-color)]">
          <span>规则说明</span>
        </div>
        <ul class="text-sm mx-4 leading-6">
          <li>1.邀请好友注册成功即可获得VIP1无限制观看权限1天,可通过邀请好友数量叠加</li>
          <li>2.点击保存图标或复制链接,将链接发送给好友或扫描保存的二维码,让好友下载App,注册并登录。</li>
          <li>3.用户为iPhone用户分享链接时,进入其它App请允许粘贴复制权限。</li>
        </ul>
        <div class="w-88px h-28px my-4 flex items-center justify-center text-sm rounded-r-full text-[var(--van-black)] bg-[var(--van-primary-color)]">
          <span>邀请步骤</span>
        </div>

        <div class="pl-46px overflow-hidden">
          <div class="text-sm">
            <div class="py-2.5 block relative">
              <div>
                <p>第一步:</p>
                <p>点击"保存图片"或复制链接</p>
              </div>
              <div class="absolute top-4 left-[-28px] z-[1]">
                <div class="w-15px h-15px rounded-full bg-[var(--van-primary-color)]"></div>
              </div>
              <div class="absolute h-full w-px top-4 left-[-21px] bg-[var(--van-black-500)]"></div>
            </div>
            <div class="py-2.5 block relative">
              <div>
                <p>第二步:</p>
                <p>将保存的图片或复制的链接通过各种渠道发送出去</p>
              </div>
              <div class="absolute top-4 left-[-28px] z-[1]">
                <div class="w-15px h-15px rounded-full bg-[var(--van-primary-color)]"></div>
              </div>
              <div class="absolute h-full w-px top-4 left-[-21px] bg-[var(--van-black-500)]"></div>
            </div>
            <div class="py-2.5 block relative">
              <div>
                <p>第三步:</p>
                <p>被邀请用户下载注册并登录App,立即生效</p>
              </div>
              <div class="absolute top-4 left-[-28px] z-[1]">
                <div class="w-15px h-15px rounded-full bg-[var(--van-primary-color)]"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </SecondPageLayout>
</template>
<script setup>
import { useAppStore } from '@/store/app';
import { userPromotion } from "@/api/user";
import { listSource } from '@/api/promotion';
import useClipboard from 'vue-clipboard3';
import { useQRCode } from '@vueuse/integrations/useQRCode';

const router = useRouter();
const title = import.meta.env.VITE_APP_TITLE;


const appStore = useAppStore();
const merchantLogo = computed(() => appStore.appConfig?.merchantLogo);

const { toClipboard } = useClipboard();

const swipers = ref([]);
const inviteCode = ref('');
const swiperIndex = ref(0);

const onClickLeft = () => history.back();

const onClickRight = () => {
  router.push({ path: '/invite_record' })
}

const onRefresh = () => {
  listSource().then(({ data: { list, code } }) => {
    inviteCode.value = code;
    swipers.value = (list || []).map((o) => ({
      ...o,
      qrcode: useQRCode(o.url, {
        width: 128,
        height: 128,
        margin: 0
      })
    }));
  });
};

const changeHandle = (index) => {
  swiperIndex.value = index;
};

const copyHandler = () => {
  const swiper = swipers.value[swiperIndex.value];
  toClipboard(swiper.url);
  showToast('复制成功');
}

onRefresh();
</script>
