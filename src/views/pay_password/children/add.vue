<template>
  <SecondPageLayout>
    <div class="flex flex-col items-center w-11/12 box-border mt-4 py-3 mx-auto relative text-sm">
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-3">支付密码</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm" @click="showPicker = true">
            <input
              v-model="params.payPwd"
              type="number"
              name="payPwd"
              autocomplete="off"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请输入支付密码(6位数字)"
            />
          </div>
        </div>
      </div>
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-4">确认密码</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm">
            <input
              v-model="params.confirmPayPwd"
              type="number"
              name="confirmPayPwd"
              autocomplete="off"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请再次输入支付密码"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="flex flex-col my-8 w-10/12 mx-auto">
      <van-button type="primary" round block @click="handleSubmit">确认</van-button>
    </div>

    <PromptComponents
      v-model="visible"
      title="温馨提示"
      content="请选绑定手机号"
      confirmText="绑定"
      :hasCancelBtn="true"
      :confirmCall="confirmHandler"
      :cancelCall="() => toggle(false)"
    />

  </SecondPageLayout>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import PromptComponents from "@/components/prompt/prompt";
import { setPayPwd } from '@/api/user';
import { useUserStore } from "@/store/user";

const router = useRouter()
const userStore = useUserStore();

const params = reactive({
  payPwd: '',
  confirmPayPwd: ''
});

const { tel } = storeToRefs(userStore)


const [visible, toggle] = useToggle(false);

const confirmHandler = () => {
  router.push({ path: "/bind_phone" });
}

const handleSubmit = (e) => {
  if (!params.payPwd) {
    showToast('请输入支付密码')
    return
  }

  if (!/^\d{6}$/.test(params.payPwd)) {
    showToast('请输入6位数字')
    return
  }

  if (params.payPwd !== params.confirmPayPwd) {
    showToast('两次密码输入不一致')
    return
  }

  if (!tel.value) {
    toggle(true)
    return
  }

  const toast = showLoadingToast({ duration: 0 });

  setPayPwd({
    payPwd: String(params.payPwd),
    confirmPayPwd: String(params.confirmPayPwd)
  }).then(() => {
    router.go(-1)
    userStore.updatePersonData()
  }).finally(() => {
    toast.close()
  })
}

</script>