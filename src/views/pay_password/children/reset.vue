<template>
  <SecondPageLayout>
    <div class="flex flex-col items-center w-11/12 box-border mt-4 py-3 mx-auto relative text-sm">
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-3">旧密码</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm" @click="showPicker = true">
            <input
              v-model="params.oldPayPwd"
              type="text"
              name="oldPayPwd"
              autocomplete="off"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请输入旧密码"
            />
          </div>
        </div>
      </div>
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-3">新密码</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm" @click="showPicker = true">
            <input
              v-model="params.newPayPwd"
              type="text"
              name="newPayPwd"
              autocomplete="off"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请输入新密码(6位数字)"
            />
          </div>
        </div>
      </div>
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-4">确认密码</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm">
            <input
              v-model="params.newConfirmPayPwd"
              type="text"
              name="newConfirmPayPwd"
              autocomplete="off"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请再次输入新密码"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="flex flex-col my-8 w-10/12 mx-auto">
      <van-button type="primary" round block @click="handleSubmit">确认</van-button>

      <router-link
        to="/pay_password_recover"
        class="text-xs block mt-4 text-center underline"
      >找回密码</router-link>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { resetPayPwd } from '@/api/user';

const router = useRouter()

const params = reactive({
  oldPayPwd: '',
  newPayPwd: '',
  newConfirmPayPwd: ''
});


const handleSubmit = (e) => {
  if (!params.oldPayPwd) {
    showToast('请输入旧密码')
    return
  }
  if (!params.newPayPwd) {
    showToast('请输入新密码')
    return
  }

  if (!/^\d{6}$/.test(params.newPayPwd)) {
    showToast('请输入6位数字')
    return
  }

  if (params.newPayPwd !== params.newConfirmPayPwd) {
    showToast('两次密码输入不一致')
    return
  }

  const toast = showLoadingToast({ duration: 0 });

  resetPayPwd({
    oldPayPwd: params.oldPayPwd,
    newPayPwd: params.newPayPwd,
    newConfirmPayPwd: params.newConfirmPayPwd
  }).then(() => {
    router.go(-1)
    // userStore.updatePersonData()
  }).finally(() => {
    toast.close()
  })
}

</script>