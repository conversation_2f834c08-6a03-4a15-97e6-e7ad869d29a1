<template>
  <SecondPageLayout>
    <div class="flex flex-col items-center w-11/12 box-border mt-4 py-3 mx-auto relative text-sm">
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-3">手机号</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm" @click="showPicker = true">
            <input
              v-model="_tel"
              type="text"
              name="code"
              readonly
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请输入验证码"
            />
          </div>
          
        </div>
      </div>
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-3">验证码</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm" @click="showPicker = true">
            <input
              v-model="params.code"
              type="text"
              name="code"
              autocomplete="off"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请输入验证码"
            />
          </div>
          <div role="button" style="margin-right: -8px;">
            <van-button type="primary" class="!rounded-lg" size="small" @click="clickHandler">
              <span v-if="isActive">还剩{{ t }}秒</span>
              <span v-else>发送验证码</span>
            </van-button>
          </div>
        </div>
      </div>
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-3">支付密码</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm" @click="showPicker = true">
            <input
              v-model="params.newPayPwd"
              type="text"
              name="newPayPwd"
              autocomplete="off"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请输入支付密码(6位数字)"
            />
          </div>
        </div>
      </div>
      <div class="flex flex-col w-full relative mb-3 px-3 box-border">
        <label for="" class="my-4">确认密码</label>
        <div className="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-lg px-3">
          <div className="flex-1 text-white text-sm">
            <input
              v-model="params.newConfirmPayPwd"
              type="text"
              name="newConfirmPayPwd"
              autocomplete="off"
              className="w-full text-left bg-transparent resize-none border-0"
              placeholder="请再次输入支付密码"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="flex flex-col my-8 w-10/12 mx-auto">
      <van-button type="primary" round block @click="handleSubmit">确认</van-button>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { recoverPayPwd, uGetTelCode } from '@/api/user';
import { useUserStore } from "@/store/user";

const router = useRouter()
const userStore = useUserStore();

const { tel } = storeToRefs(userStore);

const _tel = computed(() => tel.value.replace(/(\d{3})\d*(\d{4})/, '$1****$2'))

const params = reactive({
  newPayPwd: '',
  newConfirmPayPwd: ''
});

const t = ref(60)

const { pause, resume, isActive } = useIntervalFn(() => {
  if (t.value > 1) {
    t.value -= 1
  } else {
    pause()
  }
}, 1000, { immediate: false })

const clickHandler = () => {
  if (isActive.value) return;
  const toast = showLoadingToast({ duration: 0 });
  uGetTelCode({ tel: tel.value, codeType: 'RecoverPayPwd' }).then(() => {
    resume()
    showToast({
      message: '发送成功',
      duration: 1000
    })
  }).finally(() => {
    toast.close()
  })
}

const handleSubmit = (e) => {
  if (!params.code) {
    showToast('请输入验证码')
    return
  }
  if (!params.newPayPwd) {
    showToast('请输入支付密码')
    return
  }

  if (!/^\d{6}$/.test(params.newPayPwd)) {
    showToast('请输入6位数字')
    return
  }

  if (params.newPayPwd !== params.newConfirmPayPwd) {
    showToast('两次密码输入不一致')
    return
  }

  const toast = showLoadingToast({ duration: 0 });

  recoverPayPwd({
    code: params.code,
    newPayPwd: params.newPayPwd,
    newConfirmPayPwd: params.newConfirmPayPwd
  }).then(() => {
    router.go(-1)
    // userStore.updatePersonData()
  }).finally(() => {
    toast.close()
  })
}

</script>