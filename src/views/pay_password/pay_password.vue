<template><div></div></template>

<script setup>
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store/user";
const userStore = useUserStore();
const router = useRouter();
const { is_set_payment_password } = storeToRefs(userStore);

if (is_set_payment_password.value === 0) {
  router.replace({ path: '/pay_password_set' })
} else {
  router.replace({ path: '/pay_password_reset' })
}

</script>