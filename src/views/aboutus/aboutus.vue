<template>
  <SecondPageLayout>
    <div class="flex flex-col items-center grow shrink px-6">
      <h2 class="text-lg text-white my-3">{{ short_name }}</h2>
      <span class="text-xs text-[var(--van-gray-4)]">{{ version }}</span>
      <ul class="w-full">
        <li
          v-for="item in list"
          :key="item.option_code"
          :class="item.option_code === 'aboutus_content' ? 'text-xs text-center text-[var(--van-gray-5)] py-4' : 'w-full h-10 flex items-center justify-center text-sm bg-[#292929] rounded-full mb-4'"
          @click="copy(item.option_value)"
        >
          <span v-if="item.option_code === 'aboutus_content'">{{ item.option_value }}</span>
          <span v-else class="text-[var(--van-primary-color)]">{{ item.option_title }}{{ item.option_value }}</span>
        </li>
      </ul>

      <van-button block type="primary" @click="goSite">去官网</van-button>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useAppStore } from '@/store/app';
import useClipboard from "vue-clipboard3";
import { find } from 'lodash-es';

const appStore = useAppStore();

const version = __APP_VERSION__;
const short_name = import.meta.env.VITE_APP_SHORT_TITLE;

const list = computed(() => appStore.appConfig?.aboutUs);

const { toClipboard } = useClipboard();

const copy = async (txt) => {
  try {
    // 复制
    await toClipboard(txt);
    showSuccessToast({
      message: "复制成功",
      icon: "passed",
    });
    // 复制成功
  } catch (e) {
    // 复制失败
  }
};

const goSite = () => {
  const tmp = find(list.value, ['option_code', 'aboutus_website']);
  const txt = tmp && tmp.option_value;
  window.dsBridge?.call('app.openExternalApp', txt);
};
</script>
