<template>
  <SecondPageLayout>
    <ul class="text-sm">
      <li class="flex items-center justify-between h-[55px] bg-[#333] px-4 border-b border-[#7f7f7f] border-solid">
        <span>平台类型</span>
        <span>{{ query.platform_name }}</span>
      </li>
      <li class="flex items-center justify-between h-[55px] bg-[#333] px-4 border-b border-[#7f7f7f] border-solid">
        <span>订单状态</span>
        <span>{{ query.order_status }}</span>
      </li>
      <li class="flex items-center justify-between h-[55px] bg-[#333] px-4 border-b border-[#7f7f7f] border-solid">
        <span>游戏名</span>
        <span>{{ query.game_name }}</span>
      </li>
      <li class="flex items-center justify-between h-[55px] bg-[#333] px-4 border-b border-[#7f7f7f] border-solid">
        <span>下注时间</span>
        <span>{{ query.created_at }}</span>
      </li>
      <li class="flex items-center justify-between h-[55px] bg-[#333] px-4 border-b border-[#7f7f7f] border-solid">
        <span>下注总金额</span>
        <span>{{ query.point }}</span>
      </li>
      <li class="flex items-center justify-between h-[55px] bg-[#333] px-4 text-sm">
        <span>中奖金额</span>
        <span>{{ query.re_point }}</span>
      </li>
      <li class="flex items-center justify-between h-[55px] bg-[#333] px-4 mt-5">
        <span>订单号</span>
        <div class="flex items-center" @click="copyHandler(query.order_no)">
          <span class="mr-2">{{ query.order_no }}</span>
          <SvgIcon name="copy" color='#ffd631' style="font-size: 14px;" />
        </div>
      </li>
    </ul>
  </SecondPageLayout>
</template>

<script setup>
import useClipboard from 'vue-clipboard3';

const { query } = useRoute()
const { toClipboard } = useClipboard();

const copyHandler = (value) => {
  toClipboard(value);
  showToast('复制成功');
}
</script>