<template>
  <van-swipe class="h-full" ref="swiperRef" :loop="false" :initial-swipe="modelValue" :show-indicators="false" observer css-mode @change="onSlideChange">
    <van-swipe-item v-for="item in list" :key="item.id">
      <TaskLayout v-if="item.type === 1" />
      <PageLayout v-else-if="item.category === 1" :nid="item.id" :title="item.title" />
      <ChannelLayout v-else-if="item.category === 2" :nid="item.id" :title="item.title" />
    </van-swipe-item>
  </van-swipe>
</template>

<script setup>
import PageLayout from './PageLayout.vue';
import TaskLayout from './TaskLayout.vue';
import ChannelLayout from './ChannelLayout.vue';

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0, // 默认显示第一个 Slide
  },
  callBackHandle: Function,
  list: {
    type: Array,
    default: []
  }
});

const emit = defineEmits(['update:modelValue'])

const swiperRef = ref(null);

const swiperIndex = ref(0);

const onSlideChange = (index) => {
  swiperIndex.value = index;
  emit('update:modelValue', index);
  props.callBackHandle(index);
  // updateData({ tabIndex: swiper.activeIndex })
  // state.value.updateData?.({ tabIndex: swiper.activeIndex })
};

watch(
  () => props.modelValue,
  (newIndex) => {
    if (newIndex !== swiperIndex.value) {
      swiperIndex.value = newIndex;
      swiperRef.value.swipeTo(newIndex, { immediate: true });
    }
  }
);

</script>
