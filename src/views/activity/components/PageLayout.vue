<template>
  <div class="px-3 mt-4 h-full overflow-scroll scroll-container">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="min-h-full">
      <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
        <div v-for="item in list" :key="item.id" class="w-full h-28 rounded-md overflow-hidden mb-3" @click="navigateHandle(item)">
          <ImgComponents :imgUrl="item.pic_url"></ImgComponents>
        </div>
      </van-list>
      <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />
    </van-pull-refresh>
  </div>
</template>

<script setup>
import { useListExtra } from '@/hooks';
import { listActivity } from '@/api/activity';
import EmptyPage from '@/components/empty/empty';
import { useNavigate } from '@/hooks';

const router = useRouter();

const { navigateTo } = useNavigate();

const props = defineProps({
  nid: Number,
  title: String
});

const implementationGetParams = () => ({
  type: props.nid
});

const { list, refreshing, loading, error, finished, onRefresh, onLoad } = useListExtra({
  serverHandle: listActivity,
  implementationGetParams,
  pagination: { data: 'data.list' }
});

const navigateHandle = ({ id, jump_url, jump_type }) => {
  if (jump_type === 1) {
    navigateTo(jump_url, jump_type);
  } else {
    router.push({ path: '/webview_activity', query: { activity_id: id } })
  }
};
</script>
