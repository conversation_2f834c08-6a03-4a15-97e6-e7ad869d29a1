<template>
  <div class="px-3 mt-4 h-full overflow-scroll scroll-container">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="min-h-full">
      <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
        <div v-for="item in list" :key="item.id" class="flex items-center justify-between py-3">
          <div class="flex items-center justify-center">
            <div class="rounded-full overflow-hidden w-50px h-50px">
              <ImgComponents :imgUrl="item.pic_url"></ImgComponents>
            </div>
            <div class="ml-3">
              <p>{{ item.title }}</p>
              <span class="text-xs text-[#fff1bb] van-ellipsis">{{ item.sub_title }}</span>
            </div>
          </div>
          <div class="flex flex-col items-end justify-center">
            <div class="flex items-center">
              <div class="flex items-center mb-1.5" v-if="item.award_vip_day">
                <img src="@/assets/icons/VIP-icon.png" class="w-12">
                <span class="ml-1 text-xs text-[var(--van-primary-color)]">+{{ item.award_vip_day }}天</span>
              </div>
              <div class="flex items-center ml-2 mb-1.5" v-if="item.award_bonus">
                <img src="@/assets/icons/glod-icon.png" class="h-15.5px">
                <span class="ml-1 text-xs text-[var(--van-primary-color)]">+{{ item.award_bonus }}</span>
              </div>
            </div>
            <van-button size="mini" type="primary" round class="w-85px" @click="navigateHandle(item)">去下载</van-button>
          </div>
        </div>
      </van-list>
      <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />
    </van-pull-refresh>
  </div>
</template>

<script setup>
import { useListExtra } from '@/hooks';
import { uActions } from '@/api/user';
import { listActivity } from '@/api/activity';
import EmptyPage from '@/components/empty/empty';
import { useNavigate } from '@/hooks';

const router = useRouter();

const { navigateTo } = useNavigate();

const props = defineProps({
  nid: Number,
  title: String
});

const implementationGetParams = () => ({
  type: props.nid
});

const { list, refreshing, loading, error, finished, onRefresh, onLoad } = useListExtra({
  serverHandle: listActivity,
  implementationGetParams,
  pagination: { data: 'data.list' }
});

const navigateHandle = async ({ id, jump_url, jump_type }) => {
  if (jump_type === 1) {
    navigateTo(jump_url, jump_type);
    await uActions({ actionType: 22, eventId: id });
  } else {
    router.push({ path: '/webview_activity', query: { activity_id: id } })
  }
};
</script>
