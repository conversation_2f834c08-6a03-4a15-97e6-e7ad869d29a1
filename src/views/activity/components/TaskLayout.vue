<template>
  <div class="px-4 h-full overflow-scroll scroll-container">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="min-h-full">
      <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
        <div v-for="item in list" :key="item.id" class="flex items-center my-4" @click="navigateHandle(item)">
          <div class="w-[42px] h-[42px] rounded overflow-hidden">
            <img v-if="item.isNewerTask" src="@/assets/icons/task.png" alt="">
            <ImgComponents v-else :imgUrl="item.pic"></ImgComponents>
          </div>
          <div class="ml-4 flex-1">
            <div class="flex items-center">
              <span class="text-base">{{ item.title }}</span>
              <span v-if="item.tag" :class="[
                'text-[0.7rem] h-[18px] leading-4 box-border italic  ml-1 px-0.5 rounded-t-md	rounded-br-md rounded-bl-sm',
                {
                  'bg-[#ff4817]': item.taskTypeId === 0,
                  'bg-[#ff9c00]': item.taskTypeId !== 0,
                }
              ]">{{ item.tag }}</span>
            </div>
            <div class="text-xs text-[#fff1bb] van-ellipsis">{{ item.subTitle }}</div>
          </div>
          <div class="ml-auto text-xs text-[var(--van-primary-color)]">
            <span>详情</span>
            <van-icon name="arrow" size="10" />
          </div>
        </div>
      </van-list>
      <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />
    </van-pull-refresh>
  </div>
</template>

<script setup>
import { useListExtra } from '@/hooks';
import { listTask } from '@/api/activity';
import EmptyPage from '@/components/empty/empty';

const router = useRouter();

const { list, refreshing, loading, error, finished, onRefresh, onLoad } = useListExtra({
  serverHandle: listTask,
});

const navigateHandle = ({ taskTypeId, title, isNewerTask, linkUrl }) => {
  if (isNewerTask) {
    router.push({ path: '/newbietask', query: { title, taskTypeId, isNewerTask } })
  } else {
    router.push({ path: '/task_detail', query: { title, taskTypeId, isNewerTask } })
  }
};
</script>
