<template>
  <SecondPageLayout>
    <div class="flex flex-col h-full overflow-hidden scroll-container">
      <div style="--van-tabs-line-height: 52px">
        <van-tabs
          v-if="tabs && tabs.length"
          v-model:active="tabIndex"
          :ellipsis="false"
          line-width="25"
          line-height="2"
        >
          <van-tab v-for="tab in tabs" :key="tab.id" :title="tab.name"></van-tab>
        </van-tabs>
      </div>

      <div class="flex flex-col overflow-hidden h-full box-border">
        <div class="w-full h-full overflow-hidden max-h-full inset-0" style="--webkit-overflow-scrolling: touch">
          <KeepAlive>
            <SwiperComponents v-model="tabIndex" :list="tabs" :callBackHandle="callBackHandle" />
          </KeepAlive>
        </div>
      </div>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { listActivityType } from '@/api/activity';
import SwiperComponents from './components/swiper.component.vue';

defineOptions({
  name: 'Activity'
})

const { query: { activity_id, category = '2' } } = useRoute();
const tabKey = 0;
const tabs = ref([]);

const tabIndex = ref(tabKey);


const callBackHandle = (index) => {
  tabIndex.value = index;
};

const onRefresh = () => {
  try {
    listActivityType().then((res) => {

      const list = res.data || [];

      list.splice(1, 0, { name: '自助领取', id: -1, type: 1 });

      let tabListArr = list.map((val, index) => {
        if (activity_id && val.id.toString() === activity_id) {
          tabIndex.value = index;
        }

        if (category && val.category && val.category.toString() === category) {
          tabIndex.value = index;
        }

        return val;
      })

      tabs.value = tabListArr
    })
  } catch (error) {
    console.log(error, 'error');
  }
};

onRefresh();
</script>
