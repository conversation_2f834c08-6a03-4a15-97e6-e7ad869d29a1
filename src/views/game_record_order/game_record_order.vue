<template>
  <SecondPageLayout>
    <div class="flex flex-col py-2.5 px-4">
      <van-pullRefresh
        class="min-h-screen"
        v-model="state.refreshing"
        @refresh="onRefresh"
      >
        <div
          class="flex flex-col mb-3 bg-#ffd631 rounded-2xl border border-white border-solid overflow-hidden"
        >
          <div
            class="flex items-center text-[var(--van-gray-8)] bg-[var(--van-primary-color)] h-10 px-3"
          >
            <div class="text-md">{{ +type === 1 ? "今日" : "昨日" }}</div>
          </div>
          <div
            class="flex items-center justify-center bg-[var(--van-gray-8)] h-16 text-white text-xs"
          >
            <div class="flex flex-col items-center justify-center flex-1">
              <p class="mb-2">下单总金额</p>
              <p class="font-bold text-[var(--van-primary-color)]">
                ￥{{ state.point }}
              </p>
            </div>
            <div class="flex flex-col items-center justify-center flex-1">
              <p class="mb-2">有效下注</p>
              <p class="font-bold text-[var(--van-red)]">
                ￥{{ state.valid_bet }}
              </p>
            </div>
            <div class="flex flex-col items-center justify-center flex-1">
              <p class="mb-2">中奖总金额</p>
              <p class="font-bold text-[var(--van-primary-color)]">
                ￥{{ state.re_point }}
              </p>
            </div>
          </div>
        </div>

        <van-list
          v-model:loading="state.loading"
          v-model:error="state.error"
          :finished="state.finished"
          @load="onLoad"
        >
          <div
            v-if="state.isFetching && isEmpty(state.list)"
            class="flex items-center justify-center pt-20"
          >
            <van-empty
              image="data:image/svg+xml;base64,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"
            >
              <template #description>暂无记录</template>
            </van-empty>
          </div>

          <div
            v-for="item in state.list"
            :key="item.order_no"
            class="flex flex-col border-b border-[#7f7f7f] border-solid"
            @click.stop="navigateHandle(item)"
          >
            <div class="text-lg text-[var(--van-primary-color)] mt-4">
              {{ item.game_name }}
            </div>
            <div class="flex items-center h-16 text-white text-xs">
              <div class="flex flex-col justify-center basis-1/3">
                <p class="mb-2">下单金额</p>
                <p class="font-bold text-[var(--van-red)]">
                  ￥{{ item.point }}
                </p>
              </div>
              <div class="flex flex-col justify-center basis-1/3">
                <p class="mb-2">中奖金额</p>
                <p class="font-bold text-[var(--van-primary-color)]">
                  ￥{{ item.re_point }}
                </p>
              </div>
            </div>
          </div>
        </van-list>
      </van-pullRefresh>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { GetGameRecordDetailAPI } from "@/api/game";
import { isEmpty } from "@/utils";

const router = useRouter();
const {
  query: { type, platform_id },
} = useRoute();

const pagination = reactive({
  page: 1,
  limit: 10,
});

const navigateHandle = (row) => {
  router.push({ path: '/game_record_detail', query: { ...row } })
};

const state = reactive({
  list: [],
  total: 0,
  re_point: "0.00",
  point: "0.00",
  valid_bet: "0.00",
  isFetching: false,
  refreshing: false,
  loading: false,
  error: false,
  finished: false,
});

const onRefresh = () => {
  state.finished = false;
  state.loading = true;
  pagination.page = 1;
  onLoad();
};

const onLoad = async () => {
  state.isFetching = true;
  state.refreshing = false;

  const res = await GetGameRecordDetailAPI({
    ...pagination,
    type: +type,
    platform_id: +platform_id,
  });

  console.log("GetGameRecordDetailAPI", res);

  if (res) {
    state.total = res.data.totleCount;
    state.re_point = res.data.re_point;
    state.point = res.data.point;
    state.valid_bet = res.data.valid_bet;

    let list = res.data.lists || [];

    if (pagination.page === 1) {
      state.list = list;
    } else {
      state.list = state.list.concat(list);
    }

    state.loading = false;

    if (list.length < pagination.limit) {
      state.finished = true;
    } else if (list.length === pagination.limit) {
      state.finished = false;
      pagination.page += 1;
    }
  } else {
    state.refreshing = false;
    state.finished = true;
    state.loading = false;
    state.error = true;
  }
};
</script>

<style lang="less">
.game_record_tabs {
  .van-tabs__wrap {
    height: 52px;
  }

  .van-tabs__nav {
    background-color: var(--van-black-500);
  }

  .van-tab--active {
    font-size: 20px;
  }

  .van-tabs__line {
    bottom: 22px;
  }
}
</style>
