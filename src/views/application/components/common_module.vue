<template>

<h3 class="text-base text-center my-4 text-[var(--van-primary-color)]">优质APP推荐</h3>

  <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
    <ul>
      <li v-for="item in list" :key="item.id" class="flex items-center py-2.5 px-3">
        <div class="w-[54px] h-[54px] overflow-hidden rounded">
          <ImgComponents :imgUrl="item.pic" />
        </div>
        <div class="flex-1 ml-3">
          <p class="text-sm">{{ item.name }}</p>
          <span class="text-xs text-[#ccc]">{{ item.remark }}</span>
        </div>
        <div
          role="button"
          class="w-[58px] h-6 flex items-center justify-center text-xs border border-solid border-[var(--van-primary-color)] rounded-xl text-[var(--van-primary-color)]"
          @click.stop="navigateHandle(item)"
        >
          <span>下载</span>
        </div>
      </li>
    </ul>
  </van-list>


</template>


<script setup>
import { useListExtra } from '@/hooks';
import { uActions } from '@/api/user';
import { NavigationRecommendAPI } from '@/api/home';

const router = useRouter();

const navigateHandle = async ({ id, url }) => {
  window.dsBridge.call('app.openExternalApp', url);
  await uActions({ actionType: 21, eventId: id });
};


const { list, isFetching, loading, error, finished, onRefresh, onLoad } = useListExtra({
  serverHandle: NavigationRecommendAPI,
  pagination: { data: 'data.list' },
  size: 10
});

</script>