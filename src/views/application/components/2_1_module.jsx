import { uActions } from '@/api/user';
export default defineComponent({
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const navigateHandle = async ({ url, id }) => {
      window.dsBridge.call('app.openExternalApp', url);
      await uActions({ actionType: 21, eventId: id });
    };

    return () => (
      <div className="grid grid-cols-2 px-3 pb-3 gap-2">
        {
          props.list.map(val => (
            <div className="overflow-hidden rounded" onClick={() => navigateHandle(val)}>
              <ImgComponents imgUrl={val.pic} />
            </div>
          ))
        }
      </div>
    );
  },
});
