import { uActions } from '@/api/user';
export default defineComponent({
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const navigateHandle = async ({ url, id }) => {
      window.dsBridge.call('app.openExternalApp', url);
      await uActions({ actionType: 21, eventId: id });
    };

    return () => (
      <div className="grid grid-cols-1 gap-y-3 px-3 pb-3">
        {
          props.list.map(val => (
            <div className="w-full h-auto overflow-hidden rounded" onClick={() => navigateHandle(val)}>
              <ImgComponents imgUrl={val.pic} />
            </div>
          ))
        }
      </div>
    );
  },
});
