import { uActions } from '@/api/user';
import common_module from './common_module.vue';
export default defineComponent({
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const navigateHandle = async ({ url, id }) => {
      window.dsBridge.call('app.openExternalApp', url);
      await uActions({ actionType: 21, eventId: id });
    };

    return () => (
      <>
        <div className="grid grid-cols-5 gap-y-5">
          {
            props.list.map(val => (
              <div className="flex flex-col items-center justify-center">
                <div className="w-[54px] h-[54px] overflow-hidden rounded">
                  <ImgComponents imgUrl={val.pic} />
                </div>
                <div className="line-clamp-1 leading-7 text-xs text-center text-[#cac4ac]">{ val.name }</div>
                <div
                  role="button"
                  className="w-[58px] h-6 flex items-center justify-center text-xs border border-solid border-[var(--van-primary-color)] rounded-xl text-[var(--van-primary-color)]"
                  onClick={() => navigateHandle(val)}
                >
                  <span>下载</span>
                </div>
              </div>
            ))
          }
        </div>
        
        <common_module></common_module>

      </>
    );
  },
});
