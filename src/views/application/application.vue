<template>
  <PageLayout>
    <div class="flex-1 overflow-auto" ref="container">
      <van-pullRefresh v-model="refreshing" @refresh="getCenter">
        <div class="flex-none px-3 py-2 h-30" v-if="advList && advList.length">
          <Banner :options="advList" />
        </div>
        <van-tabs v-model:active="tabIndex" :lazy-render="false" :ellipsis="false" :line-height="0">
          <van-tab v-for="(tab, i) in tabList" :key="tab.name" :title="tab.name" :name="i">
            <FiveOneModule v-if="tab.category === 0" :list="tab.list"></FiveOneModule>
            <SingleModule v-else-if="tab.category === 2" :list="tab.list"></SingleModule>
            <TwoOneModule v-else :list="tab.list"></TwoOneModule>
          </van-tab>
        </van-tabs>
      </van-pullRefresh>
    </div>
  </PageLayout>
</template>
<script setup>
import { useAppStore } from '@/store/app';
import { NavigationCenterAPI } from '@/api/home';
import FiveOneModule from './components/5_1_module';
import TwoOneModule from './components/2_1_module';
import SingleModule from './components/single_module';
import Banner from '@/components/banner/banner.vue';

defineOptions({
  name: 'Home'
});
const appStore = useAppStore();
const advList = computed(() => appStore.adItem(19));

const tabList = ref([]);
const container = ref(null)
const top = ref(0);
const tabIndex = ref(0);
const refreshing = ref(false);

const getCenter = () => {
  try {
    NavigationCenterAPI().then((res) => {
      tabList.value = res.data.download_type.filter((o) => o.list.length);
    });
  } catch (error) {
    console.log(error, 'error');
  } finally {
    refreshing.value = false;
  }
};

onBeforeRouteLeave((to, from) => {
  top.value = container.value.scrollTop
});

onMounted(() => {
  getCenter();
});

onActivated(() => {
  container.value.scrollTop = top.value
});
</script>
