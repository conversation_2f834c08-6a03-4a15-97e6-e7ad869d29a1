<template>
  <SecondPageLayout>
    <div class="flex items-center w-[345px] mx-auto bg-[var(--van-black-500)] rounded-md box-border mt-5">
      <ul class="w-full text-sm">
        <li class="flex items-center justify-between p-3">
          <span>用户ID</span>
          <span>{{ id }}</span>
        </li>
        <li class="flex items-center justify-between p-3" @click="navigatorHandle('/edit_nickname')">
          <span>用户昵称</span>
          <div class="flex-1 text-right mr-2">{{ nickName }}</div>
          <van-icon name="arrow" />
        </li>
      </ul>
    </div>

    <div class="flex items-center w-[345px] mx-auto bg-[var(--van-black-500)] rounded-md box-border mt-5">
      <ul class="w-full text-sm">
        <li class="flex items-center p-3" @click="avatarHandle">
          <span class="flex-1">头像</span>
          <div class="flex-1 mr-2 text-right">
            <van-image round width="3.25rem" height="3.25rem" :src="`/static/webp/avatar${picUrl}.webp`" />
          </div>
          <van-icon name="arrow" />
        </li>
        <li class="flex items-center p-3" @click="birthDayHandle">
          <div class="flex-1">
            <p>生日</p>
            <span class="text-xs text-[var(--van-gray-5)]">完善生日，获取生日福利</span>
          </div>
          <div class="flex-1 text-right mr-2">{{ birthDay }}</div>
          <van-icon name="arrow" />
        </li>
        <li class="flex items-center justify-between p-3">
          <span>注册日期</span>
          <span>{{ createTimeStr }}</span>
        </li>
      </ul>
    </div>
  </SecondPageLayout>

  <AvatarPopup v-model="avatarVisible"></AvatarPopup>

  <van-popup v-model:show="pickerVisible" safe-area-inset-bottom position="bottom">
    <van-date-picker
      title="选择日期"
      v-model="pickerValue"
      :min-date="minDate"
      :max-date="maxDate"
      @confirm="onConfirm"
      @cancel="pickerVisible = false"
    />
  </van-popup>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { formatter } from '@/utils';
import { useUserStore } from '@/store/user';
import { UpdateInfoDataAPI } from '@/api/user';
import AvatarPopup from '@/components/AvatarPopup/index.vue';

const userStore = useUserStore();

const router = useRouter();

const { nickName, id, picUrl, birthDay, createTimeStr } = storeToRefs(userStore);

const avatarVisible = ref(false);

const pickerVisible = ref(false);
const pickerValue = ref([new Date().getFullYear(), new Date().getMonth() + 1, new Date().getDate()]);
const currentYear = new Date().getFullYear();
const minDate = new Date(currentYear - 100, 0, 1);
const maxDate = new Date();

const avatarHandle = () => {
  avatarVisible.value = true;
}

const birthDayHandle = () => {
  if (birthDay.value) {
    showToast('生日不能修改');
    return;
  }

  pickerVisible.value = true;
};

const onConfirm = ({ selectedValues }) => {
  const date = pickerValue.value.join('-');

  UpdateInfoDataAPI({
    infoType: 'BirthDay',
    val: date
  }).then(() => {
    showToast('设置成功');
    userStore.updatePersonData();
    pickerVisible.value = false;
  });
};

const navigatorHandle = (path) => {
  router.push({ path });
};
</script>
