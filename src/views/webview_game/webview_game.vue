<script setup>
import { GetGameTokenlinkAPI, GetPGTokenlinkAPI, ReviceWalletAPI } from '@/api/game';

const {
  query: { game_id, platform_code, from }
} = useRoute();

const router = useRouter();

const offset = ref({ x: 83, y: 90 });

const show = ref(false);


const navigatorDialog = () => {
  showConfirmDialog({
    message:'是否退出并去活动中心领奖？',
    confirmButtonText: '领奖',
    cancelButtonText: '退出',
  })
    .then(() => {
      router.push({ path: '/hd', query: { source: 'THIRD' } });
    })
    .catch(() => {
      router.replace(from || '/yx');
    });
}

const navigatorRecharge = () => {
  router.push({ path: '/cz', query: { source: 'THIRD' } });
};

const navigatorActivity = () => {
  router.push({ path: '/hd', query: { source: 'THIRD' } });
};

function onLoad() {
  const toast = showLoadingToast({ duration: 0 });
  const isPG = platform_code == 'PGGame';
  const handle = isPG ? GetPGTokenlinkAPI : GetGameTokenlinkAPI;

  handle({ game_id: Number(game_id) })
    .then(({ data: { url } }) => {
      const iframe = document.getElementById('webviewIframe');
      if (isPG) {
        iframe.src = 'about:blank'; // 可以替换为任何同源页面的URL
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        iframeDoc.open();
        iframeDoc.write(url);
      } else {
        iframe.src = url;
      }
    })
    .finally(() => {
      toast.close();
    });
}

onMounted(() => {
  onLoad();
});
</script>

<template>
  <SecondPageLayout>
    <template #navbar>
      <div class="flex-none" />
    </template>

    <div class="safe-area-wrapper van-safe-area-top relative h-screen w-screen">
      <van-floating-bubble
        v-model:offset="offset"
        axis="xy"
        @click="show = !show"
        style="--van-floating-bubble-size: 35px; overflow: visible;"
      >
        <img src="@/assets/icons/quick.png" style="width: 35px; height: 35px" />

        <div v-if="show" class="absolute top-32px w-106px h-56px">
          <div class="absolute left-0 top-0 w-30px h-30px" @click.stop="navigatorDialog">
            <img src="@/assets/icons/quit.png" />
          </div>
          <div class="absolute left-38px bottom-0 w-30px h-30px" @click.stop="navigatorActivity">
            <img src="@/assets/icons/received.png" />
          </div>
          <div class="absolute right-0 top-0 w-30px h-30px" @click.stop="navigatorRecharge">
            <img src="@/assets/icons/recharges.png" />
          </div>
        </div>

      </van-floating-bubble>

      <div class="h-full w-full flex items-center justify-center">
        <iframe
          id="webviewIframe"
          name="webviewIframe"
          allow="screen-wake-lock; fullscreen; clipboard-write; autoplay"
          frameborder="0"
          style="min-width: 100%; min-height: 100%"
        />
      </div>
    </div>
  </SecondPageLayout>
</template>

<style lang="less" scoped>
.safe-area-wrapper {
  .v-enter-active,
  .v-leave-active {
    transition: opacity 0.5s ease;
  }

  .v-enter-from,
  .v-leave-to {
    opacity: 0;
  }
}
</style>
