<template>
  <SecondPageLayout>
    <van-cell-group inset class="flex-none">
      <van-field
        v-model="parseDate"
        label="时&nbsp;间"
        label-width="4.5em"
        size="large"
        placeholder="请选择时间"
        center
        readonly
        is-link
        @click="showPicker = true"
      />
      <van-field
        v-model="params.team_uid"
        label="会员账号"
        label-width="4.5em"
        name="team_uid"
        size="large"
        placeholder="请输入用户ID"
        center
      >
        <template #button>
          <van-button size="mini" type="primary" class="w-15" @click="onRefresh">查询</van-button>
        </template>
      </van-field>
    </van-cell-group>
    <div class="flex flex-col px-4">
      <div class="flex items-center justify-center h-12 text-xs">
        <div class="flex flex-1 items-center justify-center">
          <p class="mr-1">下注总金额:</p>
          <p class=" text-red-500 font-bold">￥{{ state.money_total }}</p>
        </div>
        <div class="flex flex-1 items-center justify-center">
          <p class="text-xs mr-1">中奖总金额:</p>
          <p class="text-[var(--van-primary-color)] font-bold">￥{{ state.win_money_total }}</p>
        </div>
      </div>

      <div v-for="item in items" :key="item.id" class="flex flex-col mb-3 bg-#ffd631 rounded-2xl border border-white border-solid overflow-hidden">
        <div class="flex items-center text-[var(--van-gray-8)] bg-[var(--van-primary-color)] h-16 px-3">
          <div class="overflow-hidden rounded-md h-[38px] w-[38px]">
            <ImgComponents :imgUrl="item.icon" />
          </div>
          <div class="text-md ml-3 mr-auto">{{ item.platform_name }}</div>
          <div role="button" class="text-xs p-1" @click="navigatorHandle(item)">更多<van-icon name="arrow" /></div>
        </div>
        <div class="flex items-center justify-center bg-[var(--van-gray-8)] h-16 text-white text-xs">
          <div class="flex flex-col items-center justify-center flex-1">
            <p class="mb-2">下单注量</p>
            <p class="font-bold text-[var(--van-primary-color)]">{{ item.order_num }}</p>
          </div>
          <div class="flex flex-col items-center justify-center flex-1">
            <p class="mb-2">下单金额</p>
            <p class="font-bold text-[var(--van-red)]">￥{{ item.money }}</p>
          </div>
          <div class="flex flex-col items-center justify-center flex-1">
            <p class="mb-2">中奖金额</p>
            <p class="font-bold text-[var(--van-primary-color)]">￥{{ item.win_money }}</p>
          </div>
        </div>
      </div>

    </div>

    <van-popup v-model:show="showPicker" position="bottom">
      <van-date-picker
        v-model="params.date"
        title="选择年月日"
        @confirm="onConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>

  </SecondPageLayout>
</template>

<script setup>
import { listGameRecord } from '@/api/promotion';
import { formatter } from '@/utils';

const router = useRouter();

const showPicker = ref(false);

const items = ref([]);

const maxDate = new Date();

const state = reactify({
  money_total: '--',
  win_money_total: '--',
})


const params = reactive({
  team_uid: '',
  date: formatter(new Date(), 'YYYY-MM-DD').split('-')
});

const parseDate = computed(() => {
  return params.date.join('-')
})

const navigatorHandle = ({ platform_id }) => {
  router.push({ path: '/promotion_game_detail', query: { platform_id, date: parseDate.value, team_uid: params.team_uid } })
}


const onRefresh = () => {
  listGameRecord({
    date: parseDate.value,
    team_uid: params.team_uid
  }).then(({ data: { list, money_total, win_money_total } }) => {
    items.value = list || []
    state.money_total = money_total
    state.win_money_total = win_money_total
  })
}

const onConfirm = ({ selectedValues }) => {
  params.date = selectedValues
  showPicker.value = false
  onRefresh()
}


onRefresh()

</script>
