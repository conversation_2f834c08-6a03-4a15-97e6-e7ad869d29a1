<template>
  <SecondPageLayout>
    <div class="p-3 box-border">
      <div class="text-xs mt-4 rounded-lg bg-[var(--van-black-500)]">
        <div class="h-12 flex items-center justify-between mx-2.5 border-b-1 border-solid border-[var(--van-info-color)]">
          <div class="flex items-center">
            <div class="w-[5px] h-[15px] ml-1 mr-2.5 rounded bg-[var(--van-primary-color)]"></div>
            <span>我的下级默认分成</span>
          </div>
        </div>
        <van-field
          v-model="user.game_divide"
          label="游戏佣金比例%"
          label-width="8em"
          name="game_divide"
          size="large"
          type="digit"
          placeholder="必填"
          autocomplete="off"
          center
        >
          <template #right-icon>
            <img src="@/assets/promotion/icon_edit.png" class="w-4 h-4">
          </template>
        </van-field>
      </div>

      <div class="text-xs mt-4 rounded-lg bg-[var(--van-black-500)]">
        <div class="h-12 flex items-center justify-between mx-2.5 border-b-1 border-solid border-[var(--van-info-color)]">
          <div class="flex items-center">
            <div class="w-[5px] h-[15px] ml-1 mr-2.5 rounded bg-[var(--van-primary-color)]"></div>
            <span>我的名片</span>
          </div>
          <div @click="copyHandle"><img src="@/assets/promotion/icon_copy.png" class="h-4" /></div>
        </div>
        <div class="px-3 flex flex-col">
          <div class="flex items-center my-4" @click="$router.push('/promotion_carte')">
            <div class="overflow-hidden rounded-full w-[53px] h-[53px]">
              <img src="https://api.multiavatar.com/as11s5d5.png" class="w-full h-full">
            </div>
            <div class="ml-4 flex flex-col flex-1">
              <p class="text-sm text-[var(--van-primary-color)] min-h-5">{{ user.nickname }}</p>
              <p class="text-xs"><span class="text-[#ccc]">签名</span>&nbsp;&nbsp;&nbsp;&nbsp;{{ user.sign || '未设置' }}</p>
            </div>
            <div class="ml-auto"><van-icon name="arrow" color="var(--van-primary-color)" /></div>
          </div>
          <div class="text-xs grid grid-cols-2">
            <div class="h-10 flex items-center">
              <div class="min-w-14">
                <p class="w-4 h-[15px] text-center rounded-sm text-[var(--van-black-500)] bg-[var(--van-primary-color)]">ID</p>
              </div>
              <p>{{ user.id }}</p>
            </div>
            <div class="h-10 flex items-center">
              <div class="min-w-14">
                <p class="w-10 h-[15px] text-center rounded-sm text-[var(--van-black-500)] bg-[var(--van-primary-color)]">微信</p>
              </div>
              <p>{{ user.wechat || '未设置' }}</p>
            </div>
            <div class="h-10 flex items-center">
              <div class="min-w-14">
                <p class="w-10 h-[15px] text-center rounded-sm text-[var(--van-black-500)] bg-[var(--van-primary-color)]">绑定码</p>
              </div>
              <p>{{ user.code }}</p>
            </div>
            <div class="h-10 flex items-center">
              <div class="min-w-14">
                <p class="w-10 h-[15px] text-center rounded-sm text-[var(--van-black-500)] bg-[var(--van-primary-color)]">电话</p>
              </div>
              <p>{{ user.phone || '未设置' }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="text-xs mt-4 rounded-lg bg-[var(--van-black-500)]">
        <div class="h-12 flex items-center justify-between mx-2.5 border-b-1 border-solid border-[var(--van-info-color)]">
          <div class="flex items-center">
            <div class="w-[5px] h-[15px] ml-1 mr-2.5 rounded bg-[var(--van-primary-color)]"></div>
            <span>我的上级</span>
          </div>
        </div>
        <div class="px-3 flex flex-col" v-if="parent">
          <div class="flex items-center my-4">
            <div class="overflow-hidden rounded-full w-[53px] h-[53px]">
              <img src="https://api.multiavatar.com/9s45asd.png" class="w-full h-full">
            </div>
            <div class="ml-4 flex flex-col">
              <p class="text-sm text-[var(--van-primary-color)] min-h-5">{{ parent.nickname }}</p>
              <p class="text-xs"><span class="text-[#ccc]">签名</span>&nbsp;&nbsp;&nbsp;&nbsp;{{ parent.sign || '未设置' }}</p>
            </div>
          </div>
          <div class="text-xs grid grid-cols-2">
            <div class="h-10 flex items-center">
              <div class="min-w-14">
                <p class="w-4 h-[15px] text-center rounded-sm text-[var(--van-black-500)] bg-[var(--van-primary-color)]">ID</p>
              </div>
              <p>{{ parent.id }}</p>
            </div>
            <div class="h-10 flex items-center">
              <div class="min-w-14">
                <p class="w-10 h-[15px] text-center rounded-sm text-[var(--van-black-500)] bg-[var(--van-primary-color)]">微信</p>
              </div>
              <p>{{ parent.wechat || '未设置' }}</p>
            </div>
            <div class="h-10 flex items-center">
              <div class="min-w-14">
                <p class="w-10 h-[15px] text-center rounded-sm text-[var(--van-black-500)] bg-[var(--van-primary-color)]">绑定码</p>
              </div>
              <p>{{ parent.code }}</p>
            </div>
            <div class="h-10 flex items-center">
              <div class="min-w-14">
                <p class="w-10 h-[15px] text-center rounded-sm text-[var(--van-black-500)] bg-[var(--van-primary-color)]">电话</p>
              </div>
              <p>{{ parent.phone || '未设置' }}</p>
            </div>
          </div>
        </div>
        <div v-else class="h-40"></div>
      </div>

      
    </div>
    <div class="px-6 pb-6 flex items-center justify-between">
      <van-button plain round type="primary" class="w-[150px]" :to="{ path: '/promotion_plan_sub' }">下级佣金方案</van-button>
      <van-button round type="primary" @click="onSubmit" class="w-[150px]">保存分成</van-button
      >
    </div>
  </SecondPageLayout>
  
</template>

<script setup>
import useClipboard from 'vue-clipboard3';
import { listCenter, saveDivide } from '@/api/promotion';

const { toClipboard } = useClipboard();

const user = ref({});
const parent = shallowRef(null);

const onRefresh = () => {
  listCenter().then(({ data: { user_info, parent_user } }) => {
    user.value = user_info;
    parent.value = parent_user
  });
};

const copyHandle= () => {
  const value = `昵称：${user.value.nickname}\nAPPID：${user.value.uid}\n绑定码：${user.value.code}`
  toClipboard(value)
  showToast('复制成功')
}

const onSubmit = () => {
  if (user.value.game_divide > 100) {
    showToast('超过上限值100%')
    return
  }
  saveDivide({
    game_divide: user.value.game_divide,
  }).then(() => {
    showToast('保存成功')
  })
}

onRefresh();
</script>
