<template>
  <SecondPageLayout>
    <div class="mt-4">
      <van-cell-group inset>
        <van-form ref="form" @submit="onSubmit" @failed="onFailed" :show-error-message="false">
          <van-field v-model="user.nickname" label="昵称" label-width="4em" size="large" autocomplete="off" readonly />
          <van-field
            v-model="user.wechat"
            label="微信"
            label-width="4em"
            size="large"
            autocomplete="off"
            placeholder="请输入"
            :rules="[{ required: true, message: '请输入微信' }]"
          >
            <template #right-icon>
              <img src="@/assets/promotion/icon_edit.png" class="w-4 h-4" />
            </template>
          </van-field>
          <van-field
            v-model="user.phone"
            label="手机号"
            label-width="4em"
            size="large"
            autocomplete="off"
            type="tel"
            placeholder="请输入"
            :rules="[{ required: true, message: '请输入手机号' }]"
          >
            <template #right-icon>
              <img src="@/assets/promotion/icon_edit.png" class="w-4 h-4" />
            </template>
          </van-field>
          <van-field
            v-model="user.sign"
            rows="2"
            autosize
            label="留言"
            label-width="4em"
            type="textarea"
            size="large"
            autocomplete="off"
            maxlength="32"
            placeholder="请输入"
            show-word-limit
            :rules="[{ required: true, message: '请输入留言' }]"
          />

          <div class="mt-10">
            <van-button block round size="large" type="primary" native-type="submit"> 确 定 </van-button>
          </div>

        </van-form>
      </van-cell-group>
    </div>

    
  </SecondPageLayout>
</template>

<script setup>
import { listCenter, saveInfo } from '@/api/promotion';

const user = ref({});

const onFailed = (errorInfo) => {
  showToast(errorInfo.errors[0].message);
};

listCenter().then(({ data: { user_info } }) => {
  user.value = user_info;
});

const onSubmit = () => {
  saveInfo({
    wechat: user.value.wechat,
    phone: user.value.phone,
    sign: user.value.sign
  }).then(() => {
    showSuccessToast('保存成功')
  })
};
</script>
