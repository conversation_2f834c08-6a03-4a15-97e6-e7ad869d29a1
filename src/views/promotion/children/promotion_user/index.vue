<template>
  <SecondPageLayout>
    <van-cell-group inset>
      <van-field
        v-model="listParams.team_uid"
        label="下级代理"
        label-width="19vw"
        placeholder="请输入下级ID"
        center
        autocomplete="off"
        name="team_uid"
      >
        <template #button>
          <van-button size="mini" type="primary" @click="onRefresh" class="w-15">查询</van-button>
        </template>
      </van-field>
    </van-cell-group>
    <div class="px-4 mt-5">
      <van-pullRefresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model:loading="loading" v-model:error="error" :finished="finished" :finished-text="finishedText" @load="onLoad">
          <div class="flex items-center justify-center text-xs h-10 rounded-lg bg-[var(--van-black-500)]">
            <div class="flex-1 text-center">用户</div>
            <div class="flex-1 text-center">贡献</div>
            <div class="flex-1 text-center">游戏</div>
            <div class="flex-1 text-center">操作</div>
          </div>

          <div class="flex items-center text-xs py-3" v-for="item in list" :key="item.id">
            <div class="flex-1 text-center van-ellipsis">{{ item.nickname }}</div>
            <div class="flex-1 text-center">{{ item.rebate }}</div>
            <div class="flex-1 text-center">{{ item.game_divide }}%</div>
            <div class="flex-1 text-center">
              <van-icon name="edit" @click.stop="showView(item)" />
            </div>
          </div>
        </van-list>
      </van-pullRefresh>
    </div>
  </SecondPageLayout>

  <TeamUserModel ref="model"></TeamUserModel>

</template>

<script setup>
import { useListExtra } from '@/hooks';
import { listTeamUser, saveTeamUser } from '@/api/promotion';

import TeamUserModel from '@/views/promotion/components/team_user_module.vue';

const model = ref()

const listParams = reactive({
  team_uid: ''
})

const { list, refreshing, loading, error, finished, finishedText, onRefresh, onLoad } = useListExtra({
  serverHandle: listTeamUser,
  immediateParams: listParams,
});

const showView = (uid) => {
  model.value.onShow(uid)
}

</script>
