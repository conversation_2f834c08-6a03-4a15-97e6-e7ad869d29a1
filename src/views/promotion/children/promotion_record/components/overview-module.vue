<template>
  <div class="text-xs mt-4 rounded-lg bg-[var(--van-black-500)]">
    <div class="h-12 flex items-center justify-between mx-2.5 border-b-1 border-solid border-[var(--van-info-color)]">
      <div class="flex items-center">
        <div class="w-[5px] h-[15px] ml-1 mr-2.5 rounded bg-[var(--van-primary-color)]"></div>
        <span>佣金概览</span>
      </div>
      <div @click="show = true"><img src="@/assets/promotion/icon_search.png" class="h-6" /></div>
    </div>
    <div ref="chartRef" class="h-[150px]"></div>
  </div>

  <van-popup v-model:show="show" position="top" safe-area-inset-top teleport="body">
    <div class="pt-4 bg-[var(--van-black-500)]">
      <datePickerModule v-model="dateRange" :format="format">
        <template #range-buttons>
          <div class="mx-4 flex items-center justify-between">
            <van-button
              v-for="option in dateOptions.slice(1)"
              :key="option.value"
              size="mini"
              block
              class="flex-1"
              :type="option.value === tp ? 'primary' : 'info'"
              @click="optionHandle(option)"
              >{{ option.label }}</van-button
            >
          </div>
        </template>
      </datePickerModule>
      <div class="p-4">
        <van-button block size="mini" type="primary" @click="onSubmit"> 查 询 </van-button>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { listRebateReport } from '@/api/promotion';
import datePickerModule from './date-picker-module.vue';
import { dateOptions } from '../utils/util';
import echarts from '../utils/echart';
import { formatter } from '@/utils';

const props = defineProps({
  format: String
})

const chartRef = ref(null);

let chartInstance = null;

const show = ref(false);

const tp = ref(1);

const dateRange = ref([dateOptions[tp.value].startdate, dateOptions[tp.value].enddate]);

const initChart = (data) => {
  chartInstance = echarts.init(chartRef.value)
  const options = {
    animation: false,
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category'
    },
    yAxis: {
      type: 'value',
      position: 'right'
    },
    dataZoom: [
      {
        type: "inside", // 支持鼠标滚轮
        xAxisIndex: 0,
        start: 0,
        end: 30,
        zoomOnMouseWheel: false
      },
    ],
    series: [
      {
        type: 'bar',
        barWidth: '30%',
        itemStyle: {
          color: 'red'
        },
        label: {
          show: true,
          position: 'top',
          color: 'white',
          fontSize: 12
        }
      }
    ]
  }
  chartInstance.setOption(options)
}

const onReport = () => {
  const [startdate, enddate] = dateRange.value;
  listRebateReport({
    startdate: formatter(startdate, props.format),
    enddate: formatter(enddate, props.format),
  }).then(({ data }) => {
    if (data.length) {
      chartInstance.setOption({
        dataset: {
          dimensions: ['date', 'rebate'],
          source: data
        }
      })
    }
  });
};

const onSubmit = () => {
  show.value = false;
  onReport()
}

const optionHandle = ({ startdate, enddate, value }) => {
  tp.value = value
  dateRange.value = [startdate, enddate]
}

onMounted(() => {
  initChart();
})


onReport()
</script>