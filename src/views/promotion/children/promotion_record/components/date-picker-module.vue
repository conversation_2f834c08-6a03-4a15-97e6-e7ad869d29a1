<template>
  <div class="van-cell van-cell--borderless van-cell--center van-field">
    <div class="flex items-center text-xs">
      <label for="">时间</label>
      <div class="flex flex-1 items-center justify-end">
        <input
          :id="id && id[0]"
          autocomplete="off"
          :name="name && name[0]"
          class="border-0 bg-transparent text-white text-center w-2/5 h-full outline-transparent"
          :value="displayValue && displayValue[0]"
          readonly
          @focus="(e) => handleFocusInput(e, 0)"
          @input="handleStartInput"
        />

        <span class="inline-block h-full px-2.5 text-center">至</span>

        <input
          :id="id && id[1]"
          autocomplete="off"
          :name="name && name[1]"
          class="border-0 bg-transparent text-white text-center w-2/5 h-full outline-transparent"
          :value="displayValue && displayValue[1]"
          readonly
          @focus="(e) => handleFocusInput(e, 1)"
          @input="handleEndInput"
        />
      </div>
    </div>
  </div>

  <slot name="range-buttons" />

  <van-popup v-model:show="pickerVisible" safe-area-inset-bottom position="bottom">
    <van-date-picker
      :title="pickerTitle"
      :model-value="pickerValue"
      :max-date="maxDate"
      @confirm="onConfirm"
      @cancel="pickerVisible = false"
    />
  </van-popup>

</template>

<script setup>
import { parseDate, formatter, pickerDate, isArray } from '@/utils';

const props = defineProps({
  id: {
    type: [Array, String]
  },
  name: {
    type: [Array, String],
    default: ''
  },
  format: String,
  valueFormat: {
    type: String,
    default: 'YYYY-MM-DD'
  },
  editable: {
    type: Boolean,
    default: true
  },
  readonly: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: [Date, Array, String, Number],
    default: ''
  },
  rangeSeparator: {
    type: String,
    default: '-'
  },
  startPlaceholder: {
    type: String,
    default: '开始日期'
  },
  endPlaceholder: {
    type: String,
    default: '结束日期'
  },
  defaultValue: {
    type: [Date, Array]
  },
  defaultTime: {
    type: [Date, Array]
  },
  disabledDate: {
    type: Function
  },
  cellClassName: {
    type: Function
  },
  tabindex: {
    type: [String, Number],
    default: 0
  },
  initialPicker: {
    type: Number,
    default: 0
  }
});

const emits = defineEmits(['update:modelValue', 'change']);

const maxDate = new Date()

const state = reactive({
  role: 0
})

const pickerVisible = ref(false);

const formatDayjsToString = (days) => {
  if (!days) return null;
  if (isArray(days)) {
    return days.map((d) => d.format(props.format));
  }
  return days.format(props.format);
};

const pickerTitle = computed(() => {
  return state.role === 0 ? props.startPlaceholder : props.endPlaceholder
})

const parsedValue = computed(() => {
  let dayOrDays;
  if (isArray(props.modelValue)) {
    dayOrDays = props.modelValue.map((d) => parseDate(d, props.valueFormat));
  }
  if (isArray(dayOrDays) && dayOrDays.some((day) => !day)) {
    dayOrDays = [];
  }
  return dayOrDays;
});

const displayValue = computed(() => {
  const formattedValue = formatDayjsToString(parsedValue.value);
  return [(formattedValue && formattedValue[0]) || '', (formattedValue && formattedValue[1]) || ''];
});


const handleFocusInput = (e, role) => {
  if (
    props.readonly ||
    pickerVisible.value
  ) {
    return
  }
  state.role = role
  pickerVisible.value = true
}

const onConfirm = ({ selectedValues }) => {
  const value = formatter(selectedValues, props.format)
  let newValue
  const parsedVal = unref(parsedValue)
  if (state.role === 0) {
    newValue = [value, parsedVal && parsedVal[1]]
  }

  if (state.role === 1) {
    newValue = [parsedVal && parsedVal[0], value]
  }

  emits('update:modelValue', newValue)
  emits('change', newValue)

  pickerVisible.value = false
}

</script>
