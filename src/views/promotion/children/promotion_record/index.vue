<template>
  <SecondPageLayout>
    <div class="p-3 box-border">
      <OverviewModule :format="format"></OverviewModule>

      <div class="text-xs mt-4 rounded-lg bg-[var(--van-black-500)]">
        <div class="h-12 flex items-center justify-between mx-2.5 border-b-1 border-solid border-[var(--van-info-color)]">
          <div class="flex items-center">
            <div class="w-[5px] h-[15px] ml-1 mr-2.5 rounded bg-[var(--van-primary-color)]"></div>
            <span>佣金明细</span>
          </div>
        </div>
        <div class="mx-2.5 mt-4 px-3 py-2 flex items-center box-border overflow-hidden rounded-lg border border-solid border-[var(--van-primary-color)]">
          <div class="mr-3 break-words flex-none">
            <label for="van-field-1-input">会员账号</label>
          </div>
          <div class="flex-1 relative align-middle break-words">
            <div class="flex items-center">
              <input type="text" placeholder="请输入用户ID" v-model="query.team_uid" class="block w-full m-0 bg-transparent border-none">
              <div class="ml-1">
                <van-button size="mini" type="primary" class="w-15" @click="onRefresh">查询</van-button>
              </div>
            </div>
          </div>
        </div>
        <datePickerModule v-model="query.dateRange" :format="format">
          <template #range-buttons>
            <div class="mx-4 flex items-center justify-between">
              <van-button
                v-for="option in dateOptions"
                :key="option.value"
                size="mini"
                block
                class="flex-1"
                :type="option.value === state.tr ? 'primary' : 'info'"
                @click="optionHandle(option)"
                >{{ option.label }}</van-button
              >
            </div>
          </template>
        </datePickerModule>

        <div class="px-4 mt-4">
          <van-list v-model:loading="loading" v-model:error="error" :finished="finished" :finished-text="finishedText" @load="onLoad">
            <div class="flex items-center h-10 rounded-lg bg-[var(--van-info-color)]">
              <div class="flex-1 text-center">昵称</div>
              <div class="flex-1 text-center">返佣时间</div>
              <div class="flex-1 text-center">游戏返佣</div>
            </div>

            <div class="flex items-center leading-8" v-for="item in list" :key="item.id">
              <div class="flex-1 text-center van-ellipsis">{{ item.nickname }}</div>
              <div class="flex-1 text-center">{{ item.rebate_date }}</div>
              <div class="flex-1 text-center">{{ item.rebate_game }}</div>
            </div>

          </van-list>
        </div>
      </div>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useList } from '@/hooks';
import { listRebateCash } from '@/api/promotion';
import { dateOptions } from './utils/util';
import echarts from './utils/echart';
import OverviewModule from './components/overview-module.vue';
import datePickerModule from './components/date-picker-module.vue';
import { formatter } from '@/utils';

const format = 'YYYY-MM-DD'

const state = reactive({
  tp: 1,
  tr: 0
});

const query = reactive({
  team_uid: '',
  dateRange: [dateOptions[state.tr].startdate, dateOptions[state.tr].enddate]
})

const listParams = computed(() => {
  const [startdate, enddate] = query.dateRange
  return {
    team_uid: query.team_uid,
    startdate: formatter(startdate, format),
    enddate: formatter(enddate, format)
  }
})

const { list, refreshing, loading, error, finished, finishedText, onRefresh, onLoad } = useList({
  serverHandle: listRebateCash,
  immediateParams: listParams,
  pagination: { page: 'page', limit: 'limit', list: 'data' }
});



const optionHandle = ({ startdate, enddate, value }) => {
  state.tr = value
  query.dateRange = [startdate, enddate]
  onRefresh()
}
</script>
