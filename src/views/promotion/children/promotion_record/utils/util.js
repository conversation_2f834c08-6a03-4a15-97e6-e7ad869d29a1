import dayjs from 'dayjs'
import minMax from 'dayjs/plugin/minMax.js'

import 'dayjs/locale/zh-cn'

dayjs.extend(minMax)
dayjs.locale('zh-cn')

export const dateOptions = [
  { label: '昨日', value: 0, startdate: dayjs().subtract(1, 'days'), enddate: dayjs().subtract(1, 'days') },
  { label: '本周', value: 1, startdate: dayjs().startOf('week'), enddate: dayjs.min([dayjs().endOf('day'), dayjs().endOf('week')]) },
  { label: '上周', value: 2, startdate: dayjs().subtract(1, 'week').startOf('week'), enddate: dayjs().subtract(1, 'week').endOf('week') },
  { label: '本月', value: 3, startdate: dayjs().startOf('month'), enddate: dayjs.min([dayjs().endOf('day'), dayjs().endOf('month')]) },
  { label: '上月', value: 4, startdate: dayjs().subtract(1, 'month').startOf('month'), enddate: dayjs().subtract(1, 'month').endOf('month') }
]