<template>
  <SecondPageLayout>
    <div class="flex flex-col px-4">
      <div class="flex items-center justify-center h-12 text-xs">
        <div class="flex flex-1 items-center justify-center">
          <p class="mr-1">下注总金额:</p>
          <p class=" text-red-500 font-bold">￥{{ state.money }}</p>
        </div>
        <div class="flex flex-1 items-center justify-center">
          <p class="text-xs mr-1">中奖总金额:</p>
          <p class="text-[var(--van-primary-color)] font-bold">￥{{ state.win_money }}</p>
        </div>
      </div>

      <van-list v-model:loading="loading" v-model:error="error" :finished="finished" :finished-text="finishedText" @load="onLoad">
        <div v-for="item in list" :key="item.id" class="flex flex-col mb-3 bg-#ffd631 rounded-2xl border border-white border-solid overflow-hidden">
          <div class="flex items-center text-[var(--van-gray-8)] bg-[var(--van-primary-color)] h-16 px-3">
            <div class="text-md ml-3 mr-auto">{{ item.game_name }}</div>
          </div>
          <div class="flex items-center justify-center bg-[var(--van-gray-8)] h-16 text-white text-xs">
            <div class="flex flex-col items-center justify-center flex-1">
              <p class="mb-2">下注金额</p>
              <p class="font-bold text-[var(--van-red)]">￥{{ item.money }}</p>
            </div>
            <div class="flex flex-col items-center justify-center flex-1">
              <p class="mb-2">中奖金额</p>
              <p class="font-bold text-[var(--van-primary-color)]">￥{{ item.win_money }}</p>
            </div>
          </div>
        </div>
      </van-list>

      

    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useListExtra } from "@/hooks";
import { listGameDetail } from '@/api/promotion';

const { query: { platform_id, date, team_uid } } = useRoute()

const items = ref([])

const state = reactify({
  money: '--',
  win_money: '--',
})

const parseDate = computed(() => {
  return params.date.join('-')
})

const implementationGetParams = () => ({
  platform_id,
  date,
  team_uid
})

const implementationFetched = ({ money, win_money }) => {
  state.money = money
  state.win_money = win_money
}

const {
  list,
  refreshing,
  loading,
  error,
  finished,
  finishedText,
  onRefresh,
  onLoad,
} = useListExtra({
  serverHandle: listGameDetail,
  implementationGetParams,
  implementationFetched,
  pagination: { data: 'data.list' }
});
</script>
