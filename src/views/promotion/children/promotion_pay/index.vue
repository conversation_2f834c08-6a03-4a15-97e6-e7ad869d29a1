<template>
  <SecondPageLayout>
    <Swiper
      :slidesPerView="'auto'"
      :autoHeight="false"
      :grabCursor="true"
      :modules="[Pagination, EffectCoverflow]"
      :pagination="true"
      :centeredSlides="true"
      :effect="'coverflow'"
      :coverflowEffect="{
        rotate: 50,
        stretch: 0,
        depth: 100,
        modifier: 1,
        slideShadows: true
      }"
      @slideChange="onSlideChange"
      class="w-full"
    >
      <SwiperSlide v-for="item in swipers" :key="item.id" class="!w-[300px]">
        <div class="relative h-full rounded-md overflow-hidden">
          <ImgComponents :imgUrl="item.thumb" />
          <div class="absolute left-1/2 bottom-20 -translate-x-2/4">
            <img :src="item.qrcode" alt="QR Code" />
          </div>
        </div>
      </SwiperSlide>
    </Swiper>
    <div class="p-4">
      <div class="flex items-center justify-between px-3 mt-6 text-xs h-12 rounded-lg bg-[var(--van-black-500)]">
        <span>下级游戏反佣为70%</span>
        <van-button size="mini" type="primary" plain class="w-18" @click="navigateRelation">调整比例</van-button>
      </div>

      <p class="text-red-500 text-xs text-center mt-3 mb-8">当比例为0时下级将没有任何收益</p>

      <div class="flex items-center justify-around">
        <van-button round plain type="primary" class="w-[150px]" @click="copyHandle">复制链接</van-button>
        <van-button round type="primary" class="w-[150px]">截图保存二维码</van-button>
      </div>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { listSource } from '@/api/promotion';
import useClipboard from 'vue-clipboard3';
import { useQRCode } from '@vueuse/integrations/useQRCode';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { EffectCoverflow, Pagination } from 'swiper/modules';
// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/effect-coverflow';

const router = useRouter();

const { toClipboard } = useClipboard();

const swipers = ref([]);

const swiperIndex = ref(0);

const onSlideChange = (swiper) => {
  swiperIndex.value = swiper.activeIndex;
};

const state = reactify({
  game_divide: '0'
});

const onRefresh = () => {
  listSource().then(({ data: { list, game_divide } }) => {
    state.game_divide = game_divide;
    swipers.value = (list || []).map((o) => ({
      ...o,
      qrcode: useQRCode(o.url, {
        width: 125,
        height: 125,
        margin: 1
      })
    }));
  });
};

const copyHandle = async () => {
  try {
    const tmp = swipers.value[swiperIndex.value];
    // 复制
    await toClipboard(tmp.url);
    showToast('复制成功');
    // 复制成功
  } catch (e) {
    // 复制失败
  }
};

const navigateRelation = () => {
  router.push({ path: '/promotion_relation' });
};

onRefresh();
</script>
