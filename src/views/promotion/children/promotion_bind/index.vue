<template>
  <SecondPageLayout>
    <div class="mt-4">
      <van-cell-group inset>
        <van-form ref="form">
          <van-field
            v-model="params.uid"
            label="下级ID"
            label-width="4em"
            name="uid"
            size="large"
            autocomplete="off"
            placeholder="请输入下级ID"
            :rules="[{ required: true, message: '请输入下级ID' }]"
          />
          <van-field
            v-model="params.code"
            label="绑定码"
            label-width="4em"
            name="code"
            size="large"
            autocomplete="off"
            placeholder="请输入下级绑定码"
            :rules="[{ required: true, message: '请输入绑定码' }]"
          />
        </van-form>
      </van-cell-group>
    </div>

    <div class="mt-10 mx-4">
      <van-button
        block
        round
        size="large"
        type="primary"
        :loading="submitting"
        @click="onSubmit"
      >
        绑 定
      </van-button>

      <div class="text-[#999] text-xs mt-5">
        <p class="text-sm mb-3">提示</p>
        <p>1、代理通过此功能可以找回没成功绑定的下级会员</p>
        <p>2、绑定码位置：代理中心一上下级一获取绑定码</p>
        <p>3、限鄉定注册时间三天内的下级会员</p>
      </div>

    </div>
  </SecondPageLayout>
</template>

<script setup>
import { bindUser } from '@/api/promotion';

const form = ref()
const submitting = ref(false)

const params = reactive({
  uid: '',
  code: ''
});

const onSubmit = () => {
  form.value.validate().then(() => {
    submitting.value = true
    bindUser(params).then(() => {
      form.value.resetValidation()
      showSuccessToast('绑定成功')
    }).finally(() => {
      submitting.value = false
    })
  })
}

</script>
