<template>
  <SecondPageLayout>
    <div class="p-3 box-border">
      <van-cell
        :title="title"
        is-link
        arrow-direction="down"
        value="筛选"
        title-class="van-ellipsis"
        value-class="text-[var(--van-primary-color)]"
        @click="show = true"
      />


      <div v-for="item in items" class="text-xs rounded-lg bg-[var(--van-black-500)]">
        <div class="h-12 flex items-center mt-4">
          <div class="w-[5px] h-[15px] mx-4 rounded bg-[var(--van-primary-color)]"></div>
          <span>{{ item.fullName }}</span>
        </div>
        <div class="px-2.5">
          <div class="flex items-center h-10 rounded-lg bg-[var(--van-info-color)]">
            <span class="flex-1 text-center">方案</span>
            <span class="flex-1 text-center">有效投注额</span>
            <span class="flex-1 text-center">上限</span>
            <span class="flex-1 text-center">返点比例</span>
          </div>
          <div v-for="tp in item.info" :key="tp.id" class="flex items-center leading-8	">
            <span class="flex-1 text-center">{{ tp.name  }}</span>
            <span class="flex-1 text-center">{{ tp.need  }}</span>
            <span class="flex-1 text-center">{{ tp.upper  }}</span>
            <span class="flex-1 text-center">{{ tp.divide  }}</span>
          </div>
        </div>
      </div>
    </div>

    <van-popup v-model:show="show" round position="bottom" safe-area-inset-bottom>
      <div class="flex flex-col bg-[var(--van-black-500)]">
        <div class="text-lg py-3 text-center">筛选(多选)</div>

        <div class="grid grid-cols-3 gap-2 text-sm px-4">
          <div class="platform_item" :class="[params.platform_ids.length === 0 && 'platform_item_active']" @click="selectAll">全部</div>
          <div
            class="platform_item"
            v-for="item in plafroms"
            :key="item.id"
            :class="[params.platform_ids.includes(item.id) && 'platform_item_active']"
            @click="onSelect(item)"
          >
            {{ item.name }}
          </div>
        </div>

        <div class="p-4">
          <van-button block round type="primary" @click="onConfirm">确定</van-button>
        </div>
      </div>
    </van-popup>

  </SecondPageLayout>
</template>

<script setup>
import { listDivideSub, listDividePlatform } from '@/api/promotion';

const show = ref(false)

const items = ref([])

const plafroms = ref([])

const params = reactive({
  uid: '',
  platform_ids: []
});

const title = computed(() => {
  if (params.platform_ids.length === 0) return '全部'
  return plafroms.value.filter(o => params.platform_ids.indexOf(o.id) > -1).map(o => o.name).join('、')
})

const onRefresh = () => {
  listDivideSub({
    platform_ids: params.platform_ids.join(',')
  }).then(({ data }) => {
    items.value = data || []
  })
}

const onPlatform = () => {
  listDividePlatform().then(({ data }) => {
    plafroms.value = data || []
  })
}

const selectAll = () => {
  params.platform_ids = []
}

const onSelect = ({ id }) => {
  const index = params.platform_ids.indexOf(id)
  if (index > -1) {
    params.platform_ids.splice(index, 1)
  } else {
    params.platform_ids.push(id)
  }
}

const onConfirm = () => {
  show.value = false
  onRefresh()
}

onPlatform()
onRefresh()

</script>

<style lang="less">
.platform_item {
  height: 30px;
  border: 1px solid #ffd631;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;

  &_active {
    background-color: var(--van-primary-color);
    color: black;
  }
}
</style>
