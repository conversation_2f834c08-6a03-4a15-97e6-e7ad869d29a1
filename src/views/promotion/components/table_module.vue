<template>
  <div class="rounded-lg h-[176px] overflow-hidden bg-[var(--van-black-500)]">
    <div class="grid grid-cols-4 gap-4 h-full box-border py-4">
      <div class="flex flex-col items-center justify-center" v-for="item in menus" :key="item.key" @click="navigateHandle(item)">
        <img :src="item.icon" class="h-6" />
        <p class="text-xs text-white mt-3">{{ item.title }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import user from '@/assets/promotion/icon_user.png';
import pay from '@/assets/promotion/icon_pay.png';
import record from '@/assets/promotion/icon_record.png';
import game from '@/assets/promotion/icon_game.png';
import bind from '@/assets/promotion/icon_bind.png';
import plan from '@/assets/promotion/icon_plan.png';
import relation from '@/assets/promotion/icon_relation.png';


const router = useRouter();
const navigateHandle = ({ path }) => {
  router.push({ path });
}

const menus = [
  {
    title: '成员管理',
    icon: user,
    key: '00000001',
    path: '/promotion_user'
  },
  {
    title: '我要赚钱',
    icon: pay,
    key: '00000006',
    path: '/promotion_pay'
  },
  {
    title: '佣金报表',
    icon: record,
    key: '00000007',
    path: '/promotion_record'
  },
  {
    title: '游戏记录',
    icon: game,
    key: '00000008',
    path: '/promotion_game_record'
  },
  {
    title: '绑定下级',
    icon: bind,
    key: '00000011',
    path: '/promotion_bind'
  },
  {
    title: '佣金方案',
    icon: plan,
    key: '00000013',
    path: '/promotion_plan'
  },
  {
    title: '上下级',
    icon: relation,
    key: '00000015',
    path: '/promotion_relation'
  }
];
</script>
