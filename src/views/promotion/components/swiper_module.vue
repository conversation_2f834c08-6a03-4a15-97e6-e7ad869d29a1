<template>
  <Swiper
    :slidesPerView="4"
    :slidesPerGroup="4"
    :pagination="{  }"
    :modules="[Pagination]"
  >
    <SwiperSlide v-for="item in swipers" :key="item.id">
      <div class="flex flex-col items-center justify-center text-xs text-white h-24">
        <span class="text-base mb-1.5">{{ data[item.field] || 0 }}</span>
        <p>{{ item.label }}</p>
      </div>
    </SwiperSlide>
  </Swiper>
</template>

<script setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';

const swipers = [
  { label: '我的佣金', color: '#0087fe', field: 'rebate' },
  { label: '团队人数', color: '#7c66f8', field: 'team_user' },
  { label: '团队充值', color: '#0087fe', field: 'team_recharge' },
  { label: '团队贡献', color: '#3aa81d', field: 'team_rebate' },
  { label: '团队投注', color: '#f78906', field: 'team_game' },
  { label: '昨日增加', color: '#626270', field: 'day_user' },
  { label: '月增加', color: '#f78906', field: 'month_user' },
  { label: '月活跃', color: '#ea4335', field: 'month_active' },
  { label: '团队输赢', color: '#ea4335', field: 'team_win' }
];

const props = defineProps({
  data: Object
});
</script>