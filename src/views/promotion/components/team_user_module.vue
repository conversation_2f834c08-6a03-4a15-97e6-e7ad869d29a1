<template>
  <van-popup
    v-model:show="show"
    round
    position="bottom"
    @open="onOpen"
  >
    <div class="flex flex-col px-4 bg-[var(--van-black)] text-sm">
      <div class="flex items-center justify-between py-4">
        <div class="flex items-center">
          <span class="mr-1 text-[var(--van-primary-color)]">昵称</span>
          <span>{{ form.nickname }}</span>
        </div>
        <div class="flex items-center">
          <span class="mr-1 text-[var(--van-primary-color)]">ID</span>
          <span>{{ form.uid }}</span>
          <img src="@/assets/promotion/icon_copy.png" class="h-4 ml-2" @click="copyHandler(form.uid)" />
        </div>
      </div>

      <div class="p-4 bg-[var(--van-info-color)] rounded-lg mb-4">
        <div class="flex items-center justify-between">
          <span>{{ form.date }}</span>
          <TabModule v-model="params.date" @change="getReport" />
        </div>
        <SwiperModule :data="resouce" :is-first="false" />
      </div>

      <van-cell-group>
        <van-field
          v-model="form.game_divide"
          label="游戏佣金比例 %"
          label-width="8em"
          size="large"
          type="digit"
          name="game_divide"
          autocomplete="off"
          placeholder="请输入"
          center
        >
          <template #right-icon>
            <img src="@/assets/promotion/icon_edit.png" class="w-4 h-4" />
          </template>
        </van-field>
      </van-cell-group>

      <div class="flex items-center my-4">
        <van-button size="large" plain type="primary" :to="{ path: '/promotion_plan' }">预览佣金方案</van-button>
        <div class="w-4"></div>
        <van-button size="large" type="primary" @click="onSubmit">保存修改</van-button>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import useClipboard from 'vue-clipboard3'
import TabModule from './tab_module.vue';
import SwiperModule from './swiper_module.vue';
import { listTeamUseInfo, listTeamUserReport, saveTeamUser } from '@/api/promotion'

const emit = defineEmits(['confirm'])

const router = useRouter()

const { toClipboard } = useClipboard()

const onCopy = async (value) => {
  try {
    await toClipboard(value)
    showToast('复制成功')
  } catch (e) {
    console.error(e)
  }
}

const resouce = ref({})

const show = ref(false)

const loading = ref(false)

const params = reactive({
  date: 1,
  team_uid: null
})

const form = ref({})

const getReport = () => {
  listTeamUserReport(params).then(({ data }) => {
    resouce.value = data
  })
}

const onOpen = () => {
  const toast = showLoadingToast({ duration: 0, forbidClick: true });
  listTeamUseInfo({ team_uid: params.team_uid }).then(({ data }) => {
    form.value = data
  }).finally(() => {
    toast.close();
  })
  getReport()
}

const copyHandler = (value) => {
  toClipboard(value);
  showToast('复制成功');
}

const onSubmit = () => {
  if (form.value.game_divide > 100 || form.value.live_divide > 100) {
    showToast('超过上限值100%')
    return
  }

  showLoadingToast()

  saveTeamUser({
    team_uid: params.team_uid,
    game_divide: form.value.game_divide,
    live_divide: form.value.live_divide
  }).then(() => {
    show.value = false
    emit('confirm')
    showSuccessToast('修改成功')
  })
}

const onShow = ({ uid }) => {
  params.team_uid = uid
  show.value = true
}

defineExpose({
  onShow
})

</script>
