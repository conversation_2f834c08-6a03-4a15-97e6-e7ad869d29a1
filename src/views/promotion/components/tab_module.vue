<template>
  <div class="flex items-center rounded-full overflow-hidden border-light-50 text-xs">
    <div
      v-for="tab in tabs"
      :key="tab.value"
      :class="[
        'flex items-center justify-center w-[42.5px] h-[27.5px] ',
        {
          'bg-[var(--van-primary-color)] text-[#2f2f2f] border-[var(--van-primary-color)]': selectValue === tab.value,
          'bg-[#2f2f2f] text-white': selectValue !== tab.value
        }
      ]"
      @click.stop="onClick(tab.value)"
    >
      <span>{{ tab.label }}</span>
    </div>
  </div>
</template>

<script setup>

const tabs = [
  { value: 1, label: '本月' },
  { value: 2, label: '上月' }
];

const props = defineProps({
  modelValue: Number
})

const selectValue = ref(props.modelValue)

const emits = defineEmits(['update:modelValue', 'change'])

const onClick = (val) => {
  if (selectValue.value !== val) {
    selectValue.value = val
    emits('update:modelValue', val)
    emits('change', val)
  }
}

</script>