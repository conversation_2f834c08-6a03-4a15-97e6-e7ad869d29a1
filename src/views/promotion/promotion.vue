<template>
  <SecondPageLayout>
    <div class="w-345px mx-auto h-full">
      <van-pull-refresh v-model="loading" @refresh="onRefresh" class="min-h-full">
        <div class="my-4 h-120px overflow-hidden rounded-md" v-if="advList && advList.length">
          <Banner :options="advList" />
        </div>

        <div class="flex flex-col" v-if="notice">
          <Notice :list="notice"></Notice>
        </div>

        <div
          class="my-4 rounded-lg h-[130px] overflow-hidden bg-[var(--van-black-500)]"
          style="--swiper-pagination-bullet-width: 16px; --swiper-pagination-bullet-height: 3px; --swiper-pagination-bullet-border-radius: 2px;"
        >
          <div class="flex items-center justify-between px-4 h-10">
            <div class="flex items-center text-xs">
              <span>当前等级为</span>
              <span class="text-[var(--van-primary-color)] ml-3">{{ resouce.level }}</span>
            </div>
            <TabModule v-model="params.date" @change="onRefresh"></TabModule>
          </div>

          <SwiperModule :data="resouce"></SwiperModule>
        </div>
        <TableModule></TableModule>
      </van-pull-refresh>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useAppStore } from '@/store/app';
import { listReport } from '@/api/promotion';
import Notice from '@/components/notice/notice.vue';
import Banner from '@/components/banner/banner.vue';
import TabModule from './components/tab_module.vue';
import SwiperModule from './components/swiper_module.vue';
import TableModule from './components/table_module.vue';

const appStore = useAppStore();

const advList = computed(() => appStore.adItem(17));
const notice = computed(() => appStore.adItem(18));
const notice_tmp = computed(() => notice.value?.map((o) => o.content)?.join(' '));

const loading = ref(false);
const resouce = ref({});
const params = reactive({
  date: 1,
  me: 0
});

const onRefresh = () => {
  const toast = showLoadingToast({ duration: 0, forbidClick: true });

  listReport(params)
    .then(({ data }) => {
      resouce.value = data;
    })
    .finally(() => {
      loading.value = false;
      toast.close();
    });
};

onRefresh();
</script>
