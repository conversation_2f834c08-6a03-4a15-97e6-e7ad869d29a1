<template>
  <SecondPageLayout @click-right="onConfirm">
    <template #navbar-right>
      <span class="text-xs text-[#029dff]">保存</span>
    </template>
    <div class="flex items-center w-[345px] h-16 mx-auto bg-[var(--van-black-500)] rounded-md box-border mt-5 px-3">
      <input v-model="nickName" type="text" class="w-full text-white bg-transparent border-0 block">
    </div>
  </SecondPageLayout>

</template>

<script setup>
import { storeToRefs } from 'pinia';
import { formatter } from '@/utils';
import { useUserStore } from "@/store/user";
import { UpdateInfoDataAPI } from '@/api/user';

const userStore = useUserStore();
const { nickName } = storeToRefs(userStore);

const onConfirm = () => {
  UpdateInfoDataAPI({
    infoType: 'NickName',
    val: nickName.value
  }).then(() => {
    showToast('设置成功');
    userStore.updatePersonData();
  })
}

</script>
