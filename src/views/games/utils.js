export const navigateLink = () => {
  const route = useRoute();
  const router = useRouter();

  // type==5 ，表示彩票，进入的是自己的彩票页面
  // type==18，是PG，入厅调用  api/pg/game/token_link
  // type==其他，三方其他厅，入厅调用，api/public/game/token_link

  const navigateTo = ({ type, is_category, id, link, name, platform_code }) => {
    if (typeof type === 'number') {
      if (type === 5) {
        router.push({ path: '/game_lottery', query: { game_id: id } });
      } else {
        router.push({ path: '/webview_game', query: { game_id: id, platform_code } });
      }
    } else {
      if (link) {
        if (is_category === 1) {
          router.push({ path: '/game_category', query: { column_id: id, title: name } });
        } else {
          router.push({ path: '/webview_game', query: { game_id: id, platform_code, from: route.fullPath } });
        }
      } else {
        router.push({ path: '/game_lottery', query: { game_id: id } });
      }
    }
  };
  return { navigateTo };
};
