<template>
  <van-popup
    round
    :show="props.modelValue"
    destroy-on-close
    @closed="onClosed"
    @update:show="onModelValueUpdated"
    @clickOverlay="onSwiper"
    :style="{ width: '80%', background: 'transparent' }"
    :close-on-click-overlay="false"
  >
    <div>
      <van-swipe
        :show-indicators="false"
        :touchable="false"
        :loop="false"
        :duration="0"
        ref="swipeRef"
      >
        <van-swipe-item v-for="option in filterList" :key="option.id">
          <div
            class="w-full h-full rounded-md overflow-hidden"
            @click="navigateHandle(option)"
          >
            <ImgComponents :imgUrl="option.picUrl" />
          </div>
        </van-swipe-item>
      </van-swipe>
      <div
        @click.stop="onSwiper"
        class="absolute bottom-0 right-0 bg-black/[0.5] w-6 h-6 rounded flex items-center justify-center"
      >
        <van-icon name="cross" color="white" />
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { uActions } from "@/api/user";
import { useNavigate } from "@/hooks";

const { navigateTo } = useNavigate();

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: true,
  },
  list: {
    type: Array,
    default: () => [],
  },
});


const emit = defineEmits(["update:modelValue", "closed"]);

const filterList = computed(() => props.list.filter(o => !!o.picUrl))

const onModelValueUpdated = (val) => {
  emit("update:modelValue", val);
};

const swipeRef = ref(null);

const navigateHandle = async ({ content, jumpType, id }) => {
  navigateTo(content, jumpType);
  await uActions({ actionType: 11, eventId: id });
};

const onSwiper = () => {
  if (swipeRef.value.state.active === filterList.value.length - 1) {
    onModelValueUpdated(false);
    emit("closed");
  } else {
    swipeRef.value.next();
  }
};

const onClosed = () => {
  if (swipeRef.value) {
    swipeRef.value.state.active = 0;
  }
}


</script>
