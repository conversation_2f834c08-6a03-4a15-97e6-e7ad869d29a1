<template>
  <div class="flex grow px-3 overflow-hidden my-2.5">
    <div class="flex flex-1">
      <Swiper
        class="w-full"
        direction="vertical"
        auto-height
        free-mode
        slidesPerView="auto"
        spaceBetween="10"
        :modules="[FreeMode]"
        @swiper="onSwiper"
        @slideChange="onSlideChange"
      >
        <SwiperSlide v-for="item in list" :key="item.id">
          <SingleModule v-if="item.row === 1" :list="item.lists" />
          <FourOneModule v-else-if="item.row === 4" :list="item.lists" />
        </SwiperSlide>
      </Swiper>
    </div>
    <div
      ref="navRef"
      role="tablist"
      aria-orientation="vertical"
      class="rounded-md ml-3 relative overflow-y-scroll scrollbar-none bg-[var(--van-black-500)]"
    >
      <div
        v-for="(item, i) in list"
        :key="item.id"
        ref="titles"
        :class="[i === currentIndex ? 'menu-category-active' : 'menu-category']"
        @click="selectHandler(item.id, i)"
      >
        <div v-if="item.normal" class="w-6 h-6 mb-2">
          <img :src="item.icon" alt="" srcset="">
        </div>
        <div v-else class="w-8 h-8 mb-2">
          <ImgComponents :imgUrl="item.icon" objectFit="fill" />
        </div>
        <p class="text-xs">{{ item.name }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Swiper, SwiperSlide } from "swiper/vue";
import "swiper/css";
import { FreeMode } from "swiper/modules";
import { scrollTopTo } from '@/utils/dom.js';

import FourOneModule from "./4_1_module";
import SingleModule from "./single_module";

const props = defineProps({
  cate_id: [Number, String],
  list: {
    type: Array,
    default: () => [],
  },
});

let cancelScrollTopToRaf

const navRef = ref(null);
const titles = ref([]);

const swiperRef = ref(null);

const currentIndex = ref(0);

const onSwiper = (swiper) => {
  swiperRef.value = swiper;
};

const onSlideChange = (swiper) => {
  currentIndex.value = swiper.activeIndex;
};

const selectHandler = (cate_id, index) => {
  if (currentIndex.value !== index) {
    scrollIntoView()
    swiperRef.value.slideTo?.(index);
  }
};


// scroll active tab into view
const scrollIntoView = () => {
  const nav = navRef.value;

  if (!nav || !titles.value[currentIndex.value]) {
    return;
  }
  const title = titles.value[currentIndex.value];
  const to = title.offsetTop - (nav.offsetHeight - title.offsetHeight) / 2;

  if (cancelScrollTopToRaf) cancelScrollTopToRaf();
  cancelScrollTopToRaf = scrollTopTo(
    nav,
    to,
    0.3,
  );
};

watch(
  () => props.cate_id,
  (val) => {
    if (val) {
      const index = props.list.findIndex(o => o.id === Number(val));
      if (index > -1) currentIndex.value = index;
    }
  },
  { immediate: true }
)

watch(
  currentIndex,
  () => {
    nextTick(() => {
      scrollIntoView()
    })
  }
)

</script>
