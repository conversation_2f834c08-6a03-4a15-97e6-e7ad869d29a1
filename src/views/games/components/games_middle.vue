<template>
  <div class="relative flex flex-none items-center justify-between bg-cover bg-[url(@/assets/image/home_nav_bg.png)] h-65px mb-1">
    <!-- <img src="@/assets/image/logo-1.png" class="w-99px -ml-1.5 mr-1"> -->

    <div class="flex flex-col items-center justify-center relative w-99px h-45px overflow-hidden -ml-1.5 mr-1 bg-cover bg-[url(@/assets/image/logo-bg.png)]">
      <div className="w-78px h-28px overflow-hidden" v-if="merchantLogo">
        <ImgComponents :imgUrl="merchantLogo?.game_logo" />
      </div>
    </div>

    <div class="flex flex-1 items-center justify-between">
      <div
        v-for="item in options"
        :key="item.key"
        class="flex flex-col items-center justify-center"
        @click="navigator<PERSON><PERSON>ler(item)"
      >
        <img :src="item.icon" class="h-22px" />
        <p class="text-xs mt-1">{{ item.title }}</p>
      </div>
    </div>
    <div class="flex flex-col relative w-99px h-45px -mr-1.5 ml-1 pl-1.5 overflow-hidden box-border text-xs bg-cover bg-[url(@/assets/image/logo-bg.png)]">
      <div class="flex flex-1 items-center">
        <img src="@/assets/icons/id.png" class="h-3">
        <span class="mx-0.5">{{ id }}</span>
      </div>
      <div class="flex flex-1 items-center" @click="refreshHandler">
        <img src="@/assets/icons/b.png" class="h-3 mr-0.5s">
        <span class="text-[#ffefb0] mx-0.5">{{ wallet?.points }}</span>
        <img src="@/assets/icons/r.png" :class="['w-11px h-10px', { 'animate-spin': loading }]">
      </div>

      <!-- <div class="h-px w-140px top-50% absolute left-50% -translate-x-50% -translate-y-50% bg-cover scale-50	 bg-gradient-orange-1"></div> -->

    </div>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from '@/store/app';
import { useUserStore } from '@/store/user';
import { useService } from '@/hooks';
import rechargeIcon from '@/assets/icons/recharge.png';
import withdrawIcon from '@/assets/icons/withdraw.png';
import gifIcon from '@/assets/icons/gif.png';
import recordIcon from '@/assets/icons/record.png';
import serviceIcon from '@/assets/icons/service.png';

const router = useRouter();

const appStore = useAppStore();
const userStore = useUserStore();
const merchantLogo = computed(() => appStore.appConfig?.merchantLogo);
const { type, nickName, id } = storeToRefs(userStore);

const emit = defineEmits(['refresh']);

const props = defineProps({
  tel: String,
  loading: Boolean,
  wallet: {
    type: Object,
    default: () => ({
      points: '0.00'
    })
  }
});

const options = [
  { title: '存款', icon: rechargeIcon, key: 101 },
  { title: '提现', icon: withdrawIcon, key: 102 },
  { title: '优惠', icon: gifIcon, key: 103 },
  { title: '记录', icon: recordIcon, key: 104 },
  { title: '客服', icon: serviceIcon, key: 105 }
];

const refreshHandler = () => {
  emit('refresh');
};

const navigatorWithdraw = () => {
  if (type.value !== 0) {
    router.push({ path: '/withdraw' })
  } else {
    appStore.SetHasNeedPhoneAction(true);
  }
};

const navigatorHandler = ({ key }) => {
  switch (key) {
    case 101:
      router.push({ path: '/cz' });
      return;
    case 102:
      navigatorWithdraw();
      return;
    case 103:
      router.push({ path: '/hd' });
      return;
    case 104:
      router.push({ path: '/game_record' });
      return;
    case 105:
      useService();
      return;
  }
};

onBeforeRouteLeave((to, from, next) => {
  appStore.SetHasNeedPhoneAction(false);
  next();
});
</script>
