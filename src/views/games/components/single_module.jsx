import { isEmpty } from '@/utils';
import { navigateLink } from '../utils';

export default defineComponent({
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    nid: Number
  },
  setup(props) {
    const { navigateTo } = navigateLink();
    const navigateHandle = (val) => {
      navigateTo(val)
    };

    return () => (
      <div className="grid grid-cols-1 gap-y-2.5">
        {!isEmpty(props.list) &&
          props.list.map((val) => {
            return (
              <div className="h-20 overflow-hidden rounded"><ImgComponents imgUrl={val.icon} objectFit="fill" onClick={() => navigateHandle(val)} /></div>
            )
          })}
      </div>
    );
  },
});
