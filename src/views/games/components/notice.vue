<template>
  <van-notice-bar v-if="list.length">
    <template #left-icon><div class="pr-2.5"><img src="@/assets/icons/notice-s.png"></div></template>
    <ul class="flex items-center">
      <li class="mr-8" v-for="val in list" :key="val.id" @click.stop="navigateHandle(val)">
        <div v-html="val.content"></div>
      </li>
    </ul>
  </van-notice-bar>
</template>

<script setup>
import { useNavigate } from '@/hooks';
import { uActions } from '@/api/user';
const props = defineProps({
  list: {
    type: Array,
    default: () => []
  },
  background: {
    type: String,
    default: '#1f1f1f'
  }
})


const { navigateTo } = useNavigate();

const navigateHandle = async ({ jumpUrl, jumpType, id }) => {
  navigateTo(jumpUrl, jumpType);
  await uActions({ actionType: 11, eventId: id });
};

</script>
