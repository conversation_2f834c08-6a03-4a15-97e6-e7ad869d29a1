import { navigateLink } from '../utils';

export default defineComponent({
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    row: Number,
    nid: Number,
    itemClass: {
      type: String,
      default: 'h-20'
    },
    imgClass: {
      type: String,
      default: 'w-55px'
    }
  },
  setup(props) {
    const { navigateTo } = navigateLink();
    const navigateHandle = (val) => {
      navigateTo(val)
    };

    return () => (
      <div className="grid grid-cols-4 gap-1.5">
        {props.list.length > 0 &&
          props.list.map((val) => {
            return (
              <div
                key={val.id}
                class={['flex flex-col items-center justify-around bg-[var(--van-black-500)] rounded', props.itemClass]}
                onClick={() => navigateHandle(val)}
              >
                <div className="w-85% aspect-square relative overflow-hidden rounded-full">
                  <ImgComponents imgUrl={val.icon} />
                </div>
                <p className="line-clamp-1" style="font-size: 10px;">{val.name}</p>
              </div>
            );
          })}
      </div>
    );
  },
});
