<template>
  <PageLayout>
    <div class="flex flex-col h-full">
      <div class="flex-none w-355px mx-auto py-1 h-30" v-if="advList && advList.length">
        <Banner :options="advList" />
      </div>
      <GameMiddle :wallet="wallet" :tel="tel" :loading="loading" @refresh="onRefresh" />
      <div class="relative flex-none game-notice" v-if="notice && notice.length">
        <Notice :list="notice"></Notice>
      </div>
      <GameSwiper :list="list" :cate_id="cate_id"></GameSwiper>
    </div>
  </PageLayout>

  <GamePopupBanner v-model="show" :list="banners" @closed="closedPopupHandle"></GamePopupBanner>
</template>
<script setup>
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/store/user';
import { useAppStore } from '@/store/app';
import Banner from '@/components/banner/banner.vue';
import Notice from './components/notice.vue';
import GameMiddle from './components/games_middle.vue';
import GameSwiper from './components/games_swiper.vue';
import GamePopupBanner from './components/game_popup_banner.vue';
import { GetGameCategoryAPI, GetRecentPlayAPI } from '@/api/game';
import recentPlayIcon from '@/assets/icons/recent.png';

defineOptions({
  name: 'Games'
});

const userStore = useUserStore();
const appStore = useAppStore();

const {
  query: { cate_id }
} = useRoute();

const { wallet, tel } = storeToRefs(userStore);

const loading = ref(false);

const show = ref(false);
const advList = computed(() => appStore.adItem(23));
const notice = computed(() => appStore.adItem(16));
const banners = computed(() => appStore.adItem(31));

const categoryList = shallowRef([]);
const recentPlayList = shallowRef([]);

const list = computed(() => {
  return [
    {
      id: 0,
      is_maintain: 0,
      row: 4,
      normal: true,
      icon: recentPlayIcon,
      name: '最近',
      lists: recentPlayList.value
    },
    ...categoryList.value
  ];
});

const getRecentPlay = async () => {
  try {
    const res = await GetRecentPlayAPI();
    console.log(res, 'recentPlay res');
    recentPlayList.value = res.data.items || [];
  } catch {
    //
  }
};

const getGameCategory = async () => {
  try {
    const res = await GetGameCategoryAPI();
    console.log(res, 'gameCategory res');
    categoryList.value = res.data;
  } catch {
    //
  }
};

const onRefresh = async () => {
  loading.value = true;
  try {
    await userStore.updateUserWalletData();
  } catch {
  } finally {
    loading.value = false;
  }
};

const closedPopupHandle = () => {
  sessionStorage.setItem('GAME_POPUP', '1');
};

onMounted(() => {
  getRecentPlay();
  getGameCategory();
});

onActivated(() => {
  getRecentPlay();
  const isShow = sessionStorage.getItem('GAME_POPUP');
  if (!isShow && banners.value.length) {
    show.value = true;
  }

  userStore.updateUserWalletData();
});
</script>

<style lang="less">
.game-notice {
  --van-notice-bar-height: 20px;
  --van-notice-bar-background: url(@/assets/image/notice-bg.png) no-repeat center center/cover;
  --van-notice-bar-font-size: 12px;
  --van-notice-bar-padding: 0;
  z-index: 1;
  margin: 0 10px;
}
</style>
