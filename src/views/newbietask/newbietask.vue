<script setup>
import { claimRewards, listSubTask } from '@/api/activity'
import { useNavigate } from '@/hooks'

const {
  query: { title, taskTypeId, isNewerTask, third },
} = useRoute()

const router = useRouter()

const { navigateTo } = useNavigate()

const items = ref([])

const refreshing = ref(false)

const toast = showLoadingToast({ duration: 0, forbidClick: true })

async function onRefresh() {
  try {
    const res = await listSubTask({
      taskTypeId: Number(taskTypeId),
      isNewerTask: true,
    })
    items.value = res.data.list || []
  }
  catch (error) {
    //
  }
  finally {
    toast.close()
    refreshing.value = false
  }
}

function generatorPercentage({ done, total }) {
  return done / total * 100
}

function navigatorHandle({ linkUrl }) {
  navigateTo(linkUrl, 1)
}

function clickRightHandle() {
  router.push({ path: '/activity_claim', query: { taskTypeId, isNewerTask } })
}

function claimHandle({ taskListId }) {
  claimRewards({
    taskListId,
    isNewerTask: true,
    source: third || '',
  })
    .then(() => {
      showSuccessToast('领取成功')
      onRefresh()
    })
    .catch((error) => {
      //
    })
}

onRefresh()
</script>

<template>
  <SecondPageLayout :title="title">
    <template #navbar-right>
      <van-button round plain type="primary" size="mini" class="w-[75px]" @click="clickRightHandle">
        领取记录
      </van-button>
    </template>
    <div class="box-border h-full">
      <van-pull-refresh v-model="refreshing" class="min-h-full" @refresh="onRefresh">
        <div v-for="item in items" :key="item.id" class="van-hairline--bottom flex items-center py-4">
          <div class="mx-4 h-[42px] w-[42px] overflow-hidden">
            <ImgComponents :img-url="item.pic" />
          </div>
          <div class="flex flex-1 flex-col justify-around text-xs">
            <div class="mb-1 flex items-center">
              <span>{{ item.title }}</span>
              <div class="relative ml-2.5 h-5 min-w-[59px] flex items-center rounded-full bg-[#ffcfb1] text-[0.625rem] text-[#ff553e]">
                <img src="@/assets/icons/PT.png" class="h-5 w-5">
                <span class="flex-1 text-center">送{{ item.award }}</span>
              </div>
            </div>
            <div class="flex items-center">
              <div class="mr-1 w-[150px]">
                <van-progress
                  :percentage="generatorPercentage(item.progress)"
                  stroke-width="5"
                  color="#ffd631"
                  track-color="#666666"
                  :show-pivot="false"
                />
              </div>
              <span>{{ item.progress.done > item.progress.total ? item.progress.total : item.progress.done }}/{{ item.progress.total }}</span>
            </div>
          </div>
          <div class="mr-3 flex-none">
            <van-button v-if="item.awardStatus === 'Done'" size="mini" type="info" round class="w-[70px]">
              {{ item.buttonWord }}
            </van-button>
            <van-button
              v-else-if="item.awardStatus === 'DoJob'"
              size="mini"
              type="primary"
              round
              class="w-[70px]"
              @click="navigatorHandle(item)"
            >
              {{ item.buttonWord }}
            </van-button>
            <van-button
              v-else-if="item.awardStatus === 'FetchNow'"
              size="mini"
              type="primary"
              round
              class="w-[70px]"
              @click="claimHandle(item)"
            >
              领取
            </van-button>
            <van-button
              v-else-if="item.awardStatus === 'Done'"
              size="mini"
              type="info"
              round
              class="w-[70px]"
            >
              已完成
            </van-button>
            <van-button
              v-else-if="item.awardStatus === 'Miss'"
              size="mini"
              type="info"
              round
              class="w-[70px]"
            >
              未完成
            </van-button>
          </div>
        </div>
      </van-pull-refresh>
    </div>
  </SecondPageLayout>
</template>
