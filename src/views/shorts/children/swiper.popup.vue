<template>
  <van-popup
    :show="show"
    @update:show="onModelShowUpdated"
    @opened="openedHandle"
    teleport="body"
    safe-area-inset-top
    safe-area-inset-bottom
    destroy-on-close
    :duration="0"
    :style="{ width: '100vw', height: '100vh', left: 0, right: 0, maxWidth: '100%', background: 'black' }"
  >
    <HeaderComponents
      :similarity="params.similarityType"
      :duration="params.durationType"
      :callBackHandle="callBackHandle"
      @click-left="onModelShowUpdated(false)"
      @click-right="clickRightHandle"
    >
      <div class="relative flex-none mt-5" v-if="notices && notices.length">
        <Notice :list="notices"></Notice>
      </div>
      <div class="relative flex-none mt-5">
        <DanmuLayer :banners="banners" :swiper-index="swiperIndex" :videoTime="videoTime"></DanmuLayer>
      </div>
    </HeaderComponents>


    

    <div v-if="show" class="h-full w-full relative overflow-hidden">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh" :disabled="swiperIndex !== 0" class="w-screen h-full">
        <Swiper
          virtual
          :modules="[Virtual]"
          :slides-per-view="1"
          :initial-slide="swiperIndex"
          direction="vertical"
          class="h-full"
          @swiper="onSwiper"
          @slideChange="onSlideChange"
        >
          <SwiperSlide v-for="(val, index) in state.list" :key="val.id" :virtualIndex="index">
            <div class="short_video_items w-full h-full relative flex flex-col">
              <div class="w-full flex-1">
                <VideoComponents
                  v-if="index === swiperIndex && val.isAd !== true"
                  :vid="val.id"
                  :isCloudMode="val.isCloudMode"
                  :cloudFileId="val.cloudFileId"
                  :playUrl="val.playUrl"
                  :cloudUrl="val.cloudUrl"
                  :videoCover="val.videoCover"
                  :videoDuration="val.videoDuration"
                  :timeupdateCall="timeupdateHandle"
                  :playCall="playHandle"
                  @left="leftHandle"
                />

                <div v-else-if="index !== swiperIndex && val.isAd !== true" class="full-wrapper relative w-full h-full overflow-hidden">
                  <ImgComponents :imgUrl="val.videoCover" />
                </div>

                <div v-else class="full-wrapper relative w-full h-full overflow-hidden" @click="navigateHandle(val)">
                  <ImgComponents :imgUrl="val.picUrl" />
                </div>
              </div>

              <div>
                <LeftContent :title="val.title" :tags="val.tags"></LeftContent>

                <RightContent
                  v-if="index === swiperIndex && val.isAd !== true"
                  :vid="val.id"
                  :isCollected="val.isCollected"
                  :likeTimes="val.likeTimes"
                  :commentTimes="val.commentTimes"
                  :shareTimes="val.shareTimes"
                />
              </div>
            </div>
          </SwiperSlide>
        </Swiper>
      </van-pull-refresh>
    </div>
  </van-popup>


  <PromptComponents
    v-model="visible"
    title="温馨提示"
    content="升级VIP可观看视频，每日无限观影~"
    cancelText="推广送VIP"
    confirmText="升级VIP"
    :closeOnClickOverlay="true"
    :hasCancelBtn="true"
    :confirmCall="promptConfirmHandler"
    :cancelCall="promptCancelHandler"
  />

</template>

<script setup>
import { storeToRefs } from 'pinia';
import { insertAds } from '@/utils';
import { useAppStore } from '@/store/app';
import { useUserStore } from '@/store/user';
import { uActions } from '@/api/user';
import { shortVideoList } from '@/api/video';
import { useNavigate } from '@/hooks';
import PromptComponents from '@/components/prompt/prompt';
import Notice from "@/components/notice/notice.vue";
import DanmuLayer from '@/components/danmuLayer/danmuLayer.vue';
import HeaderComponents from '../components/header.vue';
import VideoComponents from '../components/video';
import LeftContent from '../components/leftContent';
import RightContent from '../components/rightContent';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Virtual } from 'swiper/modules';
import 'swiper/css';

const props = defineProps({
  show: Boolean,
});

const emits = defineEmits(['update:show']);

const router = useRouter();

const appStore = useAppStore();
const userStore = useUserStore();

const { navigateTo } = useNavigate();
const visible = ref(false);
const ads = computed(() => appStore.adItem(9));
const notices = computed(() => appStore.adItem(21));
const banners = computed(() => appStore.adItem(26));
const { vipLevel, vipValidTime, shortVideoFreeWatchLeft } = storeToRefs(userStore);

const swiperRef = ref(null);
const videoTime = ref(0);
const refreshing = ref(false);
const swiperIndex = ref(0);

const onModelShowUpdated = (val) => {
  emits('update:show', val);
};

const clickRightHandle = () => {
  router.push({ path: '/search' });
};

const state = reactive({
  rawList: [],
  list: []
});

const params = reactive({
  pageIndex: 1,
  pageSize: 20,
  similarityType: 0,
  durationType: 0,
  tags: []
});

const callBackHandle = (val, type) => {
  params[type] = val;
  if (type === 'similarityType') {
    const tmp = state.list[swiperIndex.value];
    if (tmp && tmp.isAd !== true) {
      params.tags = tmp.tags;
    }
  }
  onRefresh(true);
};

const visibleHandle = ({ list, index, categoryId, themeId }) => {
  state.rawList = list;
  state.list = insertAds(list, ads.value);
  params.categoryId = categoryId;
  params.themeId = themeId;
  swiperIndex.value = index;
};

const openedHandle = () => {
  nextTick(() => {
    if (swiperRef.value) {
      setTimeout(() => {
        swiperRef.value?.slideTo(swiperIndex.value, 0);
        swiperRef.value?.update();
      }, 150);
    }
  });
};

const onRefresh = async (isRefresh) => {
  swiperIndex.value = 0;
  params.pageIndex = 1;
  if (isRefresh !== true) {
    params.similarityType = 0;
    params.durationType = 0;
    params.tags = [];
  }
  state.rawList = [];
  try {
    await getShortVideoList(true);
  } finally {
    refreshing.value = false;
  }
}

const getShortVideoList = async (isRefresh = false) => {
  try {
    const { data } = await shortVideoList(params);
    const list = data.list;
    if (list.length) {
      if (isRefresh) {
        state.rawList = list; // 刷新时直接替换
      } else {
        state.rawList = state.rawList.concat(list); // 分页时追加
      }
      state.list = insertAds(state.rawList, ads.value);
    }
  } catch (e) {
    state.rawList = [];
    state.list = [];
  }
};


const promptCancelHandler = () => {
  visible.value = false;
  router.push({ path: '/invite' });
};

const promptConfirmHandler = () => {
  visible.value = false;
  router.push({ path: '/vip' });
};


const onSwiper = (swiper) => {
  swiperRef.value = swiper;
};

const onSlideChange = (swiper) => {
  swiperIndex.value = swiper.activeIndex;
};

const playHandle = () => {
  if (vipLevel.value > 0 || vipValidTime.value > 0) {
    return true;
  } else {
    if (shortVideoFreeWatchLeft.value < 1) {
      visible.value = true;
      return false;
    } else {
      return true;
    }
  }
};

const leftHandle = () => {
  shortVideoFreeWatchLeft.value -= 1;
};

const navigateHandle = async ({ content, jumpType, id }) => {
  navigateTo(content, jumpType);
  await uActions({ actionType: 11, eventId: id });
};

const timeupdateHandle = (time) => {
  videoTime.value = time;
}

watch(swiperIndex, (val) => {
  if (state.list.length === swiperIndex.value + 1) {
    params.pageIndex += 1;
    setTimeout(() => {
      getShortVideoList();
    }, 200);
  }
});

defineExpose({
  visibleHandle
})
</script>
