<template>
  <PageLayout>
    <div class="flex flex-col overflow-hidden h-full relative">
      <TopBanner></TopBanner>
      <div class="flex flex-col">
        <div
          class="flex items-center h-65px shorts-tabs"
          style="
            background: linear-gradient(90deg, #443b16, #171717 100%);
            --van-tabs-nav-background: transparent;
            --van-tab-font-size: 15px;
          "
        >
        <!-- <img src="@/assets/image/logo.png" class="w-77.5px mx-3" /> -->
          <div className="w-77.5px h-27.5px overflow-hidden mx-3" v-if="merchantLogo">
            <ImgComponents :imgUrl="merchantLogo?.short_video_logo" />
          </div>

          <div class="flex-1 flex items-center overflow-hidden">
            <van-tabs v-if="tabList.length > 0" v-model:active="tabIndex" shrink :ellipsis="false" :line-height="0" class="w-full">
              <van-tab v-for="(val, i) in tabList" :key="val.id" :title="val.name" :name="i" />
            </van-tabs>
          </div>
          <div class="flex">
            <div class="w-7 h-7 mx-4" @click="searchNavigateHandle">
              <img src="@/assets/icons/search-b.png" />
            </div>
          </div>
        </div>
      </div>
      <NestedSwiper v-if="tabList.length > 0" :list="tabList" v-model:tab-index="tabIndex"></NestedSwiper>
    </div>
  </PageLayout>

  <ShortPopop ref="ShortPopopRef" v-model:show="show"></ShortPopop>

</template>

<script setup>
import { storeToRefs } from 'pinia';
import { NavigationBarAPI } from '@/api/home';
import { useAppStore } from '@/store/app';
import LoadingPage from '@/components/loadingPage/LoadingPage';
import TopBanner from '@/components/topBanner/topBanner.vue';

import NestedSwiper from './components/NestedSwiper.vue';
import ShortPopop from './children/swiper.popup.vue';
import { InjectionKey } from './keys';

defineOptions({
  name: 'Shorts'
});

const route = useRoute();
const router = useRouter();
const appStore = useAppStore();
const merchantLogo = computed(() => appStore.appConfig?.merchantLogo);

const tabList = ref([]);
const tabIndex = ref(0);

const show = ref(false);
const ShortPopopRef = ref();

const shortsProvide = provide(InjectionKey, {
  opened: (row) => {
    ShortPopopRef.value?.visibleHandle(row);
    show.value = true;
  }
});

const searchNavigateHandle = () => {
  router.push({ path: '/search', query: { type: 'name' } });
};

const getNavigationBar = () => {
  try {
    NavigationBarAPI({ mediaType: 2 }).then((res) => {
      tabList.value = res.data;
    });
  } catch (error) {
    console.log(error, 'error');
  }
};

watch(() => route.query.refreshKey, () => {
  if (route.query.refreshKey) {
    getNavigationBar()
  }
})

onMounted(() => {
  getNavigationBar();
});
</script>


<style lang="less">
.shorts-tabs .van-tab--active {
  font-size: 18px;
}
</style>