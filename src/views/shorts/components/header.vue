<template>
  <div class="fixed top-0 left-0 w-full z-10 van-safe-area-top" style="--van-nav-bar-background: transparent;">
    <van-nav-bar
      left-arrow
      :border="false"
      @click-left="onClickLeft"
      @click-right="onClickRight"
    >
      <!-- <template #title>
        <div class="flex items-center justify-center relative">
          <van-popover v-model:show="similarityShow" theme="light" :actions="similarityActions" @select="onSelectSimilarity">
            <template #reference>
              <span class="flex items-center px-1 text-xs">
                <div class="van-ellipsis">{{ SimilarityText }}</div>
                <van-icon name="play" size="10" :style="`transform: rotate(${similarityShow ? '-90' : '90'}deg);`" />
              </span>
            </template>
          </van-popover>

          <van-popover v-model:show="durationShow" theme="light" :actions="durationActions" @select="onSelectDuration">
            <template #reference>
              <span class="flex items-center px-1 text-xs">
                <div class="van-ellipsis">{{ DurationText }}</div>
                <van-icon name="play" size="10" :style="`transform: rotate(${durationShow ? '-90' : '90'}deg);`" />
              </span>
            </template>
          </van-popover>
        </div>
      </template> -->
      <template #right>
        <img src="@/assets/icons/<EMAIL>" class="w-4 h-4" />
      </template>
    </van-nav-bar>
    <slot></slot>
  </div>
  
</template>


<script setup>

const props = defineProps({
  similarity: Number,
  duration: Number,
  callBackHandle: Function
})

const emit = defineEmits(['click-left', 'click-right'])

const similarityShow = ref(false);
const similarityActions = [
  { text: '相似热播视频', value: 1 },
  { text: '相似新上热播', value: 2 },
  { text: '相似最近上架', value: 3 },
  { text: '相似观看最多', value: 4 },
  { text: '相似收藏最多', value: 5 },
  { text: '相似随机显示', value: 6 },
];

const durationShow = ref(false);
const durationActions = [
  { text: '不限时长', value: 0 },
  { text: '小于5分钟', value: 1 },
  { text: '大于5分钟', value: 2 },
];

const onSelectSimilarity = (action) => {
  props.callBackHandle(action.value, 'similarityType')
}

const onSelectDuration = (action) => {
  props.callBackHandle(action.value, 'durationType')
}


const SimilarityText = computed(() => {
  return similarityActions.find(o => o.value === props.similarity)?.text || '点击选择相似视频'
})

const DurationText = computed(() => {
  return durationActions[props.duration].text
})

const onClickLeft = () => {
  emit('click-left');
}

const onClickRight = () => {
  emit('click-right');
}

</script>

<style lang="less">
.van-dropdown-menu__title {
  font-size: var(--van-font-size-sm);
  line-height: var(--van-line-height-md);
}

.van-dropdown-menu__title:after {
  border-color: transparent transparent white white;
}

</style>