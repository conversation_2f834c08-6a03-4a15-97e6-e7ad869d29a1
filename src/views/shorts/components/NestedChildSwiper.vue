<template>
  <Swiper
    nested
    css-mode
    :slides-per-view="1"
    :initial-slide="0"
    :resistanceRatio="0.5"
    @swiper="onSwiper"
    @slideChange="onSlideChange"
    class="h-full"
  >
    <SwiperSlide v-for="val in list" :key="val.themeId" v-slot="{ isActive }">
      <SwiperComponent :cid="cid" :tid="val.themeId" :isActive="isActive"></SwiperComponent>
    </SwiperSlide>
  </Swiper>
</template>


<script setup>
import SwiperComponent from './swiper.component.vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
const props = defineProps({
  list: Array,
  cid: Number,
  active: Number
});

const emit = defineEmits(['update:active']);

const swiperRef = ref();

const onSwiper = (swiper) => {
  swiperRef.value = swiper;
}

const onSlideChange = (swiper) => {
  emit('update:active', swiper.activeIndex);
};


watch(
  () => props.active,
  (newIndex) => {
    const swiper = swiperRef.value
    if (swiper && swiper.activeIndex !== newIndex) {
      swiper.slideTo(newIndex, 0)
    }
  }
)

</script>