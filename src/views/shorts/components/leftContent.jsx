import { selectTagsLabel } from '@/utils';
import { useAppStore } from '@/store/app';
const LeftContent = defineComponent({
  name: 'LeftContent',
  props: {
    title: String,
    tags: Array
  },
  setup(props) {
    const router = useRouter();
    const appStore = useAppStore();
    const tagMaps = selectTagsLabel(appStore.tagList, props.tags);


    const navigatorHandle = ({ code, name }) => {
      router.push({ name: 'VideoTag', params: { tagId: code }, query: { title: name } })
    }

    return () => (
      <div className="leftContent_container absolute bottom-12 pl-2 z-[100]">
        <p className="w-[250px] text-sm font-normal line-clamp-2">{props.title}</p>
        <ul className="flex items-center mt-1.5 w-64 overflow-x-auto">
          {tagMaps &&
            tagMaps.map((val) => {
              return (
                <li class="mr-2 text-xs flex-shrink-0 text-[var(--van-primary-color)]" key={val.code} onClick={() => navigatorHandle(val)}>
                  #{val.name}
                </li>
              );
            })}
        </ul>
      </div>
    );
  }
});

export default LeftContent;
