<template>
  <div class="w-full relative" v-once>
    <Swiper
      :slidesPerView="'auto'"
      :modules="[Autoplay]"
      :autoplay="{
        delay: 1000,
        disableOnInteraction: false,
      }"
    >
      <SwiperSlide v-for="item in list" :key="item.id" style="width: 100px; margin-left: 10px;">
        <div class="flex flex-col w-full">
          <div class="w-full h-75px overflow-hidden relative rounded-lg" @click="navigatorHandler(item)">
            <ImgComponents :imgUrl="item.picUrl" />
            <div class="absolute left-0 bottom-0 w-50px h-22px flex items-center justify-center bg-[#171717]/[.67] rounded-tr-lg rounded-bl-lg">
              <span class="text-sm">广告</span>
            </div>
          </div>
          <p class="text-sm my-2.5">{{ item.name }}</p>
        </div>
      </SwiperSlide>
    </Swiper>
  </div>
</template>

<script setup>
import { uActions } from '@/api/user';
import { useNavigate } from '@/hooks';
import { Swiper, SwiperSlide } from "swiper/vue";
import { Autoplay } from 'swiper/modules';
import "swiper/css";
import "swiper/css/autoplay";

const props = defineProps({
  list: {
    type: Array,
    default: () => []
  }
});

const { navigateTo } = useNavigate();

const navigatorHandler = async ({ id, jumpType, content }) => {
  navigateTo(content, jumpType);
  await uActions({ actionType: 11, eventId: id });
};

</script>