import { listSource } from '@/api/promotion';
import { useAppStore } from '@/store/app';
import useClipboard from 'vue-clipboard3';
import { useQRCode } from '@vueuse/integrations/useQRCode';

export default defineComponent({
  props: {
    modelValue: Boolean
  },
  setup(props, { emit }) {
    const appStore = useAppStore();
    const merchantLogo = computed(() => appStore.appConfig?.merchantLogo);

    
    const inviteCode = ref('');
    const swipers = ref([]);
    const swiperIndex = ref(0);

    const title = import.meta.env.VITE_APP_TITLE;
    const { toClipboard } = useClipboard();

    const onModelValueUpdated = (val) => {
      emit('update:modelValue', val)
    }

    const onOpen = () => {
      listSource().then(({ data: { list, code } }) => {
        inviteCode.value = code;
        swipers.value = (list || []).map((o) => ({
          ...o,
          qrcode: useQRCode(o.url, {
            width: 128,
            height: 128,
            margin: 0
          })
        }));
      });
    };

    const changeHandle = (index) => {
      swiperIndex.value = index;
    };

    const copyHandler = () => {
      const swiper = swipers.value[swiperIndex.value];
      toClipboard(swiper.url);
      showToast('复制成功');
    }

    return () => (
      <van-popup
        show={props.modelValue}
        onUpdate:show={onModelValueUpdated}
        onOpen={onOpen}
        round={true}
        teleport="body"
        style={{ width: '92%', backgroundColor: '#2c2c2c' }}
      >
        <div className="flex flex-col items-center h-100 py-5 box-border">
          <div className="flex items-center mx-4 mb-6">
            <div className="w-15 h-15 overflow-hidden">
              <ImgComponents imgUrl={merchantLogo.value?.invite_friend_logo} />
            </div>
            {/* <img src="/pwa-512x512.png" class="w-[60px] h-[60px] rounded-md" /> */}
            <div className="ml-3">
              <p className="text-lg mb-2">{ title }</p>
              <p className="text-xs">邀请好友注册，自动叠加无限制1天观看</p>
            </div>
          </div>
          <van-swipe class="w-full" loop={false} indicator-color="white" onChange={changeHandle}>
            {
              swipers.value.map(item => (
                <van-swipe-item>
                  <div className="w-full flex flex-col items-center justify-center">
                    <div className="p-2 bg-white rounded">
                      <div className="w-32 h-32">
                        <img src={item.qrcode} alt="" />
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-center mt-4">邀请码：{ inviteCode.value }</p>
                </van-swipe-item>
              ))
            }
          </van-swipe>
          <div className="mt-auto" style="--van-button-default-height: 40px;">
            <van-space>
              <van-button type="primary" class="w-100px" onClick={copyHandler}>复制链接</van-button>
              <van-button type="primary" class="w-100px">截图保存</van-button>
            </van-space>
          </div>
        </div>
      </van-popup>
    )
  }
})