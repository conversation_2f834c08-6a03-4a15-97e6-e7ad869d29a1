<template>
  <div class="px-2.5 h-full overflow-y-scroll" ref="container">
    <van-pullRefresh v-model="refreshing" @refresh="onRefresh" class="min-h-full">
      <SwiperSwipe v-if="banners && banners.length" :list="banners"></SwiperSwipe>
      <van-list
        v-model:loading="loading"
        :finished="finished"
        :immediate-check="false"
        @load="onLoad"
      >
        <div class="grid grid-cols-2 gap-2.5" >
          <template v-for="(val, index) in state.list">
            <ShortVideoCover
              v-if="val.isAd !== true"
              :key="val.id"
              :title="val.title"
              :imgUrl="val.videoCover"
              :tags="val.tags"
              :views="val.viewTimes"
              :time="val.videoDuration"
              :vid="val.id"
              :type="val.type"
              @click="clickPopupHandle(index)"
            />

            <div v-else class="flex flex-col">
              <div class="w-full h-215px overflow-hidden rounded" @click="navigateHandle(val)">
                <ImgComponents :imgUrl="val.picUrl" object-fit="fill" />
              </div>
              <div class="px-1 py-3 flex items-center">
                <div class="w-28px h-4 rounded bg-white/[.1] flex items-center justify-center mr-2">
                  <span class="text-9px">广告</span>
                </div>
                <p class="text-xs text-white line-clamp-1">{{ val.name }}</p>
              </div>
            </div>
          </template>
        </div>

        <template #loading>
          <LoadingPage />
        </template>
      </van-list>
    </van-pullRefresh>
  </div>
</template>

<script setup>
import { useAppStore } from '@/store/app';
import { uActions } from '@/api/user';
import { shortVideoList } from '@/api/video';
import SwiperSwipe from './swiper.swipe.vue';
import { useNavigate } from '@/hooks';
import LoadingPage from '@/components/loadingPage/LoadingPage';
import ShortVideoCover from './ShortVideoCover';
import { insertAds } from '@/utils';
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';

import { InjectionKey } from '../keys';

const { navigateTo } = useNavigate();
const injected = inject(InjectionKey);

const props = defineProps({
  cid: Number,
  tid: Number,
  isActive: Boolean
});

const appStore = useAppStore();

const ads = computed(() => appStore.adItem(30));
const banners = computed(() => appStore.adItem(29));
const top = ref(0)
const container = ref(null)
const refreshing = ref(false);
const loading = ref(false);
const error = ref(false);
const finished = ref(false);
const list = ref([]);
const hasLoaded = ref(false);

const state = reactive({
  rawList: [],
  list: []
})

const clickPopupHandle = (idx) => {
  injected.opened({
    list: toRaw(state.rawList),
    index: idx,
    categoryId: props.cid,
    themeId: props.tid
  });
};

const params = reactive({
  pageIndex: 1,
  pageSize: 20,
  similarityType: 0,
  durationType: 0,
  tags: [],
  categoryId: props.cid,
  themeId: props.tid
})

const onRefresh = () => {
  params.pageIndex = 1;
  finished.value = false;
  loading.value = true;
  onLoad();
};

const onLoad = async () => {
  try {
    if (refreshing.value) {
      state.rawList = [];
      state.list = [];
      refreshing.value = false;
    }

    const { data } = await shortVideoList(params);
    params.pageIndex += 1;
    state.rawList = state.rawList.concat(data.list);
    state.list = insertAds(state.rawList, ads.value);
    finished.value = data.list.length < data.pageSize;

  } catch (e) {
    state.rawList = [];
    state.list = [];
  } finally {
    hasLoaded.value = true;
    loading.value = false;
  }
};

onBeforeRouteLeave((to, from) => {
  top.value = container.value.scrollTop
});

onActivated(() => {
  container.value.scrollTop = top.value
});

watch(
  () => props.isActive,
  (val) => {
    if (val && !hasLoaded.value) {
      onRefresh();
    }
  }
);


const navigateHandle = async ({ content, jumpType, id }) => {
  navigateTo(content, jumpType);
  await uActions({ actionType: 11, eventId: id });
};

</script>
