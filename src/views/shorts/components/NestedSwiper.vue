<template>
  <div class="w-full flex-1 relative z-1 flex flex-col overflow-hidden">
    <div class="w-full h-full overflow-hidden max-h-full inset-0 overflow-scrolling">
      <Swiper
        nested
        :slides-per-view="1"
        :touchStartPreventDefault="false"
        class="h-full"
        @swiper="onSwiper"
        @slideChange="onSlideChange"
      >
        <swiper-slide v-for="val in list" :key="val.id">
          <div class="h-full flex flex-col overflow-hidden">
            <div class="relative mb-2.5">
              <div
                class="shorts-theme-tabs"
                @touchstart.stop
                @touchmove.stop
                style="--van-tabs-line-height: 40px; --van-tab-active-text-color: white"
              >
                <van-tabs
                  v-model:active="tabIndexes[val.id]"
                  shrink
                  :ellipsis="false"
                  :line-height="0"
                  @change="(i) => onChangeTab(i, val.id)"
                >
                  <van-tab v-for="(tab, i) in val.themeList" :key="tab.themeId" :name="i">
                    <template #title>{{ tab.themeName }}</template>
                  </van-tab>
                </van-tabs>
              </div>

              <div class="absolute top-0 right-0 bottom-0 w-10 flex items-center justify-center" @click="navigatorHandle">
                <img src="@/assets/icons/nav-icon.png" class="w-5" />
              </div>
            </div>

            <div class="flex-1 overflow-hidden">
              <NestedChildSwiper :cid="val.id" :list="val.themeList" v-model:active="tabIndexes[val.id]" />
            </div>
          </div>
        </swiper-slide>
      </Swiper>
    </div>
  </div>
</template>

<script setup>
import NestedChildSwiper from './NestedChildSwiper.vue';
import SwiperComponent from './swiper.component.vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
const props = defineProps({
  tabIndex: Number,
  list: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:tabIndex']);
const router = useRouter();
const swiperRef = ref();
const currentrIndex = ref(0);

const tabIndexes = reactive({});

const onSwiper = (swiper) => {
  swiperRef.value = swiper;
};

const onSlideChange = (swiper) => {
  currentrIndex.value = swiper.activeIndex;

  emit('update:tabIndex', swiper.activeIndex);
};

const onChangeTab = (val, cid) => {
  tabIndexes[cid] = val;
};

const navigatorHandle = () => {
  router.push({ path: '/dsp_channel' });
};

watch(
  () => props.tabIndex,
  (val) => {
    if (swiperRef.value) {
      swiperRef.value.slideTo(val, 0);
    }
  }
);

onMounted(() => {
  props.list.forEach((val) => {
    tabIndexes[val.id] = 0;
  });
});
</script>

<style lang="less">
.shorts-theme-tabs {
  padding-right: 30px;
  .van-tab {
    background-color: #3b3b3d;
    padding: 0 12px;
    border-radius: 15px;
    height: 28px;
    margin-top: 6px;
    margin-right: 10px;
    line-height: 28px;
  }

  .van-tab--active {
    color: var(--van-black);
    background-color: var(--van-primary-color);
  }
}
</style>
