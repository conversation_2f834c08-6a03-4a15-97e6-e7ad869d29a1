import TimeLabel from '@/components/timeLabel/timeLabel';
import VideoCoverExtra from '@/components/video/VideoCoverExtra';
export default defineComponent({
  name: 'ShortVideoCover',
  props: {
    vid: Number,
    type: Number,
    tid: Number,
    cid: Number,
    imgUrl: String,
    time: Number,
    views: Number,
    title: String,
    tags: Array,
  },
  emits: ['click'],
  setup(props, { emit }) {
    const clickHandle = () => {
      emit('click');
    };
    return () => (
      <div className="flex flex-col" onClick={clickHandle}>
        <div className="flex relative h-215px">
          <div className='w-full h-full overflow-hidden rounded-t'>
            <ImgComponents imgUrl={props.imgUrl} hasAnimation />
          </div>
          <TimeLabel time={props.time} views={props.views} />
        </div>
        <VideoCoverExtra title={props.title} tags={props.tags} />
      </div>
    )
  }
})