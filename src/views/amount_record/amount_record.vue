<template>
  <SecondPageLayout>
    <template #navbar>
      <van-nav-bar safe-area-inset-top left-arrow :title="$route.meta.title" @click-left="$router.go(-1)" :border="false" />

      <van-tabs v-model:active="_type" line-width="25">
        <van-tab v-for="tab in tabs" :name="tab.type" :title="tab.title" />
      </van-tabs>
    </template>

    <div style="min-height: 75vh">
      <KeepAlive>
        <WithdrawRecord v-if="_type === 1"></WithdrawRecord>
        <RechargeRecord v-else-if="_type === 2"></RechargeRecord>
        <BonusRecord v-else-if="_type === 3"></BonusRecord>
      </KeepAlive>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import WithdrawRecord from './components/withdraw_record.vue';
import RechargeRecord from './components/recharge_record.vue';
import BonusRecord from './components/bonus_record.vue';

const {
  query: { type }
} = useRoute();
const _type = ref(type ? Number(type) : 1);
const tabs = [
  { title: '提现记录', type: 1 },
  { title: '充值记录', type: 2 },
  { title: '彩金记录', type: 3 }
];
</script>
