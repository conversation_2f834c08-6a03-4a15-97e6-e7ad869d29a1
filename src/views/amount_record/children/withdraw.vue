<template>
  <SecondPageLayout>
    <div class="flex items-center justify-between py-3 px-4">
      <div>
        <p>提现金额</p>
        <p class="text-2xl mt-1">¥{{ shallow.amount }}</p>
      </div>
      <van-button v-if="shallow.apply_state === 0" size="small" round disabled >待审核</van-button>
      <van-button v-else-if="shallow.apply_state === 1" size="small" round type="success">成功</van-button>
      <van-button v-else-if="shallow.apply_state === 2" size="small" round type="danger">未通过</van-button>
    </div>

    <van-cell-group>
      <van-cell title="订单号" :value="orderid" />
      <van-cell title="账单时间" :value="formatter(shallow.ordertime * 1000, 'YYYY-MM-DD HH:mm')" />
      <van-cell title="银行" :value="shallow.bank_name" />
      <van-cell title="姓名" :value="shallow.realname"/>
      <van-cell title="银行卡账号" :value="shallow.bank_card" />
      <van-cell title="到账金额" :value="shallow.real_amt" />
      <van-cell title="手续费" :value="shallow.fee" />
      <van-cell title="拒绝理由" :value="shallow.reject_reason" value-class="!text-red-500"  />
    </van-cell-group>
  </SecondPageLayout>
</template>

<script setup>
import { GetWithdrawDetailAPI } from '@/api/finance';
import { formatter } from '@/utils';

const { query: { orderid } } = useRoute()

const shallow = shallowRef({})

const onRefresh = () => {
  GetWithdrawDetailAPI({ orderid }).then((res) => {
    shallow.value = res.data
  })
}

onRefresh()

</script>