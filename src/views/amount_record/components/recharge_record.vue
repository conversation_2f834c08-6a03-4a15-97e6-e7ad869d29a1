<template>
  <div class="py-2 px-3 h-full box-border">
    <van-pullRefresh
      v-model="refreshing"
      @refresh="onRefresh"
      class="min-h-full"
    >
      <van-list
        v-model:loading="loading"
        v-model:error="error"
        :finished="finished"
        @load="onLoad"
      >
        <ul class="flex flex-col text-xs">
          <li class="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-md">
            <p style="width: 35%;" class="text-center">金额</p>
            <p style="width: 35%;" class="text-center">充值时间</p>
            <p style="width: 30%;" class="text-center">状态</p>
          </li>
          <li v-for="item in list" :key="item.orderid" class="flex items-center justify-center h-12">
            <p style="width: 35%;" class="text-center">{{ item.amount }}</p>
            <p style="width: 35%;" class="text-center whitespace-pre-wrap">{{ formatter(item.ordertime * 1000) }}</p>
            <p style="width: 30%;" class="text-center">
              <span v-if="item.apply_state === 0">待处理</span>
              <span v-else-if="item.apply_state === 1" class="text-[var(--van-primary-color)]">成功</span>
              <span v-else-if="item.apply_state === 2" class="text-[var(--van-red)]">未通过</span>
            </p>
          </li>
        </ul>
      </van-list>

      <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />
    </van-pullRefresh>
  </div>
</template>

<script setup>
import { useListExtra } from '@/hooks'
import { formatter } from '@/utils'
import { GetRechargeRecordAPI } from '@/api/finance'
import EmptyPage from '@/components/empty/empty';

const {
  list,
  isFetching,
  refreshing,
  loading,
  error,
  finished,
  onRefresh,
  onLoad
} = useListExtra({
  serverHandle: GetRechargeRecordAPI,
  pagination: { data: 'data.list' }
})
</script>

