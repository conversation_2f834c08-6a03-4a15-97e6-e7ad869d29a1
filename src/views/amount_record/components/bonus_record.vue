<template>
  <div class="py-2 px-3 h-full box-border">
    <van-pullRefresh
      v-model="refreshing"
      @refresh="onRefresh"
      class="min-h-full"
    >
      <van-list
        v-model:loading="loading"
        v-model:error="error"
        :finished="finished"
        @load="onLoad"
      >
        <ul class="flex flex-col text-xs">
          <li class="flex items-center justify-center h-10 bg-[var(--van-black-500)] rounded-md">
            <p style="width: 35%;" class="text-center">金额</p>
            <p style="width: 35%;" class="text-center">时间</p>
            <p style="width: 30%;" class="text-center">类型</p>
          </li>
          <li v-for="item in list" :key="item.orderid" class="flex items-center justify-center h-10">
            <p style="width: 35%;" class="text-center">{{ item.money }}</p>
            <p style="width: 35%;" class="text-center whitespace-pre-wrap">{{ item.created_at }}</p>
            <p style="width: 30%;" class="text-center">
              <span class="text-[var(--van-blue)]">{{ item.type_info }}</span>
            </p>
          </li>
        </ul>
      </van-list>

      <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />
    </van-pullRefresh>
  </div>
</template>

<script setup>
import { useListExtra } from '@/hooks'
import { GetBonusAPI } from '@/api/game'
import EmptyPage from '@/components/empty/empty';

const {
  list,
  isFetching,
  refreshing,
  loading,
  error,
  finished,
  onRefresh,
  onLoad
} = useListExtra({
  serverHandle: GetBonusAPI,
  pagination: { data: 'data.list' }
})
</script>

