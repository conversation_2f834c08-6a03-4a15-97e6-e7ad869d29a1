<template>
  <SecondPageLayout @click-right="onClickRight">
    <template #navbar-right>完成</template>
    <div class="flex flex-col px-3 py-6">
      <div class="text-sm py-3">我的频道 <span class="text-xs text-[#808080]">长按拖动排序</span></div>
        <Draggable v-model="themes" item-key="id" class="grid grid-cols-3 gap-2.5">
          <template #item="{ element, index }">
            <div class="h-34px rounded text-sm relative flex items-center justify-center bg-[#3b3b3b]">
              <span>{{ element.name }}</span>
              <!-- <div class="absolute top-1 right-1 w-2.5 h-2.5" @click.stop="deleteHandle(element, index)">
                <img src="@/assets/icons/delete.png" alt="">
              </div> -->
            </div>
          </template>
        </Draggable>


      <div class="text-sm py-3">更多频道</div>
      <!-- <div class="grid grid-cols-3 gap-2.5">
        <div class="h-34px rounded text-sm relative flex items-center justify-center bg-[#3b3b3b]" v-for="val in userThemes" :key="val.id">
          <span>{{ val.name }}</span>
          <div class="absolute top-1 right-1 w-2.5 h-2.5" @click.stop="addHandle(val, i)">
            <img src="@/assets/icons/plus.png" alt="">
          </div>
        </div>
      </div> -->
    </div>
  </SecondPageLayout>

  <PromptComponents
    v-model="show"
    title="温馨提示"
    content="是否保存刚才编辑的内容？"
    confirmText="保存"
    hasCancelBtn
    :cancelCall="cancelHandler"
    :confirmCall="confirmHandler"
  />
</template>

<script setup>
import Draggable from 'vuedraggable'
import { ThemesAPI, SaveThemesAPI } from '@/api/user';
import PromptComponents from "@/components/prompt/prompt";

const router = useRouter();

const show = ref(false);
const themes = ref([]);
const userThemes = ref([]);

const onClickRight = () => {
  show.value = true;
};

const cancelHandler = () => {
  show.value = false;
};

const deleteHandle = (val, i) => {
  themes.value.splice(i, 1);
  userThemes.value.push(val);
};

const addHandle = (val, i) => {
  userThemes.value.splice(i, 1);
  themes.value.push(val);
}

const getThemes = async () => {
  try {
    const { data } = await ThemesAPI({ mediaType: 2 });

    themes.value = data.themes;
    userThemes.value = data.userThemes;
  } catch (e) {
    //
  }
};

const confirmHandler = async () => {
  try {
    const res = await SaveThemesAPI({ mediaType: 2, themeIds: themes.value.map(o => o.id) });

    if (res) {
      show.value = false;
      showToast('保存成功');
      router.push({
        name: 'Shorts',
        query: { refreshKey: Date.now() } // 每次不同，强制触发 watch
      })
    }
  } catch (e) {
    //
  }
}


onMounted(() => {
  getThemes();
})


</script>