import { FollowPlanWaysConfigAPI } from '@/api/game';
import type {
  FollowTypeOption
} from '@/types/api';
import { useQuery } from '@tanstack/vue-query';
import { computed, ref, watch, type Ref } from 'vue';

/**
 * Composable for fetching follow plan ways configuration options
 * Uses Vue Query's useQuery for data fetching with caching and reactivity
 */
export function useFollowPlanWaysConfigQuery(gameId: Ref<number | null> | number | null) {
  // Convert gameId to a ref if it's not already
  const gameIdRef = ref(typeof gameId === 'number' ? gameId : gameId?.value || null);
  
  // Watch for changes if gameId is a ref
  if (typeof gameId === 'object' && gameId !== null) {
    watch(gameId, (newValue) => {
      gameIdRef.value = newValue;
    }, { immediate: true });
  }

  // Query for fetching configuration options
  const query = useQuery({
    queryKey: computed(() => ['followPlanConfigOptions', gameIdRef.value]),
    queryFn: async (): Promise<FollowTypeOption[]> => {
      return (await FollowPlanWaysConfigAPI({ gameId: gameIdRef.value })).data;
    },
    enabled: computed(() => !!gameIdRef.value),
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });

  // Loading and error states
  const isLoading = computed(() => query.isLoading.value);
  const isError = computed(() => query.isError.value);
  const error = computed(() => query.error);
  const isSuccess = computed(() => query.isSuccess.value);

  // Helper methods
  const refetch = () => query.refetch();

  return {
    // Data
    configOptions: query.data,
    // State
    isLoading,
    isError,
    isSuccess,
    error,
    // Methods
    refetch,
    // Raw query for advanced usage
    query,
  };
}

/**
 * Type definitions for the composable return value
 */
export type UseFollowPlanWaysConfigQueryReturn = ReturnType<typeof useFollowPlanWaysConfigQuery>;
