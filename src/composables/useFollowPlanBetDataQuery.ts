import { FollowPlanBetDataAPI } from '@/api/game';
import type { FollowPlanBetDataRequest, FollowPlanBetDataResponse } from '@/types/api';
import { useQuery } from '@tanstack/vue-query';
import { computed, ref, watch, type Ref } from 'vue';

/**
 * Composable for fetching follow plan bet data
 * Uses Vue Query's useQuery for data fetching with caching and reactivity
 */
export function useFollowPlanBetDataQuery(
  gameId: Ref<number | null> | number | null,
  playWayCode: Ref<string | null> | string | null,
  pageSortKey: Ref<string> | string = ''
) {
  // Convert parameters to refs if they're not already
  const gameIdRef = ref(typeof gameId === 'number' ? gameId : gameId?.value || null);
  const playWayCodeRef = ref(typeof playWayCode === 'string' ? playWayCode : playWayCode?.value || null);
  const pageSortKeyRef = ref(typeof pageSortKey === 'string' ? pageSortKey : pageSortKey?.value || '');

  // Watch for changes if parameters are refs
  if (typeof gameId === 'object' && gameId !== null) {
    watch(
      gameId,
      (newValue) => {
        gameIdRef.value = newValue;
      },
      { immediate: true }
    );
  }

  if (typeof playWayCode === 'object' && playWayCode !== null) {
    watch(
      playWayCode,
      (newValue) => {
        playWayCodeRef.value = newValue;
      },
      { immediate: true }
    );
  }

  if (typeof pageSortKey === 'object' && pageSortKey !== null) {
    watch(
      pageSortKey,
      (newValue) => {
        pageSortKeyRef.value = newValue;
      },
      { immediate: true }
    );
  }

  // Create query key that updates when parameters change
  const queryKey = computed(() => ['followPlanBetData', gameIdRef.value, playWayCodeRef.value, pageSortKeyRef.value]);

  // Query enabled condition - only run when we have required parameters
  const enabled = computed(() => gameIdRef.value !== null && playWayCodeRef.value !== null && playWayCodeRef.value !== '');

  // Query function
  const queryFn = async (): Promise<FollowPlanBetDataResponse> => {
    if (!gameIdRef.value || !playWayCodeRef.value) {
      throw new Error('gameId and playWayCode are required');
    }

    const requestData: FollowPlanBetDataRequest = {
      gameId: gameIdRef.value,
      playWayCode: playWayCodeRef.value,
      pageSortKey: pageSortKeyRef.value
    };

    const response = await FollowPlanBetDataAPI(requestData);
    return response.data;
  };

  // Use Vue Query
  const query = useQuery({
    queryKey,
    queryFn,
    enabled,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
    retry: 2
  });

  // Return reactive values and methods
  return {
    // Data
    betData: computed(() => query.data.value),

    // States
    isLoading: computed(() => query.isLoading.value),
    isFetching: computed(() => query.isFetching.value),
    isError: computed(() => query.isError.value),
    isSuccess: computed(() => query.isSuccess.value),

    // Error handling
    error: computed(() => query.error.value),

    // Methods
    refetch: query.refetch
  };
}
