import { EventEmitter, EventEmitterClass, Call } from '@mini-code/base-func';
import { aesEncrypt, aesDecrypt, gzipDecompress, ungzipDecompress } from '../handler';
import { RECEIVE_STATE_UPDATE, ON_CONNECT_CLOSE, CONNECT_READY } from '../constant';

const onOpenMark = 'onOpen';
const onMessageMark = 'onMessage';
let lastHiddenAt;

class SocketHelper extends EventEmitterClass {
  constructor(getParams, opts = {}) {
    super();

    this.connected = false;
    this.connecting = false;
    this.isClosed = true;

    this.getParams = getParams;
    this.opts = opts;
    this.reconnection = this.opts.reconnection || false;
    this.reconnectionAttempts = this.opts.reconnectionAttempts || Infinity;
    this.reconnectionDelay = this.opts.reconnectionDelay || 1000;
    this.reconnectTimeoutId = 0;
    this.reconnectionCount = 0;
    this.connect(getParams, opts);

    // handle the mobile phone lock screen then unlock screen, reconnect
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        lastHiddenAt = Date.now();
      }

      if (document.visibilityState === 'visible') {
        if (lastHiddenAt) {
          const lockedDuration = Date.now() - lastHiddenAt;
          // If locked for more than 1 minutes, reconnect
          if (lockedDuration > 60000) {

            if (!this.connected) {
              this.disconnect();
              this.reconnect()
            }
          }
          // Use lockedDuration for reconnect logic, etc.
          lastHiddenAt = null;
        } else {
          console.log('🔓 Tab became visible (no hidden time recorded)');
        }
      }
    });
  }

  connect(getParams, opts) {
    console.log('connect ws');
    const protocol = opts.protocol || '';
    if (this.connecting) return;
    this.connecting = true;
    return getParams().then(({ connectionUrl, is_aes, aes_key, aes_iv }) => {
      this.is_aes = is_aes;
      this.aes_key = aes_key;
      this.aes_iv = aes_iv;
      this.socket = protocol === '' ? new WebSocket(connectionUrl) : new WebSocket(connectionUrl, protocol);

      this.socket.binaryType = 'arraybuffer';

      this.socket.onopen = this.onopen;
      this.socket.onmessage = this.onmessage;
      this.socket.onerror = this.onerror;
      this.socket.onclose = this.onclose;

      return this.socket;
    });
  }

  reconnect() {
    console.log('reconnect ws');
    if (this.reconnectionCount < this.reconnectionAttempts) {
      this.reconnectionCount++;
      clearTimeout(this.reconnectTimeoutId);

      this.reconnectTimeoutId = setTimeout(() => {
        this.connect(this.getParams, this.opts);
      }, this.reconnectionDelay);
    }
  }

  disconnect() {
    console.log('disconnect ws');
    this.reconnectionCount = 0;
    this.socket?.close();
  }

  send = ({ message, success, fail }) => {
    let buffer;
    const finalMessage = gzipDecompress(JSON.stringify(message));

    if (this.is_aes) {
      buffer = aesEncrypt(finalMessage, this.aes_key, this.aes_iv);
    } else {
      buffer = new Uint8Array(finalMessage).buffer;
    }
    if (!this.socket) {
      fail?.(new Error('WebSocket is not connected'));
      return;
    }
    this.socket?.send(buffer);
    success?.();
  };

  onopen = (event) => {
    console.log('onOpen ws');
    this.connected = true;
    this.connecting = false;
    this.reconnectionCount = 0;
    this.isClosed = false;
    this.emit(onOpenMark, event);
    this.emit(CONNECT_READY, event);
  };

  onmessage = (event) => {
    const { data } = event;
    let decompressedData;
    if (this.is_aes) {
      const decodedData = aesDecrypt(data, this.aes_key, this.aes_iv);
      decompressedData = ungzipDecompress(decodedData);
    } else {
      decompressedData = ungzipDecompress(new Uint8Array(data));
    }
    try {
      const finalData = JSON.parse(decompressedData);
      this.emit(onMessageMark, finalData);
    } catch {
      EventEmitter.emit(RECEIVE_STATE_UPDATE, data);
    }
  };

  onerror = (e) => {
    console.log('onErr ws', e);
    /** 如果发生错误，则主动关闭 websocket 链接 */
    this.socket?.close();
  };

  onclose = (e) => {
    console.log('onClose ws');
    console.log(e);
    this.handleException(e);
    if (this.reconnection) this.reconnect();
  };

  handleException = (event) => {
    this.connected = false;
    this.connecting = false;
    this.socket = null;
    this.isClosed = true;
    EventEmitter.emit(ON_CONNECT_CLOSE, event);
  };
}

export { SocketHelper, onOpenMark, onMessageMark, EventEmitter };
