
import CryptoJS from 'crypto-js'

export function aesEncrypt(data, aesKey = "", aesIv = "") {
  const _key = CryptoJS.enc.Base64.parse(aesKey);
  const _iv = CryptoJS.enc.Base64.parse(aesIv);

  const encrypted = CryptoJS.AES.encrypt(CryptoJS.lib.WordArray.create(data), _key, {
      iv: _iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
  });
  const ciphertext = encrypted.ciphertext;

  const encryptedArray = new Uint8Array(ciphertext.sigBytes);
  for (let i = 0; i < ciphertext.sigBytes; i++) {
    encryptedArray[i] = ciphertext.words[i >>> 2] >>> (24 - (i % 4) * 8) & 0xff;
  }
  console.log(encryptedArray)
  return encryptedArray.buffer
}

export function aesDecrypt(binaryData, aesKey = "", aesIv = "") {
  const _key = CryptoJS.enc.Base64.parse(aesKey);
  const _iv = CryptoJS.enc.Base64.parse(aesIv);

  const decrypted = CryptoJS.AES.decrypt({
    ciphertext: CryptoJS.lib.WordArray.create(binaryData)
  }, _key, {
    iv: _iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });

  const decryptedArray = new Uint8Array(decrypted.sigBytes);
  for (let i = 0; i < decrypted.sigBytes; i++) {
    decryptedArray[i] = (decrypted.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
  }

  return decryptedArray
}