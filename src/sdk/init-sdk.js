import { SocketHelper } from './socket';
import { getLocalStorage } from '@/utils';
const SDKErrorDesc = '请先调用 InitSDK';

let $WS;

function GetWS() {
  if (!$WS) console.error(SDKErrorDesc);
  return $WS;
}

function WSSend(message) {
  return new Promise((resolve, reject) => {
    if (!$WS) {
      throw Error(SDKErrorDesc);
    }
    return $WS.send({
      message,
      success: (res) => {
        resolve(res);
      },
      fail: (res) => {
        // failResHandler(res);
        reject(res);
      }
    });
  });
}

/**
 * 初始化 websocket
 * @getParams {() => {connectionUrl: string, is_aes: boolean, aes_key: string, aes_iv: string}} params - 一个返回配置对象的函数
 * @returns {Promise<void>}
 */
function InitSDK(getParams) {
  return new Promise((resolve, reject) => {
    $WS = new SocketHelper(getParams, {
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 3000,
    });

    $WS.on('onOpen', () => {
      resolve($WS);
    });
  });
}

/**
 * 检查是否正常链接
 */
function CheckConnectState() {
  let isConnecting = false;
  if (!$WS) return isConnecting;
  isConnecting = $WS.connected;
  return isConnecting;
}

/**
 * 关闭 websocket 链接
 */
function CloseWS() {
  if ($WS) {
    if ($WS.socket) $WS.socket.close();
    $WS = null;
  }
}

export { InitSDK, GetWS, WSSend, CheckConnectState, CloseWS };
