import { defineComponent } from "vue";
import { useAppStore } from "@/store/app";
import { selectTagsLabel } from "@/utils";
export default defineComponent({
  props: {
    title: String,
    tags: {
      type: Array,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const appStore = useAppStore();
    const tagMaps = selectTagsLabel(appStore.tagList, props.tags);
    return () => (
      <div class="flex flex-col px-1">
        <div class="text-xs text-white leading-8 line-clamp-1">
          {props.title}
        </div>
        <div class="text-[var(--van-primary-color)] text-10px mb-2.5 w-full whitespace-nowrap overflow-x-auto scrollable-none">
          <div class="flex items-center">
            {tagMaps &&
              tagMaps.map((val) => {
                return (
                  <span
                    class="bg-white/[.1] h-18px leading-[19px] rounded-full px-1 mr-1"
                    key={val.code}
                  >
                    {val.name}
                  </span>
                );
              })}
          </div>
        </div>
      </div>
    );
  },
});
