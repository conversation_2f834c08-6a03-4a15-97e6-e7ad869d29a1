import TimeLabel from '../timeLabel/timeLabel';
import Video<PERSON>overExtra from './VideoCoverExtra';
export default defineComponent({
  props: {
    vid: Number,
    type: Number,
    tid: Number,
    cid: Number,
    imgUrl: String,
    time: Number,
    views: Number,
    title: String,
    tags: Array,
    isWatched: Boolean
  },
  setup(props, { emit }) {
    const route = useRoute();
    const router = useRouter();

    const localIsWatched = ref(props.isWatched);

    const navigateHandle = () => {
      const { query, path } = route;

      const isVideosRoute = /^\/videos\/\d+$/.test(path);
      if (isVideosRoute) {
        router.replace({ path: `/videos/${props.vid}`, query });
      } else {
        router.push({ path: `/videos/${props.vid}`, query: { cid: props.cid, tid: props.tid } });
      };

      localIsWatched.value = true;
    };

    // 当 props.isWatched 变化时，同步到本地
    watch(() => props.isWatched, (val) => {
      localIsWatched.value = val;
    });

    return () => (
      <div className="flex flex-col rounded bg-[var(--van-black-500)]" onClick={navigateHandle}>
        <div className="flex relative h-90px">
          <div className="w-full h-full overflow-hidden rounded-t">
            <ImgComponents imgUrl={props.imgUrl} hasAnimation />
          </div>
          <TimeLabel time={props.time} views={props.views} />
          {localIsWatched.value && (
            <div className="absolute left-0 top-3 w-60px h-5 text-xs flex items-center justify-center bg-[#171717]/[.67] rounded-r-full">
              您已看过
            </div>
          )}
        </div>
        <VideoCoverExtra title={props.title} tags={props.tags} />
      </div>
    );
  }
});
