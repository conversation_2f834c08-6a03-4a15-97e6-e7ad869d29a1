<template>
  <van-tabbar
    v-model="active"
    :border="false"
    route
    class="relative flex-initial"
    :fixed="false"
    safe-area-inset-bottom
    :before-change="onBeforChange"
  >
      <van-tabbar-item
        v-for="option in options"
        :key="option.path"
        :to="option.path"
        :name="option.name"
        class="relative"
      >
        <template #icon="props">
          <img :src="props.active ? option.active : option.inactive" />
        </template>
        <span>{{  option.title }}</span>
        <img v-if="option.badge" src="@/assets/icons/gift_vip.png" class="absolute h-12.5px right-4 top-2" />
      </van-tabbar-item>
    </van-tabbar>
</template>

<script setup>
import { userLogged } from '@/hooks';

import app from '@/assets/icons/<EMAIL>';
import app_1 from '@/assets/icons/<EMAIL>';
import csp from '@/assets/icons/<EMAIL>';
import csp_1 from '@/assets/icons/<EMAIL>';
import dy from '@/assets/icons/<EMAIL>';
import dy_1 from '@/assets/icons/<EMAIL>';
import yx from '@/assets/icons/<EMAIL>';
import yx_1 from '@/assets/icons/<EMAIL>';
import wd from '@/assets/icons/<EMAIL>';
import wd_1 from '@/assets/icons/<EMAIL>';

const active = ref(0)

const options = [
  // {
  //   active: app_1,
  //   inactive: app,
  //   title: '热门',
  //   name: 'Home',
  //   path: '/home'
  // },
  {
    active: csp_1,
    inactive: csp,
    title: '视频',
    name: 'Video',
    path: '/video'
  },
  {
    active: dy_1,
    inactive: dy,
    title: '抖阴',
    name: 'Shorts',
    path: '/dsp'
  },
  {
    active: yx_1,
    inactive: yx,
    title: '游戏',
    name: 'Games',
    path: '/yx',
    badge: true,
    needLogged: true
  },
  {
    active: wd_1,
    inactive: wd,
    title: '我的',
    name: 'Profile',
    path: '/wd',
    needLogged: true
  }
]


const onBeforChange = (name) => {
  if (name === 'Profile' || name === 'Games'  || name === 'Recharge' ) {
    return userLogged()
  } else {
    return true
  }
}

</script>
