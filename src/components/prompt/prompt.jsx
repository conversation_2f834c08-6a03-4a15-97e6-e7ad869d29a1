import ImgIcon from "@/assets/image/tip.png";
export default defineComponent({
  name: "PromptComponents",
  props: {
    title: String,
    modelValue: Boolean,
    content: String,
    closeable: Boolean,
    cancelText: {
      type: String,
      default: "取消",
    },
    confirmText: {
      type: String,
      default: "确定",
    },
    overlay: {
      type: Boolean,
      default: true,
    },
    closeOnClickOverlay: Boolean,	
    confirmCall: Function,
    cancelCall: Function,
    visibleHandle: Function,
    hasCancelBtn: Boolean,
    contentClass: String,
    safeAreaInsetBottom: Boolean,
    titleClass: {
      type: String,
      default: 'pt-8'
    },
    duration: {
      type: Number,
      default: 0.15
    }
  },
  setup(props, { emit, slots }) {
    const onModelValueUpdated = (val) => {
      emit("update:modelValue", val);
    };

    const onClosed = () => {
      onModelValueUpdated(false);
    };

    const onOpened = () => {};

    return () => (
      <van-popup
        show={props.modelValue}
        onUpdate:show={onModelValueUpdated}
        onClosed={onClosed}
        onOpened={onOpened}
        round={true}
        lazy-render={false}
        overlay={props.overlay}
        closeable={props.closeable}
        duration={props.duration}
        safe-area-inset-bottom={props.safeAreaInsetBottom}
        close-on-click-overlay={props.closeOnClickOverlay}
        style={{ width: "80%", backgroundColor: "white", overflow: "initial" }}
      >
        <div className="flex flex-col items-center text-black relative rounded-xl bg-gradient-primary">
          <div className="absolute -translate-y-1/2	">
            {slots.icon ? slots.icon() : <img src={ImgIcon} className="h-46.5px" />}
          </div>
          <p class={['text-lg font-medium', props.titleClass]}>{props.title}</p>
          <p class={['text-sm mx-3 my-5 whitespace-pre-wrap', props.contentClass]} v-html={props.content}></p>
          <div className="flex items-center justify-center pb-3">
            {props.hasCancelBtn && (
              <van-button
                round
                color="rgba(255, 214, 49, .4)"
                size="small"
                class="w-125px"
                style="margin-right: 12px; color: var(--van-button-primary-color);"
                onClick={props.cancelCall}
              >
                {props.cancelText}
              </van-button>
            )}
            <van-button
              round
              type="primary"
              size="small"
              class="w-125px"
              onClick={props.confirmCall}
            >
              {props.confirmText}
            </van-button>
          </div>
        </div>
      </van-popup>
    );
  },
});
