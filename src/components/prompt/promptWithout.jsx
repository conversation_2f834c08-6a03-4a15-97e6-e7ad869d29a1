export default defineComponent({
  name: "PromptWithoutComponents",
  props: {
    title: String,
    modelValue: Boolean,
    content: String,
    closeable: Boolean,
    cancelText: {
      type: String,
      default: "取消",
    },
    confirmText: {
      type: String,
      default: "确定",
    },
    overlay: {
      type: Boolean,
      default: true,
    },
    closeOnClickOverlay: Boolean,	
    confirmCall: Function,
    cancelCall: Function,
    visibleHandle: Function,
    hasCancelBtn: Boolean,
    contentClass: String,
    duration: {
      type: Number,
      default: 0.15
    }
  },
  setup(props, { emit, slots }) {
    const onModelValueUpdated = (val) => {
      emit("update:modelValue", val);
    };

    const onClosed = () => {
      onModelValueUpdated(false);
    };

    const onOpened = () => {};

    return () => (
      <van-popup
        show={props.modelValue}
        onUpdate:show={onModelValueUpdated}
        onClosed={onClosed}
        onOpened={onOpened}
        round={true}
        lazy-render={false}
        overlay={props.overlay}
        closeable={props.closeable}
        duration={props.duration}
        teleport="body"
        close-on-click-overlay={props.closeOnClickOverlay}
        style={{ width: "80%", backgroundColor: "white", overflow: "initial" }}
      >
        <div className="flex flex-col items-center text-black relative rounded-xl">
          <p className="text-lg font-medium">{ props.title }</p>
          <div className="text-center px-3 py-6">
            <slots></slots>
          </div>
          <div className="overflow-hidden px-5 pb-6">
            {props.hasCancelBtn && (
              <van-button
                round
                plain
                size="small"
                class="w-125px"
                type="primary"
                style="margin-right: 12px;"
                onClick={props.cancelCall}
              >
                {props.cancelText}
              </van-button>
            )}
            <van-button
              round
              type="primary"
              size="small"
              class="w-125px"
              onClick={props.confirmCall}
            >
              {props.confirmText}
            </van-button>
          </div>
        </div>
      </van-popup>
    );
  },
});
