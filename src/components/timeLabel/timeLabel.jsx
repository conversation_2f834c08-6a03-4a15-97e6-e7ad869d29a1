import { defineComponent } from "vue";
import { durationSeconds } from "@/utils";
import viewImg from '@/assets/icons/<EMAIL>';

export default defineComponent({
  props: {
    views: Number,
    time: Number
  },
  setup(props) {
    const formatViews = (num) => {
      return num > 9999 ? Math.floor(num / 1000) / 10 + "万" : num;
    };

    return () => (
      <div class="absolute flex items-center justify-between w-full box-border text-white bottom-1 px-2">
        <div class="flex items-center justify-center bg-white/[.3] w-12 h-5 rounded-full text-10px">
          <img src={viewImg} class="h-2" />
          <span class="ml-1">{formatViews(props.views)}</span>
        </div>
        <span class="text-10px">{durationSeconds(props.time)}</span>
      </div>
    );
  },
});
