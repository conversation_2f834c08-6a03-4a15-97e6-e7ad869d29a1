<template>
  <div class="flex items-center justify-around h-10">
    <div
      v-for="item in filterOptions"
      :key="item.value"
      class="flex items-center text-slate-300	text-sm "
      @click="handleSort(item)"
    >
      <span class="mr-1">{{ item.name }}</span>
      <img :src="item.value === order ? desc ? t2 : t3 : t1">
    </div>
  </div>
</template>

<script setup>
import t1 from '@/assets/icons/t1.png';
import t2 from '@/assets/icons/t2.png';
import t3 from '@/assets/icons/t3.png';

const props = defineProps({
  order: String,
  desc: Boolean
})

const emit = defineEmits(['update:order', 'update:desc', 'change'])

const filterOptions = [
  { name: '最新', value: 'Newest' },
  { name: '最热', value: 'Hottest' },
  { name: '时长', value: 'FilmDuration' },
]

const handleSort = ({ value }) => {
  if (props.order === value) {
    emit('update:desc', !props.desc)
  } else {
    emit('update:desc', true)
    emit('update:order', value)
  }

  emit('change')
}

</script>