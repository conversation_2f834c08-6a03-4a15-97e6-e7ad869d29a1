<template>
  <div class="grid grid-cols-4 gap-x-2.5 mx-3 pb-[25px]">
    <div class="relative" @click="navigatorHandle('/hd', { category: 2 })">
      <img src="@/assets/image/w1.png" alt="">
      <img src="@/assets/image/tag1.png" class="h-[20.5px] absolute left-50% -bottom-4 -translate-x-2/4">
    </div>
    <div class="relative" @click="navigatorHandle('/cz')">
      <img src="@/assets/image/w2.png" alt="">
      <img src="@/assets/image/tag2.png" class="h-[20.5px] absolute left-50% -bottom-4 -translate-x-2/4">
    </div>
    <div class="relative" @click="navigatorHandle('/yx')">
      <img src="@/assets/image/w3.png" alt="">
    </div>
    <div class="relative" @click="navigatorHandle('/promotion')">
      <img src="@/assets/image/w4.png" alt="">
      <img src="@/assets/image/hot.png" class="h-[20.5px] absolute left-50% -bottom-3 -translate-x-2/4">
    </div>
  </div>
</template>

<script setup>
const router = useRouter();

const navigatorHandle = (path, query) => {
  router.push({ path, query });
};

</script>