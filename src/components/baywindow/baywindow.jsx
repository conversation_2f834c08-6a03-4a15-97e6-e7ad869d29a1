import { useAppStore } from '@/store/app';
import { useNavigate } from '@/hooks';

export default defineComponent({
  name: 'baywindow',
  setup() {
    const appStore = useAppStore();
    const { navigateTo } = useNavigate();
    const list = computed(() => appStore.baywindow);

    const clickHandle = async ({ id, jump_url }) => {
      navigateTo(jump_url, 1);
    };

    return () => (
      <div className="w-full h-full">
        <van-swipe autoplay={2000} touchable={false} show-indicators={false}>
          {list.value.map((item) => (
            <van-swipe-item>
              <div className="w-full h-full overflow-hidden rounded-full" onClick={() => clickHandle(item)}>
                <ImgComponents imgUrl={item.icon_url} />
              </div>
            </van-swipe-item>
          ))}
        </van-swipe>
      </div>
    );
  }
});
