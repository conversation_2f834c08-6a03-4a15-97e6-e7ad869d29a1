<template>
  <van-swipe :autoplay="3000" indicator-color="white">
    <van-swipe-item v-for="option in options" :key="option.id">
      <div class="w-full h-full rounded-md overflow-hidden" @click="bannerHandle(option)">
        <ImgComponents :imgUrl="option.picUrl" hasAnimation />
      </div>
    </van-swipe-item>
  </van-swipe>
</template>

<script setup>
import { uActions } from '@/api/user';
import { useNavigate } from '@/hooks';

const props = defineProps({
  options: {
    type: Array,
    default: () => []
  }
});

const { navigateTo } = useNavigate();

const bannerHandle = async ({ id, jumpType, content }) => {
  navigateTo(content, jumpType);
  await uActions({ actionType: 11, eventId: id });
};
</script>
