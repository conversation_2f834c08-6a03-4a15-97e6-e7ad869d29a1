<template>
  <div class="grid grid-cols-5 gap-x-5">
    <div class="mt-2 text-center" v-for="option in options" :key="option.id">
      <div class="overflow-hidden rounded" @click="bannerHandle(option)">
        <ImgComponents :imgUrl="option.picUrl" hasAnimation />
      </div>
      <div class="line-clamp-1 leading-5 text-xs text-center text-[#cac4ac]">{{ option.name }}</div>
    </div>
  </div>
</template>

<script setup>
import { uActions } from '@/api/user';
import { useNavigate } from '@/hooks';

const props = defineProps({
  options: {
    type: Array,
    default: () => []
  }
});

const { navigateTo } = useNavigate();

const bannerHandle = async ({ id, jumpType, content }) => {
  navigateTo(content, jumpType);
  await uActions({ actionType: 11, eventId: id });
};
</script>
