<template>
  <van-popup
    :show="show"
    round
    :overlay="true"
    :close-on-click-overlay="closeOnClickOverlay"
    style="overflow: initial"
    @close="onPopupClose"
  >
    <div class="flex flex-col items-center px-8 pb-5 relative text-black relative rounded-xl bg-gradient-primary">
      <div class="relative -mt-[40px]">
        <img src="@/assets/image/person_main.png" class="w-[80px]" />
      </div>
      
      <h2 class="text-lg font-bold">请选择登录方式</h2>
      
      <div class="w-full space-y-4 mt-4">

        <van-button
          plain
          round
          block
          size="small"
          color="#666"
          class="flex items-center justify-center"
          @click="$emit('guest-login')"
        >
          <div class="flex items-center font-medium">
            <img src="@/assets/webp/icon/icon-login-guest.webp" class="w-3 h-3 object-contain mr-2" />
            游客登录
          </div>
        </van-button>
        
        <van-button
          round
          block
          color="#666"
          size="small"
          class="flex items-center justify-center font-medium"
          @click="$emit('account-login')"
        >
          <div class="flex items-center font-medium">
            <img src="@/assets/webp/icon/icon-login-account.webp" class="w-3 h-3 object-contain mr-2" />
            账号密码登录
          </div>
        </van-button>
        
        <van-button
          round
          block
          color="#FFCC00"
          size="small"
          class="flex items-center justify-center relative"
          @click="$emit('phone-login')"
        >
          <div class="flex items-center text-black font-medium">
            <img src="@/assets/webp/icon/icon-phone.webp" class="w-3 h-3 object-contain mr-2" />
            手机登录/注册
          </div>
          <img src="@/assets/webp/icon/icon-badge-phone-login.webp" class="absolute right-2 -top-[5px] h-5" alt="推荐" />
        </van-button>
        
        <div class="flex items-center text-xs font-medium text-gray-500 mt-2">
          <img src="@/assets/webp/icon/icon-error.webp" class="w-3 h-3 mr-1" />
          建议手机注册保障您的资金账号安全
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  closeOnClickOverlay: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:show', 'account-login', 'phone-login', 'guest-login']);

// Handle popup close event
const onPopupClose = () => {
  emit('update:show', false);
};
</script>
