<template>
  <van-popup
    :show="props.modelValue"
    @update:show="onModelValueUpdated"
    safe-area-inset-bottom
    position="bottom"
    teleport="body"
    class="bg-[var(--van-black-500)]"
  >
    <div class="flex items-center justify-between relative h-11">
      <button type="button" class="h-full px-4 text-sm bg-transparent border-none text-[var(--van-text-color-2)]" @click="onCancel">取消</button>
      <div class="van-picker__title van-ellipsis">选择默认头像</div>
      <button type="button" class="h-full px-4 text-sm bg-transparent border-none text-[var--van-primary-color)]" @click="onSubmit">确认</button>
    </div>

    <div class="flex items-center flex-wrap mb-3">
      <div
        v-for="(_, i) in Array.from({ length: 15 })"
        :key="i"
        class="flex items-center justify-center my-2.5 w-1/5"
        @click="clickHandle(i + 1)"
      >
        <div class="relative w-12 h-12">
          <img :src="`/static/webp/avatar${i + 1}.webp`" class="w-12 h-12" />
          <div v-if="Number(picUrl) === i + 1" class="absolute bottom-0 right-0">
            <img src="@/assets/icons/a-s.png" class="w-4 h-4" />
          </div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/store/user';
import { UpdateInfoDataAPI } from '@/api/user';

const userStore = useUserStore();
const { picUrl } = storeToRefs(userStore);

const props = defineProps({
  modelValue: {
    type: Boolean
  }
});

const emits = defineEmits(['update:modelValue']);

const onModelValueUpdated = (val) => {
  emits('update:modelValue', val);
};

const clickHandle = (val) => {
  picUrl.value = val;
};

const onCancel = () => {
  onModelValueUpdated(false);
};

const onSubmit = () => {
  UpdateInfoDataAPI({
    infoType: 'Avatar',
    val: String(picUrl.value)
  }).then(() => {
    onModelValueUpdated(false);
    userStore.updatePersonData();
  });
};
</script>
