<template>
  <div class="w-full pointer-events-none">
    <div
      v-show="show"
      :key="currentIndex"
      class="relative mx-auto w-345px h-30px pl-10 pr-2 py-1 box-border bg-[var(--van-primary-color)]/90 text-black text-sm rounded-xl pointer-events-auto"
      :style="containerStyle"
      @click.stop="navigatorHandle"
      @animationend="onDanmuAnimationEnd"
    >
      <div class="absolute -left-2 bottom-0 z-1"><img src="@/assets/icons/transfer.png" alt="" /></div>
      <div class="w-full overflow-hidden">
        <div ref="textRef" class="inline-block whitespace-nowrap will-change-transform backface-hidden" :style="scrollingStyle" v-html="bannerText"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { debounce } from 'lodash-es';
import { useNavigate } from '@/hooks';

const { navigateTo } = useNavigate();
const props = defineProps({
  banners: {
    type: Array,
    default: () => []
  },
  swiperIndex: Number,
  videoTime: Number
});
const currentIndex = ref(0);
const show = ref(false);
const bannerText = ref('');
const textRef = ref(null);
const containerStyle = ref({});
const scrollingStyle = ref({});
let mainTimer = null;
let nextTimer = null;
let overflowTimer = null;


const navigatorHandle = () => {
  const tmp = props.banners[currentIndex.value - 1];
  if (!tmp) return;
  navigateTo(tmp.jumpUrl, tmp.jumpType);
}

function reset() {
  clearTimeout(mainTimer);
  clearTimeout(overflowTimer);
  clearTimeout(nextTimer);
  currentIndex.value = 0;
  show.value = false;
  bannerText.value = '';
  containerStyle.value = {};
  scrollingStyle.value = {};
  
  if (textRef.value) {
    textRef.value.style.removeProperty('--distance');
  }
}

// 当swiper切换时重置
watch(
  () => props.swiperIndex,
  () => {
    reset();
  }
);

// 根据videoTime触发弹幕
watch(
  () => props.videoTime,
  () => {
    triggerBanner();
  }
);

function triggerBanner() {
  if (show.value) return;
  const banner = props.banners[currentIndex.value];
  if (!banner) return;
  if (props.videoTime < banner.videoPlayTime) return;
  // 显示弹幕
  showBanner(banner);
}

function onDanmuAnimationEnd(e) {
  if (e.animationName !== 'danmuSlide') return; // 只处理主动画
  show.value = false;
  scrollingStyle.value = {};

  // intervalTime 等待后再触发下一条
  const banner = props.banners[currentIndex.value - 1]; // 当前弹幕
  if (banner && typeof banner.intervalTime === 'number') {
    // 这里可以用 setTimeout，也可以用 requestAnimationFrame 实现等待
    setTimeout(() => {
      triggerBanner();
    }, banner.intervalTime * 1000);
  } else {
    triggerBanner();
  }
};

function showBanner(banner) {

  if (!banner?.content || typeof banner.stayTime !== 'number') {
    console.warn('Invalid banner data');
    return;
  }
  show.value = true;
  bannerText.value = banner.content;

  // 设置滑入+停留+滑出总时长
  const slideInTime = 2.5;
  const slideOutTime = 2.5;
  const totalSec = slideInTime + banner.stayTime + slideOutTime;
  
  containerStyle.value = { animation: `danmuSlide ${totalSec}s linear forwards` };
  currentIndex.value++;

  // 2.5s 入场后检查是否溢出并启动滚动
  nextTick(() => {
    overflowTimer = setTimeout(() => {
      checkOverflow();
    }, slideInTime * 1000);
  });

  // 完整动画结束后，等待 intervalTime 秒再显示下一条
  mainTimer = setTimeout(() => {
    show.value = false;
    scrollingStyle.value = {};
    
    // 确保当前动画完全结束后再计算 intervalTime
    nextTimer = setTimeout(() => {
      triggerBanner();
    }, banner.intervalTime * 1000); // 这里的 intervalTime 现在是在完整动画结束后才开始计时
    
  }, totalSec * 1000);
}

const checkOverflow = debounce(() => {
  const el = textRef.value;
  const parent = el?.parentElement;
  if (el && parent) {
    const elW = el.scrollWidth;
    const pW = parent.clientWidth;
    if (elW > pW) {
      const distance = elW - pW;
      const scrollSec = Math.max(distance / 50, 3);
      
      el.style.setProperty('--distance', `${distance}px`);
      scrollingStyle.value = {
        animation: `scrollText ${scrollSec}s linear forwards`,
        transform: 'translateX(0)',
        'transition': 'transform 0.3s ease-out'
      };
    }
  }
}, 200)

onUnmounted(() => {
  clearTimeout(mainTimer);
  clearTimeout(overflowTimer);
  clearTimeout(nextTimer);
});
</script>

<style lang="less">
@keyframes danmuSlide {
  0% {
    transform: translateX(100%);
  }
  25% {
    transform: translateX(0);
  }
  75% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes scrollText {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-1 * var(--distance)));
  }
}

/* scrollText uses inline style, var(--distance) is unused here */
</style>
