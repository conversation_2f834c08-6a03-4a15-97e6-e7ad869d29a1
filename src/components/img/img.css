.LazyLoad {
  width: 100%;
  /* opacity: 0.3; */
  /* transition: all 0.5s ease-in-out; */
  height: 100%;
  /* background-size: 100% 100%; */

  /* transform: translateZ(0); */
}
.LazyLoad_image {
  height: 100%;
  background: url('@/assets/image/placeholder.jpg') no-repeat 100% 100%;
  background-size: 100% 100%;
}

@keyframes popIn {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

/* 应用动画效果 */
.image {
  /* animation: popIn 0.3s ease-out; */
  opacity: 1;
  transform: scale(1);
}

.image-not-animation {
  opacity: 1;
}
