<template>
  <van-popup
    round
    v-model:show="hasNeedService"
    :style="{ width: '80%', backgroundColor: 'transparent' }"
  >
    <van-cell
      v-for="item in customerServiceUrl"
      :key="item.id"
      :title="item.nickName"
      title-class="van-ellipsis"
      :label="item.title"
      center
      is-link
      @click="goServer(item)"
    >
      <template #icon>
        <div className='w-10 h-10 mr-2 rounded-full overflow-hidden'>
          <ImgComponents :imgUrl="item.pic" />
        </div>
      </template>
    </van-cell>
  </van-popup>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { useAppStore } from "@/store/app";
import { useUserStore } from '@/store/user';

const router = useRouter();
const appStore = useAppStore();
const userStore = useUserStore();

const { customerServiceUrl, hasNeedService } = storeToRefs(appStore);

const { nickName, id, picUrl, vipId } = userStore


/**
 * 
 * @param type {0=其他,1=美洽} 
 * @param open_type {0=内跳,1=外跳} 
 */
const goServer = ({ type, url, open_type, nickName: title }) => {

  if (type === 0) {
    if (open_type === 0) {
      router.push({ path: '/webview', query: { url: encodeURIComponent(url) } })
    } else {
      window.dsBridge.call('app.openExternalApp', url);
    }
  } else {
    let separator = url.includes("?") ? "&" : "?";
    const meta = `CUSTOM!NAME=${nickName}&CUSTOM!USER_ID=${id}&visitorInfo=MKUserId:${id}MKUserName:${nickName}&visiter_id=${id}`
    const full_url = `${url}${separator}${meta}`;

    if (open_type === 0) {
      router.push({ path: '/webview', query: { url: encodeURIComponent(full_url) } })
    } else {
      window.dsBridge.call('app.openExternalApp', full_url);
    }
  }
  appStore.SetHasNeedServiceAction(false);
};

</script>