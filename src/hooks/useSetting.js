import { warn } from '@/utils/log'
import { getAppEnvConfig } from '@/utils/env'

export function useGlobSetting() {
  const {
    VITE_GLOB_APP_SHORT_NAME,
    VITE_GLOB_APP_BASE_API,
    VITE_GLOB_APP_GAME_API,
    VITE_GLOB_APP_FINANCE_API,
    VITE_GLOB_APP_CHAT_API,
    VITE_GLOB_APP_PROMOTION_API,
    VITE_GLOB_APP_PLAY_LICENSE_URL,
  } = getAppEnvConfig()

  if (!/[a-z_]*/i.test(VITE_GLOB_APP_SHORT_NAME)) {
    warn(
      `VITE_GLOB_APP_SHORT_NAME Variables can only be characters/underscores, please modify in the environment variables and re-running.`,
    )
  }

  // Take global configuration
  const glob = {
    shortName: VITE_GLOB_APP_SHORT_NAME,
    url_base: VITE_GLOB_APP_BASE_API,
    url_game: VITE_GLOB_APP_GAME_API,
    url_chat: VITE_GLOB_APP_CHAT_API,
    url_finance: VITE_GLOB_APP_FINANCE_API,
    url_promotion: VITE_GLOB_APP_PROMOTION_API,
    url_license: VITE_GLOB_APP_PLAY_LICENSE_URL,
  }
  return glob
}
