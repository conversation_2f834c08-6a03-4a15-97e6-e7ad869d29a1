export function useFullScreenVars() {
  const updateVars = () => {
    const isLandscape = window.innerWidth > window.innerHeight
    const vw = isLandscape ? window.screen.width / 100 : window.innerWidth / 100
    const vh = isLandscape ? window.screen.height / 100 : window.innerHeight / 100
    document.documentElement.style.setProperty('--vw', `${vw}px`)
    document.documentElement.style.setProperty('--vh', `${vh}px`)
  }

  onMounted(() => {
    updateVars()
    window.addEventListener('resize', updateVars)
    window.addEventListener('orientationchange', updateVars)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('resize', updateVars)
    window.removeEventListener('orientationchange', updateVars)
  })
}