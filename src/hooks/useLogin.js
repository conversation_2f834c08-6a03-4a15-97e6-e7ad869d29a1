import { uLogin } from '@/api/user';
import { useUserStore } from '@/store/user';
import fingerPrint from '@fingerprintjs/fingerprintjs';
import { setLocalStorage } from '@/utils';
import { PageEnum } from '@/enums/pageEnum';

export const useLogin = async (params) => {
  const isFinished = ref(false);
  const userStore = useUserStore();
  const isError = ref(false);
  const errorMessage = ref('');

  const { VITE_TOKEN_KEY, VITE_PLAY_USER_KEY } = import.meta.env;

  const version = __APP_VERSION__;

  let fingerInfo = await fingerPrint.load();
  let uuid = await fingerInfo.get();

  const deviceId = localStorage.getItem('deviceId') ? localStorage.getItem('deviceId') : uuid.visitorId;
  const inviteCode = sessionStorage.getItem('inviteCode');
  const toast = showLoadingToast({ duration: 0, forbidClick: true });

  try {
    const res = await uLogin({
      ...params,
      device: 2,
      uuid: deviceId,
      appVersion: version,
      sysVersion: window.__static_public_data__?.sysVersion,
      deviceName: window.__static_public_data__?.deviceName,
      inviteCode: window.__static_public_data__?.inviteCode
    });

    if (res.data === null) {
      isError.value = true;
      errorMessage.value = res.msg;
      return;
    }

    if (res.data.subchanelId === PageEnum.SUB_CHANNEL) {
      if (!localStorage.getItem('hemav_get_subchannel')) {
        window.dsBridge?.call('app.getSubChannelkey');
        localStorage.setItem('hemav_get_subchannel', '1')
      }
    } else {
      window.dsBridge?.call('app.zifyReportRegister');
    }

    isFinished.value = true;
    userStore.updateUserInfo(res.data);
    setLocalStorage(VITE_TOKEN_KEY, res.data.token);
    setLocalStorage(VITE_PLAY_USER_KEY, JSON.stringify(res.data));
  } catch (error) {
    isFinished.value = false;
    isError.value = true;
  } finally {
    toast.close();
  }

  return {
    isFinished: readonly(isFinished),
    isError: readonly(isError),
    errorMessage: readonly(errorMessage)
  };
};
