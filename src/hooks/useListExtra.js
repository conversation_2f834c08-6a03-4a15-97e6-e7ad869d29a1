import { get, noop } from 'lodash-es';
export const useListExtra = ({
  serverHandle = () => Promise,
  backupServerHandle = () => Promise,
  immediateParams = {},
  implementationGetParams = noop,
  implementationRefresh = noop,
  implementationFetched = noop,
  size = 20,
  pagination
}) => {
  pagination = pagination === undefined ? {} : pagination;
  const { page = 'page', limit = 'limit', data = 'data' } = pagination;
  const tableOptions = reactive({
    [page]: 1,
    [limit]: size
  });

  const states = reactive({
    list: [],
    isFetching: false,
    refreshing: false,
    loading: false,
    error: false,
    finished: false,
    finishedText: '--- 暂无更多内容 ---'
  });

  const paramsObj = computed(() => {
    return {
      ...implementationGetParams?.(),
      ...unref(immediateParams),
      ...unref(tableOptions)
    };
  });

  const useBackup = ref(false); // 新增状态

  const onLoad = async () => {
    try {
      let res;

      if (useBackup.value) {
        res = await backupServerHandle(unref(paramsObj))
      } else {
        res = await serverHandle(unref(paramsObj));
      }

      states.isFetching = true;
      states.refreshing = false;

      let list = get(res, data) || [];

      if (!useBackup.value && tableOptions[page] === 1 && list.length === 0) {
        useBackup.value = true;
        res = await backupServerHandle(unref(paramsObj));
        list = get(res, data) || [];
      }

      if (res) {
        if (tableOptions[page] === 1) {
          states.list = list;
        } else {
          states.list = states.list.concat(list);
        }

        states.loading = false;

        if (list.length < tableOptions[limit]) {
          states.finished = true;
        } else if (list.length === tableOptions[limit]) {
          states.finished = false;
          tableOptions[page] += 1;
        }

        implementationFetched?.(res.data);
      } else {
        states.refreshing = false;
        states.finished = true;
        states.loading = false;
      }
    } catch(error) {
      states.refreshing = false;
      states.finished = true;
      states.loading = false;
    }
  };

  const onRefresh = () => {
    states.finished = false;
    states.loading = true;
    tableOptions[page] = 1;
    implementationRefresh?.();
    onLoad();
  };

  return {
    ...toRefs(states),
    useBackup,
    onLoad,
    onRefresh
  };
};
