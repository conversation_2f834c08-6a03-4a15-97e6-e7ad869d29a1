import { isEmpty, get, noop } from 'lodash-es';
export const useList = ({
  serverHandle = () => Promise,
  immediateParams = {},
  implementationFetched = noop,
  limit = 20,
  pagination = { page: 'pageIndex', limit: 'pageSize', count: 'data.totleCount', list: 'data.list' }
}) => {
  const tableOptions = reactive({
    [pagination.page]: 1,
    [pagination.limit]: limit
  });

  const states = reactive({
    list: [],
    count: 0,
    executed: false,
    isFetching: false,
    refreshing: false,
    loading: false,
    error: false,
    finished: false,
    finishedText: '--- 暂无更多内容 ---'
  });

  const paramsObj = computed(() => {
    return {
      ...unref(immediateParams),
      ...unref(tableOptions)
    };
  });

  const onLoad = async () => {
    try {
      const res = await serverHandle(unref(paramsObj));
      states.isFetching = true;

      const list = get(res, pagination.list) || [];
      states.count = get(res, pagination.count) || 0;

      if (tableOptions[pagination.page] === 1) {
        states.list = list;
      } else {
        states.list = states.list.concat(list);
      }

      states.loading = false;

      if (list.length < tableOptions[pagination.limit]) {
        states.finished = true;
      } else if (list.length === tableOptions[pagination.limit]) {
        states.finished = false;
        tableOptions[pagination.page] += 1;
      }
      implementationFetched?.(res.data);
    } catch (e) {
      states.refreshing = false;
      states.finished = true;
      states.loading = false;
    } finally {
      states.refreshing = false;
    }
    
  };

  const onRefresh = () => {
    states.list = [];
    states.finished = false;
    states.loading = true;
    tableOptions[pagination.page] = 1;
    onLoad();
  };

  return {
    ...toRefs(states),
    onLoad,
    onRefresh
  };
};
