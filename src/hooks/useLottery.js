import { SetLotteryBetAPI } from '@/api/game';
import { useUserStore } from '@/store/user';
import { useVideoStore } from '@/store/video';
import { reducer, doubleSelect, mergeSelectes } from '@/utils/game';
import { storeToRefs } from 'pinia';

// Singleton shared state object that persists outside the hook function
const sharedState = {
  // Flag to track if the hook has been initialized
  initialized: false,

  // UI state
  show: ref(false),
  nap: ref(''),
  showNap: ref(false),
  currentGameId: ref(null),

  // Lottery betting state
  states: reactive({
    // 已下注目录
    selected: [],
    // 预选下注目录
    preselected: [],
    // 点击项
    select: {}
  }),

  // Reset method that properly resets values instead of recreating refs
  reset(game_id) {
    this.show.value = false;
    this.nap.value = '';
    this.showNap.value = false;
    this.currentGameId.value = game_id;

    // Reset reactive state properties
    this.states.selected = [];
    this.states.preselected = [];
    this.states.select = {};
  }
};

export function useLottery({ game_id }) {
  // Auto-reset if game_id has changed
  if (sharedState.currentGameId.value !== game_id) {
    sharedState.reset(game_id);
  }

  const refreshing = ref(false);

  // Use shared state instead of creating new refs
  const { show, nap, showNap, states } = sharedState;

  const userStore = useUserStore();
  const videoStore = useVideoStore();

  const { wallet } = storeToRefs(userStore);
  const { chip, chips, hasHelpKeep, keepLastIssuePreselected } = storeToRefs(videoStore);

  const displayChips = computed(() => {
    const tmp = [];

    videoStore.customChips.forEach((val) => {
      if (chips.value.includes(val.key)) {
        tmp.push(val);
      }
    });

    videoStore.normalChips.forEach((val) => {
      if (chips.value.includes(val.key)) {
        tmp.push(val);
      }
    });
    return tmp;
  });

  const displayChip = computed(() => displayChips.value?.find((o) => o.key === chip.value));

  const totalAmount = computed(() => {
    return states.preselected.reduce((p, t) => {
      // 获取每个玩法的总金额
      const bet_point = t.bet_data.map((o) => o.point).reduce(reducer, 0);

      p += bet_point;

      return p;
    }, 0);
  });

  const refreshHandle = async () => {
    try {
      refreshing.value = true;
      await userStore.updateUserWalletData();
    } finally {
      refreshing.value = false;
    }
  };

  const betCallHandle = (preselected) => {
    videoStore.setLastTimeBettings({ game_id: game_id, bet: preselected });
    refreshHandle();
  };

  const pickSelect = (point) => {
    const { type_id, how_id } = states.select;

    const tmp = { type_id, point };

    const _index = states.preselected.findIndex((o) => o.how_id === how_id);

    if (_index > -1) {
      const tt = states.preselected[_index].bet_data.findIndex((o) => o.type_id === type_id);

      if (tt > -1) {
        states.preselected[_index].bet_data[tt].point += point;
      } else {
        states.preselected[_index].bet_data.push(tmp);
      }
    } else {
      states.preselected.push({
        how_id,
        bet_data: [tmp]
      });
    }
  };

  const onSelect = ({ type_id, how_id, amount }) => {
    states.select = { type_id, how_id };

    // 如果用户金额小于1元最低投注
    if (parseFloat(wallet.value?.points) < 1) {
      show.value = true;
      return;
    }

    // 剩余可用金额
    const canPerselectPoint = parseInt(wallet.value?.points) - totalAmount.value;

    // 如果可用金额小于1
    if (canPerselectPoint < 1) {
      show.value = true;
      return;
    }

    const betAmount = amount || displayChip.value?.point;

    if (displayChip.value?.point === 'ALL') {
      pickSelect(canPerselectPoint);
      return;
    }

    if (canPerselectPoint < betAmount) {
      nap.value = canPerselectPoint;
      showNap.value = true;
      return;
    }

    pickSelect(betAmount);
  };

  // 2 bet
  const onMultiple = () => {
    if (states.preselected.length === 0) {
      return;
    }
    if (parseInt(wallet.value?.points) >= totalAmount.value * 2) {
      doubleSelect(states.preselected);
    } else {
      const remainingPoint = parseInt(wallet.value?.points) - totalAmount.value;
      states.preselected[0].bet_data[0].point = states.preselected[0].bet_data[0].point + remainingPoint;
    }
  };

  // 取消按钮
  const onCancel = () => {
    if (states.preselected.length === 0) {
      return;
    }
    states.preselected = [];
  };
  const onSubmit = async (period) => {
    if (states.preselected.length === 0) {
      return;
    }
    const toast = showLoadingToast();
    try {
      const res = await SetLotteryBetAPI({
        id: period,
        game_id,
        points: totalAmount.value,
        bet: states.preselected
      });

      if (res) {
        states.select = {};
        mergeSelectes(states.selected, states.preselected);
        betCallHandle?.(states.preselected);
        states.preselected = [];
        showToast({
          position: 'top',
          message: '下注成功'
        });
      }
    } catch (e) {
      console.error('onSubmit error', e);
      toast.close();
    }
  };

  return {
    ...toRefs(states),
    refreshing,
    show,
    nap,
    showNap,
    displayChips,
    pickSelect,
    onSelect,
    onCancel,
    onSubmit,
    onMultiple
  };
}
