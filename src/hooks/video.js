// import TCPlayer from 'tcplayer.js';
// import 'tcplayer.js/dist/tcplayer.min.css';

import { generatepsign } from '@/utils/psign';
import { getLocalStorage } from '@/utils';
export default class ReactiveVideo {
  constructor({ el, controlBar, autoplay, play, pause, progress, currentTime, ended, experEnded, loadeddata }) {
    this.el = el;
    this.player = null;
    this.autoplay = autoplay || false;
    this.loged = true;
    this.current = 0;
    this.duration = 1;
    this.controlBar = controlBar || false;
    this.options = {
      play: play,
      pause: pause,
      progress: progress,
      currentTime: currentTime,
      ended: ended,
      experEnded: experEnded,
      loadeddata: loadeddata
    };

    this.state = {
      play: false,
      pause: false,
      loaded: false
    };

    // this.checkLoged();

    this.setCurrentTime = this.setCurrentTime.bind(this);
  }

  add(fileID) {
    return {
      fileID,
      appID: import.meta.env.VITE_APP_PLAY_APP_ID,
      psign: generatepsign(fileID)
    };
  }

  checkLoged() {
    const token = getLocalStorage(import.meta.env.VITE_TOKEN_KEY);
    this.loged = !!token;
    if (this.loged) {
      this.state.expered = false;
    }

    return this.loged;
  }

  setCurrentTime(time, flag = true) {
    if (this.state.pause) return;

    let _time = flag ? this.current + time : time;

    if (_time < 0) _time = 0;
    if (_time > this.duration) _time = this.duration - 1;

    this.player?.currentTime(_time);
  }
  /**
   * @param {boolean} isCloudMode 是否通过fileID云点播
   * @param {string} fileID 点播媒体文件的 ID
   * @param {*} domain 播放时使用的域名
   * @param {string} cloudUrl 播放地址
   */

  init(isCloudMode, fileID, domain, cloudUrl) {
    const opts = {
      licenseUrl: import.meta.env.VITE_APP_PLAY_LICENSE_URL,
      bigPlayButton: false,
      autoplay: this.autoplay,
      controlBar: this.controlBar
    };

    if (isCloudMode) {
      opts.fileID = fileID;
      opts.appID = import.meta.env.VITE_APP_PLAY_APP_ID;
      opts.psign = generatepsign(fileID, domain);
    } else {
      opts.sources = [{ src: cloudUrl }];
    }

    this.player = window.TCPlayer(unref(this.el), opts);

    this.player.on('loadedmetadata', () => {
      this.autoplay && this.player?.play();
      this.duration = this.player.duration();
    });

    this.player.on('loadeddata', () => {
      this.state.loaded = true;
      this.options.loadeddata?.(true);
    });

    this.player.on('play', () => {
      this.state.play = true;
      this.state.pause = false;
      this.options.play?.();
    });

    this.player.on('pause', () => {
      this.state.play = false;
      this.state.pause = true;
      this.options.pause?.();
    });

    this.player.on('ended', () => {
      this.options.ended?.();
    });

    // this.player.on('durationchange', () => {
    //   console.log('durationchange')
    // });

    this.player.on('seeking', () => {
      console.log('one time');
      // this.player.currentTime(Math.ceil(this.player.currentTime()));
    });

    this.player.on('timeupdate', () => {
      const current = this.player.currentTime();
      this.current = current;
      this.options.currentTime?.(current);
      this.options.progress?.({
        current: current,
        duration: this.duration
      });
    });

    this.player.on('error', () => {
      console.log('error');
      // this.player?.pause()
    });

    return this.player;
  }
}
