import { useAppStore } from '@/store/app';
import { uGetServer } from '@/api/home';

export const useService = () => {
  const appStore = useAppStore();
  const toast = showLoadingToast({ duration: 0 });
  uGetServer()
    .then((res) => {
      appStore.SetHasNeedServiceAction(true);
      appStore.SetCustomerServiceUrlAction(res.data.server);
    })
    .finally(() => {
      toast.close();
    });
};
