import { GetTicketAPI } from '@/api/game';
import { isOfficialLottery } from '@/utils/game';
import { useTimeoutFn } from '@/hooks';


export function useLotteryClaimant({ game_id, explainCallHandle, stopCallHandle, implementationFetched }) {
  const isStop = ref(false);
  const closedTime = ref(5);
  const time = ref(5);
  const loading = ref(false);
  const gameInfo = ref({});
  const lastPeriod = ref({});
  const period = ref(null);
  const next = ref(false);
  let refreshCount = 0;

  // Handle reactive game_id - get the current value
  const getCurrentGameId = () => {
    if (typeof game_id === 'object' && game_id.value !== undefined) {
      return game_id.value;
    }
    return typeof game_id === 'function' ? game_id.value : game_id;
  };

  const TimeClaimant = (val) => {
    const currentId = getCurrentGameId();
    if (isOfficialLottery(currentId)) {
      closedTime.value = val.end_second;
    } else {
      closedTime.value = val.standard_second - val.end_second;
    }
    const t = val.end_time - val.server_time;
    if (t > 0) {
      isStop.value = false;
      time.value = t;
    } else if (t === 0) {
      isStop.value = false;
      time.value = 1;
    } else {
      isStop.value = true;
      if (parseInt(currentId) <= 9) {
        time.value = Math.abs(t);
      } else {
        time.value = val.end_second + val.end_time - val.server_time;
      }
    }
  };

  const claimant = async () => {
    try {
      const currentId = getCurrentGameId();

      if (!currentId) {
        console.error('Cannot call GetTicketAPI: game_id is missing or invalid');
        return;
      }

      loading.value = true;
      const { data: { data: Data } } = await GetTicketAPI({ game_id: currentId });
      const { game_info, last_period, ...rest } = Data;
      gameInfo.value = game_info;
      lastPeriod.value = last_period;
      explainCallHandle?.(game_info?.info);
      TimeClaimant(rest);

      const period_next = rest.id;
      if (isOfficialLottery(currentId)) {
        period.value = period_next;
        loading.value = false;
        if (!last_period?.res) {
          if (!isStop.value) {
            start(30 * 1e3);
          }
        } else {
          stop();
          implementationFetched?.(last_period);
        }
      } else {
        if (period_next === period.value) {
          refreshCount += 1;
          start(refreshCount <= 3 ? refreshCount * 1e3 : 5 * 1e3);
        } else {
          refreshCount = 0;
          period.value = period_next;
          loading.value = false;
          next.value = false;
          implementationFetched?.(last_period);
        }
      }
    } catch (error) {
      loading.value = false;
    }
  };

  const { start, stop } = useTimeoutFn(claimant, {
    immediate: false
  });

  // Watch for game_id changes and automatically call claimant
  // if (typeof game_id === 'object' && game_id.value !== undefined) {
  //   // It's a reactive ref/computed
  //   watch(game_id, (newGameId, oldGameId) => {
  //     if (newGameId && newGameId !== oldGameId) {
  //       claimant();
  //     }
  //   }, { immediate: false });
  // }

  return {
    isStop,
    closedTime,
    time,
    loading,
    gameInfo,
    lastPeriod,
    period,
    next,
    claimant,
  };
};