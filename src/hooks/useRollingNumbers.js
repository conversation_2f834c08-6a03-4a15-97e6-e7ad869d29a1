/**
 *
 * @param {Object} options
 * @param {String} resultStr 形如 "1-2-3"
 * @param {Boolean} animated 是否动画
 * @param {Number} count 数字个数
 * @param {Number} min 最小值（默认1）
 * @param {Number} max 最大值（默认6）
 * @returns {Ref<Array<String>>} 当前显示的数字数组
 */
export const useRollingNumbers = ({
  resultStr,
  animated,
  count,
  min = 1,
  max = 6,
  parseResult = (val) => String(val).split("-"),
}) => {
  const getRows = () => parseResult(unref(resultStr));
  const displayRows = ref(getRows());

  let timer = null;

  const startAnimation = () => {
    if (timer) clearInterval(timer);
    timer = setInterval(() => {
      displayRows.value = Array(count)
        .fill(0)
        .map(() => String(Math.floor(Math.random() * (max - min + 1)) + min));
    }, 100);
  };

  const stopAnimation = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
    displayRows.value = getRows();
  };

  watch(
    [animated, resultStr],
    ([ani, res]) => {
      if (ani) {
        startAnimation();
      } else {
        stopAnimation();
      }
    },
    { immediate: true }
  );

  return displayRows;
};
