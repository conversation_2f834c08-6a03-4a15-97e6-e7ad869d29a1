import { datafluxRum } from '@cloudcare/browser-rum';
import { getAppEnvConfig } from '@/utils/env'
import pkg from '../package.json';


const reportWebVitals = () => {

  const {
    VITE_GLOB_APP_BASE_API,
    VITE_GLOB_APP_GAME_API,
    VITE_GLOB_APP_FINANCE_API,
    VITE_GLOB_APP_CHAT_API,
    VITE_GLOB_APP_PROMOTION_API,
  } = getAppEnvConfig();

  let env = '';
  switch (import.meta.env.MODE) {
    case 'development':
      env = 'local'
      break;
    case 'release':
      env = 'prod';
      break;
    case 'develop':
      env = 'pre';
      break;
    default:
      env = 'local';
      break;
  }

  datafluxRum.init({
    applicationId: 'hem_com',
    // datakitOrigin: '<DataKit domain or IP>', // Needs configuration when integrating through DK
    clientToken: '4193a7b6f89144bea324ae125cefb426', // Fill in when accessing publicly via OpenWay
    site: 'https://id1-rum-openway.truewatch.com', // Fill in when accessing publicly via OpenWay
    env: env,
    version: pkg.version,
    sessionSampleRate: 100,
    sessionReplaySampleRate: 70,
    trackInteractions: true,
    traceType: 'ddtrace', // Optional, default is ddtrace, currently supports 6 types: ddtrace, zipkin, skywalking_v3, jaeger, zipkin_single_header, w3c_traceparent
    allowedTracingOrigins: [VITE_GLOB_APP_BASE_API, VITE_GLOB_APP_GAME_API, VITE_GLOB_APP_FINANCE_API, VITE_GLOB_APP_CHAT_API, VITE_GLOB_APP_PROMOTION_API],  // Optional, allows all requests to inject trace collector headers. Can be request origin or regex
  })
}

export default reportWebVitals
