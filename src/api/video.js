import useAxiosApi from '@/utils/useAxiosApi'

/**
 * 搜索关键词查询
 * @returns UseAxiosReturn
 */
export const getSearchKey = (data = {}) => {
  return useAxiosApi('/keywordConfig/search', { data })
}

/**
 * 关键词点击增加次数
 * @returns UseAxiosReturn
 */
export const increaseClickTimes = (data) => {
  return useAxiosApi('/keywordConfig/increaseClickTimes', { data })
}

/**
 * 视频库-标签列表
 * @returns UseAxiosReturn
 */
export const GetTagListApi = () => {
  return useAxiosApi('/video/tagList')
}

/**
 * 为你精选/猜你喜欢
 * @returns UseAxiosReturn
 */
export const GetVideoRecommendApi = (data) => {
  return useAxiosApi('/video/recommend', { data })
}

/**
 * 标签关联视频
 * @returns UseAxiosReturn
 */
export const GetVideoTagApi = (data) => {
  return useAxiosApi('/video/tagVideo', { data })
}

/**
 * 视频库-标签搜索
 * @returns UseAxiosReturn
 */
export const labelSearch = (data) => {
  return useAxiosApi('/video/labelSearch', { data })
}


/**
 * 是否收藏
 * @returns UseAxiosReturn
 */
export const uIsCollectVideo = (data) => {
  return useAxiosApi('/video/isVideoCollected', { data })
}

/**
 * 视频详情
 * @returns UseAxiosReturn
 */
export const videoDetail = (data) => {
  return useAxiosApi('/common/videoDetail', { data })
}

/**
 * 主题查询
 * @returns UseAxiosReturn
 */
export const editedVideoForOneTheme = (data) => {
  return useAxiosApi('/common/editedVideoForOneTheme', { data })
}

/**
 * 短视频视频查询
 * @returns UseAxiosReturn
 */
export const shortVideoList = (data) => {
  return useAxiosApi('/common/shortVideoList', { data })
}

/**
 * 视频统计信息
 * @returns UseAxiosReturn
 */
export const VideoCountInfo = (data) => {
  return useAxiosApi('/common/videoCountInfo', { data })
}