import { createServiceExtra } from '@/utils/useAxiosApi';
const useAxiosApi = createServiceExtra('url_promotion');

/**
 * 活动类别
 * @returns UseAxiosReturn
 */
export const listActivityType = () => {
  return useAxiosApi('/promotion/activity/getAllActivityType', { method: 'POST' })
}

/**
 * 活动列表
 * @returns UseAxiosReturn
 */
export const listActivity = (data) => {
  return useAxiosApi('/promotion/activity/getActivityList', { data, method: 'POST' })
}

/**
 * 活动详情
 * @returns UseAxiosReturn
 */
export const listActivityDetail = (data) => {
  return useAxiosApi('/promotion/activity/activityDetail', { data, method: 'POST' })
}

/**
 * 任务列表
 * @returns UseAxiosReturn
 */
export const listTask = (data) => {
  return useAxiosApi('/promotion/activity/manualJobList', { data, method: 'POST' })
}

/**
 * 任务列表明细
 * @returns UseAxiosReturn
 */
export const listSubTask = (data) => {
  return useAxiosApi('/promotion/activity/subTaskList', { data, method: 'POST' })
}

/**
 * 任务领取
 * @returns UseAxiosReturn
 */
export const claimRewards = (data) => {
  return useAxiosApi('/promotion/activity/claimRewards', { data, method: 'POST' })
}

/**
 * 任务领取全部
 * @returns UseAxiosReturn
 */
export const claimAll = (data) => {
  return useAxiosApi('/promotion/activity/claimAll', { data, method: 'POST' })
}

/**
 * 领取记录
 * @returns UseAxiosReturn
 */
export const claimRewardsRecord = (data) => {
  return useAxiosApi('/promotion/activity/claimRewardsRecord', { data, method: 'POST' })
}
