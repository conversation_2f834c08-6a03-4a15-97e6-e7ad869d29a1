import { createServiceExtra } from "@/utils/useAxiosApi";

const useAxiosApi = createServiceExtra('url_game');

/**
 * 用户钱包
 * @returns UseAxiosReturn
 */
export const GetWalletAPI = () => {
  return useAxiosApi("/api/user/money/info", { method: "GET" });
};


/**
 * 回调三方余额
 * @returns UseAxiosReturn
 */
export const ReviceWalletAPI = () => {
  return useAxiosApi("/api/user/my/retrieveAllThirdMoney", { method: "POST" });
};

/**
 * 游戏分类列表
 * @returns UseAxiosReturn
 */
export const GetGameCategoryAPI = () => {
  return useAxiosApi("/api/public/game/all_category", { method: "GET" });
};

/**
 * 最近游戏
 * @returns UseAxiosReturn
 */
export const GetRecentPlayAPI = () => {
  return useAxiosApi("/api/user/recentPlay/gameList", { method: "POST" });
};

/**
 * 游戏小分类列表
 * @returns UseAxiosReturn
 */
export const GetGameCategoryDetailAPI = (params) => {
  return useAxiosApi("/api/public/game/category/detail", { method: "GET", params });
};

/**
 * 三方游戏的link
 * @returns UseAxiosReturn
 */
export const GetGameTokenlinkAPI = (data) => {
  return useAxiosApi("/api/public/game/token_link", { method: "POST", data });
};

/**
 * PG三方游戏的link
 * @returns UseAxiosReturn
 */
export const GetPGTokenlinkAPI = (data) => {
  return useAxiosApi("/api/pg/game/token_link", { method: "POST", data });
};

/**
 * 游戏记录
 * @returns UseAxiosReturn
 */
export const GetGameRecordAPI = (params) => {
  return useAxiosApi("/api/user/game/record", { method: "GET", params });
};

/**
 * 平台的游戏记录
 * @returns UseAxiosReturn
 */
export const GetGameRecordDetailAPI = (params) => {
  return useAxiosApi("/api/user/game/detail_record", { method: "GET", params });
};


/**
 * 播放页POPUP
 * @returns UseAxiosReturn
 */
export const GetGameVideoCategoryAPI = (params) => {
  return useAxiosApi("/api/public/game/live_category", { method: "GET", params });
};

/**
 * 播放页4个彩种
 * @returns UseAxiosReturn
 */
export const GetGameVideoCategoryNEWAPI = (params) => {
  return useAxiosApi("/api/public/game/live_category_new", { method: "GET", params });
};

/**
 * Lottery list API
 * @returns UseAxiosReturn
 */
export const GetLotteryCategoryAPI = () => {
  return useAxiosApi("/api/public/game/os_game", { method: "GET" });
};

/**
 * 下注API
 * @returns UseAxiosReturn
 */
export const SetLotteryBetAPI = (data) => {
  return useAxiosApi("/api/user/game/ticket/play", { method: "POST", data });
};

/**
 * 配置数据
 * @returns UseAxiosReturn
 */
export const TicketClassAPI = (params) => {
  return useAxiosApi("/api/public/game/ticket/detail", { method: "GET", params });
};

/**
 * 开奖详情数据
 * @returns UseAxiosReturn
 */
export const GetTicketAPI = (data) => {
  return useAxiosApi("/api/user/game/ticket/detail", { method: "POST", data });
};

/**
 * 开奖列表
 * @returns UseAxiosReturn
 */
export const GetTicketRecordAPI = (params) => {
  return useAxiosApi("/api/public/game/history_record", { method: "GET", params });
};

/**
 * 跟单计划投注数据
 * @param {Object} data - 跟单投注数据
 * @param {number} data.gameId - 游戏ID
 * @param {string} data.playWayCode - {DanSuang}
 * @param {string} [data.pageSortKey] - 分页排序键
 * @returns UseAxiosReturn
 */
export const FollowPlanBetDataAPI = (data) => {
  return useAxiosApi("/api/public/game/followPlanBetData", { method: "POST", data });
};

/**
 * 跟单计划配置
 * @param {Object} data - 跟单配置数据
 * @param {number} data.gameId - 游戏ID
 * @returns UseAxiosReturn
 */
export const FollowPlanWaysConfigAPI = (data) => {
  return useAxiosApi("/api/public/game/followPlanWaysConfig", { method: "POST", data });
};

/**
 * 下注记录
 * @returns UseAxiosReturn
 */
export const GetLotteryBetRecordAPI = (params) => {
  return useAxiosApi("/api/user/my/play_cord", { method: "GET", params });
};

/**
 * 彩金记录
 * @returns UseAxiosReturn
 */
export const GetBonusAPI = (params) => {
  return useAxiosApi("/api/user/my/detail_bonus", { method: "GET", params });
};
