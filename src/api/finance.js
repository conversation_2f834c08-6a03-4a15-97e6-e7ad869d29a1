import { createServiceExtra } from '@/utils/useAxiosApi'


const useAxiosApi = createServiceExtra('url_finance');


/**
 * 充值类型列表获取
 * @returns UseAxiosReturn
 */
export const GetRechargeTypeList = () => {
  return useAxiosApi("/finance/recharge/rechargeTypeList", { method: "POST" });
};

/**
 * 充值类型详情
 * @returns UseAxiosReturn
 */
export const GetRechargeConfigList = (data) => {
  return useAxiosApi("/finance/recharge/rechargeConfigList", { method: "POST", data });
};

/**
 * 快速入款
 * @returns UseAxiosReturn
 */
export const GetRechargeCollectionList = (data) => {
  return useAxiosApi("/finance/recharge/getQuickBankList", { method: "POST", data });
};

/**
 * 人工代充
 * @returns UseAxiosReturn
 */
export const GetCustomerList = (data) => {
  return useAxiosApi("/finance/recharge/customerServiceList", { method: "POST", data });
};

/**
 * 创建订单
 * @returns UseAxiosReturn
 */
export const CreayePayment = (data) => {
  return useAxiosApi("/finance/recharge/paymentCreate", { method: "POST", data });
};

/**
 * 创建快速订单
 * @returns UseAxiosReturn
 */
export const CreayeQuickPayment = (data) => {
  return useAxiosApi("/finance/recharge/quickPayCreate", { method: "POST", data });
};

/**
 * 充值记录
 * @returns UseAxiosReturn
 */
export const GetRechargeRecordAPI = (data) => {
  return useAxiosApi("/finance/recharge/myRechargeLog", { method: "POST", data });
};


/**
 * 获取钱包类型
 * @returns UseAxiosReturn
 */
export const GetWalletTypeAPI = (data) => {
  return useAxiosApi("/finance/withdraw/walletTypeList", { method: "POST", data });
};

/**
 * 绑定钱包
 * @returns UseAxiosReturn
 */
export const BindWalletAPI = (data) => {
  return useAxiosApi("/finance/withdraw/bindWallet", { method: "POST", data });
};

/**
 * 获取提现方式
 * @returns UseAxiosReturn
 */
export const GetWithdrawTypeList = () => {
  return useAxiosApi("/finance/withdraw/withdrawTypeList", { method: "POST" });
};

/**
 * 获取绑定银行卡
 * @returns UseAxiosReturn
 */
export const GetWithdrawBankList = () => {
  return useAxiosApi("/finance/withdraw/myBankCardList", { method: "POST" });
};

/**
 * 获取银行列表
 * @returns UseAxiosReturn
 */
export const GetBankList = () => {
  return useAxiosApi("/finance/withdraw/bankList", { method: "POST" });
};

/**
 * 绑定银行卡
 * @returns UseAxiosReturn
 */
export const BindBankCard = (data) => {
  return useAxiosApi("/finance/withdraw/bindCard", { method: "POST", data });
};

/**
 * 提现
 * @returns UseAxiosReturn
 */
export const CreateWithdrawAPI = (data) => {
  return useAxiosApi("/finance/withdraw/withdrawCreate", { method: "POST", data });
};

/**
 * 提现记录
 * @returns UseAxiosReturn
 */
export const GetWithdrawLogList = (data) => {
  return useAxiosApi("/finance/withdraw/myWithdrawLog", { method: "POST", data });
};

/**
 * 提现详情
 * @returns UseAxiosReturn
 */
export const GetWithdrawDetailAPI = (data) => {
  return useAxiosApi("/finance/withdraw/detail", { method: "POST", data });
};
