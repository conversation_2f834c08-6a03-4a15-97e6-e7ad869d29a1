import useAxiosApi from '@/utils/useAxiosApi'

/**
 * 登录
 * @returns UseAxiosReturn
 */
export const uLogin = (data) => {
  return useAxiosApi('/user/uLogin', { data, needToken: false })
}

/**
 * 注册
 * @returns UseAxiosReturn
 */
export const uRegister = (data) => {
  return useAxiosApi('/user/uRegister', { data, needToken: false })
}

/**
 * ws链接TOKEN
 * @returns UseAxiosReturn
 */
export const GetWSToken = (data) => {
  return useAxiosApi('/user/wsToken', { data })
}



/**
 * 上传
 * @returns UseAxiosReturn
 */
export const picUpload = (data) => {
  return useAxiosApi('/common/picUpload', { data })
}

/**
 * 心跳
 * @returns UseAxiosReturn
 */
export const CheckHeartBeat = () => {
  return useAxiosApi('/user/heartBeat')
}

/**
 * 验证码
 * @returns UseAxiosReturn
 */
export const uGetTelCode = (data) => {
  return useAxiosApi('/user/uGetTelCode', { data })
}

/**
 * 用户详情
 * @returns UseAxiosReturn
 */
export const PersonInfoDataAPI = () => {
  return useAxiosApi('/user/detailInfo')
}


/**
 * 用户主题
 * @returns UseAxiosReturn
 */
export const SetupPassword = (data) => {
  return useAxiosApi('/user/pwdReset', { data })
}

/**
 * 更新用户详情
 * @returns UseAxiosReturn
 */
export const UpdateInfoDataAPI = (data) => {
  return useAxiosApi('/user/infoUpdate', { data })
}

/**
 * 绑定手机号
 * @returns UseAxiosReturn
 */
export const uBindingTel = (data) => {
  return useAxiosApi('/user/uBindingTel', { data })
}

/**
 * 用户行为
 * @returns UseAxiosReturn
 */
export const uActions = (data) => {
  return useAxiosApi('/user/uActions', { data, hiddenMid: true })
}

/**
 * 游客浏览记录
 * @returns UseAxiosReturn
 */
export const uVistor = (data) => {
  return useAxiosApi('/user/uVistor', { data })
}

/**
 * 长视频记录
 * @returns UseAxiosReturn
 */
export const uWatchLongVideoHistory = (data) => {
  return useAxiosApi('/user/uWatchLongVideoHistory', { data })
}

/**
 * 长视频记录
 * @returns UseAxiosReturn
 */
export const uWatchShortVideoHistory = (data) => {
  return useAxiosApi('/user/uWatchShortVideoHistory', { data })
}

/**
 * 收藏记录
 * @returns UseAxiosReturn
 */
export const uCollectVideoHistory = (data) => {
  return useAxiosApi('/user/uCollectVideoHistory', { data })
}

/**
 * 设置支付密码
 * @returns UseAxiosReturn
 */
export const setPayPwd = (data = {}) => {
  return useAxiosApi('/user/setPayPwd', { data })
}

/**
 * 重置支付密码
 * @returns UseAxiosReturn
 */
export const resetPayPwd = (data = {}) => {
  return useAxiosApi('/user/resetPayPwd', { data })
}

/**
 * 验证支付密码
 * @returns UseAxiosReturn
 */
export const verifyPayPwd = (data = {}) => {
  return useAxiosApi('/user/verifyPayPwd', { data })
}

/**
 * 找回支付密码
 * @returns UseAxiosReturn
 */
export const recoverPayPwd = (data = {}) => {
  return useAxiosApi('/user/recoverPayPwd', { data })
}

/**
 * 邀请好友：推广地址
 * @returns UseAxiosReturn
 */
export const userPromotion = (data = {}) => {
  return useAxiosApi('/user/promotion', { data })
}

/**
 * 邀请好友：邀请好友记录
 * @returns UseAxiosReturn
 */
export const inviteList = (data = {}) => {
  return useAxiosApi('/user/inviteList', { data })
}

/**
 * 意见反馈
 * @returns UseAxiosReturn
 */
export const sendFeedback = (data = {}) => {
  return useAxiosApi('/feedBack/submit', { data })
}

/**
 * VIP列表
 * @returns UseAxiosReturn
 */
export const VIPListAPI = (data = {}) => {
  return useAxiosApi('/user/vipList', { data })
}

/**
 * 领取VIP奖励
 * @returns UseAxiosReturn
 */
export const RewardVipAPI = (data = {}) => {
  return useAxiosApi('/user/vipGetReward', { data })
}

/**
 * VIP奖励记录
 * @returns UseAxiosReturn
 */
export const RecordVipAPI = (data = {}) => {
  return useAxiosApi('/user/vipRecord', { data })
}


/**
 * 用户主题
 * @returns UseAxiosReturn
 */
export const ThemesAPI = (data = {}) => {
  return useAxiosApi('/user/themes', { data })
}

/**
 * 用户主题
 * @returns UseAxiosReturn
 */
export const SaveThemesAPI = (data = {}) => {
  return useAxiosApi('/user/saveTheme', { data })
}