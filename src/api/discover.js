import useAxiosApi from '@/utils/useAxiosApi'

/**
 * 搜索关键词查询
 * @returns UseAxiosReturn
 */
export const SearchKeyAPI = (data = {}) => {
  return useAxiosApi('/keywordConfig/search', { data })
}

/**
 * 关键词点击增加次数
 * @returns UseAxiosReturn
 */
export const increaseClickTimes = (data) => {
  return useAxiosApi('/keywordConfig/increaseClickTimes', { data })
}

/**
 * 搜索模糊
 * @returns UseAxiosReturn
 */
export const blurSearch = (data) => {
  return useAxiosApi('/keyword/blurSearch', { data })
}