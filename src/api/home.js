import useAxiosApi from '@/utils/useAxiosApi'

/**
 * 应用列表
 * @returns UseAxiosReturn
 */
export const NavigationCenterAPI = (data = {}) => {
  return useAxiosApi('/app/center', { data })
}

/**
 * 应用推荐列表
 * @returns UseAxiosReturn
 */
export const NavigationRecommendAPI = (data = {}) => {
  return useAxiosApi('/app/recommend', { data })
}

/**
 * 飘窗数据
 * @returns UseAxiosReturn
 */
export const BaywindowAPI = (data = {}) => {
  return useAxiosApi('/app/baywindow', { data })
}

/**
 * 获取视频分类列表
 * @returns UseAxiosReturn
 */
export const NavigationBarAPI = (data = {}) => {
  return useAxiosApi('/video/catList', { data })
}

/**
 * 配置数据
 * @returns UseAxiosReturn
 */
export const AppConfigAPI = () => {
  return useAxiosApi('/common/sysBaseConfigData', { method: 'POST' })
}

/**
 * 客服地址
 * @returns UseAxiosReturn
 */
export const uGetServer = () => {
  return useAxiosApi('/sys/uGetServer')
}

/**
 * 推荐视频
 * @returns UseAxiosReturn
 */
export const recommendVideoList = (data) => {
  return useAxiosApi('/common/recommendVideoList', { data })
}
/**
 * 视频查询
 * @returns UseAxiosReturn
 */
export const editedVideoList = (data) => {
  return useAxiosApi('/common/editedVideoList', { data })
}

/**
 * 主题查询
 * @returns UseAxiosReturn
 */
export const editedVideoForOneTheme = (data) => {
  return useAxiosApi('/common/editedVideoForOneTheme', { data })
}

/**
 * 初始化
 * @returns UseAxiosReturn
 */
export const appFirstPageInitData = (data = {}) => {
  return useAxiosApi('/common/appFirstPageInitData', { data })
}

/**
 * 获取所有广告
 * @returns UseAxiosReturn
 */
export const GetAdListAPI = (data = {}) => {
  return useAxiosApi('/sys/getAllAds', { data })
}

/**
 * 主题广告
 * @returns UseAxiosReturn
 */
export const uGetActivity = (data = {}) => {
  return useAxiosApi('/sys/uGetActivity', { data })
}