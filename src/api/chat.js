import { createServiceExtra } from '@/utils/useAxiosApi';
const useAxiosApi = createServiceExtra('url_chat');

/**
 * 最近消息
 * @param {Object} data
 * @param {number} data.gameId - 游戏ID
 * @param {number} data.number - 消息数量
 * @returns UseAxiosReturn
 */
export const GetRecentMsg = (data) => {
  return useAxiosApi('/chat/getRecentMsg', { data, method: 'POST' })
}

/**
 * 获取群公告
 * @returns UseAxiosReturn
 */
export const GetGroupNotice = () => {
  return useAxiosApi('/chat/getNotice', { method: 'POST' })
}

/**
 * 发送消息
 * @param {Object} data - Message data
 * @param {number} data.gameId - 游戏ID
 * @param {string} data.content - 消息内容
 * @param {number} data.type - 0 is normal text, 1 is barrage text (barrage messages are displayed with animation effects and must pay points to send).
 * @returns UseAxiosReturn
 */
export const SendMessage = (data) => {
  return useAxiosApi('/chat/send_msg', { data, method: 'POST'})
}