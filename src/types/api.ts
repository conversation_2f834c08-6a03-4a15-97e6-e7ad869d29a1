/**
 * API Response wrapper interface
 */
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data?: T;
}

/**
 * Follow Plan Ways Configuration Query Request Interface
 * Used for fetching available configuration options
 */
export interface FollowPlanWaysConfigQueryRequest {
  /** 游戏ID */
  gameId: number;
}

/**
 * Follow Type Option Interface
 */
export interface FollowTypeOption {
  code: string;
  name: string;
}

/**
 * Bet Type Option Interface
 */
export interface BetTypeOption {
  /** 选项值 */
  value: string;
  /** 显示标签 */
  label: string;
  /** 描述 */
  description?: string;
  /** 是否可用 */
  enabled: boolean;
  /** 分类 */
  category?: string;
}

/**
 * Follow Plan Ways Configuration Response Interface
 */
export interface FollowPlanWaysConfigResponse {
  /** 配置ID */
  config_id?: string;
  /** 游戏ID */
  game_id: number;
  /** 跟单类型 */
  follow_type: 'auto' | 'manual';
  /** 最大投注金额 */
  max_bet_amount: number;
  /** 最小投注金额 */
  min_bet_amount: number;
  /** 允许的投注类型数组 */
  bet_types: string[];
  /** 是否自动跟单 */
  auto_follow: boolean;
  /** 跟单比例 */
  follow_ratio: number;
  /** 配置状态 */
  status?: 'active' | 'inactive';
  /** 创建时间 */
  created_at?: string;
  /** 更新时间 */
  updated_at?: string;
}

/**
 * Follow Plan Bet Data Request Interface
 */
export interface FollowPlanBetDataRequest {
  /** 游戏ID */
  gameId: number;
  /** 玩法代码 */
  playWayCode: string;
  /** 分页排序键 */
  pageSortKey?: string;
}

/**
 * Follow Plan Bet Data Response Interface
 */
export interface FollowPlanBetDataResponse {
  /** 投注记录列表 */
  lists: FollowPlanBetItem[];
  /** 总数量 */
  latestOpen: FollowPlanBetItem;
}

/**
 * Follow Plan Bet Item Interface
 */
export interface FollowPlanBetItem {
  gameDetailId: number;
  gameTypeId: number;
  gameTypeName: string;
  gameHowId: number;
  period: string;
  periodShort: string;
  selfDefinePlayWayCode: string;
  selfDefinePlayWayName: string;
  result: string;
  point: number;
}

/**
 * Error response interface
 */
export interface ApiError {
  code: number;
  msg: string;
  details?: any;
}
