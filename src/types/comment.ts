/**
 * Comment Type Definitions for Lottery Popup Comment System
 */

export type UserWsMessage = {
  client_id: string;
  data: {
    content: string;
    isRoomManager: number;
    rid: number;
    room_id: number;
    send_time: string;
    skin: boolean;
    tenant_id: number;
    type: number;
    uid: number;
    user_id: number;
    user_info: {
      avatar: string;
      isnoble: boolean;
      level: number;
      levelIcon: string;
      nickname: string;
      sex: number;
      sex_text: string;
      user_id: number;
    };
  };
  event_type: number;
  uuid: string;
};

export type BettingWsMessage = {
  client_id: string;
  data: {
    code: number;
    data: {
      anchor_id: number;
      bet: {
        bet_data: { name: string; odd: number; point: number; type_id: number }[];
        how_id: number;
        name: string;
      }[];
      game_id: number;
      game_name: string;
      last_period: any;
      liveId: string;
      money: string;
      points: number;
      target: string;
      user_id: number;
      user_name: string;
    };
    game_id: number;
    game_type: string;
    liveId: string;
    target: string;
  };
  event_type: number;
  uuid: string;
};

export type WinningWsMessage = {
  client_id: string;
  data: {
    anchor_id: string;
    code: number;
    data: {
      game_name: string;
      money: string;
      target: string;
      user_id: number;
      user_name: string;
    };
    game_id: number;
    game_type: string;
    liveId: string;
    msg: string;
    target: string;
  };
  event_type: number;
  uuid: string;
};

export type WsMessage = UserWsMessage | BettingWsMessage | WinningWsMessage;

export type RecentMessage = {
  type: number;
  content: string;
  avatar: string;
  isRoomManager: number;
  level: number;
  levelIcon: string;
  send_time: string;
  nickname: string;
  userId: number;
};

/**
 * Comment Submit Request Interface
 */
export interface CommentSubmitRequest {
  /** 游戏ID */
  gameId: number;
  /** 评论内容 */
  content: string;
  /** 评论类型 (用户评论固定为 0) */
  type: number;
  /** 回复的评论ID (可选) */
  replyToId?: string;
}
