import { isEmpty, isString, isObject } from '@/utils';
import { PageEnum } from '@/enums/pageEnum';

const defaultValue = () => ({
  sysVersion: '',
  deviceName: '',
  inviteCode: '',
  subchannelId: PageEnum.SUB_CHANNEL
});

const setValue = (app, payload = defaultValue()) => {
  // app.config.globalProperties.__APP_DATA__ = payload;
  window.__static_public_data__ = payload;
};

function setAlthoughFarAwayBePunished(app, value) {
  if (isEmpty(value)) {
    setValue();
  } else if (isString(value)) {
    try {
      const val = JSON.parse(value);
      setValue(app, val);
    } catch (e) {
      setValue(app);
    }
  } else if (isObject(value)) {
    setValue(app, value);
  }
}

const viteChannelPlugin = {
  install(app, options = {}) {
    if (window.dsBridge?.hasNativeMethod('app.getParam')) {
      const synValue = window.dsBridge.call('app.getParam');
      setAlthoughFarAwayBePunished(app, synValue);
    } else {
      setValue(app);
    }
  }
};

export default viteChannelPlugin;
