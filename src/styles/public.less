::-webkit-scrollbar {
  display: none;
  background: transparent;
}

html,body {
  -webkit-font-smoothing: antialiased;
  -webkit-text-size-adjust: 100%;
  -webkit-user-select: none;
  user-select: none;
  -webkit-overflow-scrolling: touch;
  -webkit-user-select: none;
  touch-action: pan-x pan-y;
  overscroll-behavior: none;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  color: white;
  background: var(--van-black);
}

body ::-webkit-scrollbar {
  display: none;
}

#app {
  height: 100%;
  min-height: 100vh;
  font-family: Avenir,Helvetica,Arial,sans-serif;
}

.van-config-provider {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.scrollbar-none {
  scrollbar-width: none; /* 针对 Firefox 浏览器隐藏滚动条 */
}

.scrollable-none::-webkit-scrollbar {
  display: none; /* 完全隐藏滚动条 */
}

.overflow-scrolling {
  --webkit-overflow-scrolling: touch;
}

.slide-fadein-left-enter-active,
.slide-fadein-left-leave-active,
.slide-fadein-right-enter-active,
.slide-fadein-right-leave-active {
  transition:
    opacity 0.3s,
    transform 0.4s,
    -webkit-transform 0.4s;
  position: absolute;
  left: 0;
  width: 100%;
}

.slide-fadein-left-enter-from,
.slide-fadein-right-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

.slide-fadein-left-leave-to,
.slide-fadein-right-enter-from {
  transform: translateX(-20px);
  opacity: 0;
}

.absolute-layout-enter-active,
.absolute-layout-leave-active {
  position: absolute;
  left: 0;
  width: 100%;
}

.van-fixed-bottom {
  padding-bottom: var(--van-tabbar-height);
  padding-bottom: var(--van-tabbar-height);
}

.play-gradient-primary {
  background: linear-gradient(0deg, #f7e7cd 0%, #fefefc 100%);;
}

.play-gradient-black {
  background: linear-gradient(0deg, #303030 0%, #555352 100%), linear-gradient(#18c8ad, #18c8ad);
}

.van-image-rounded {
  .van-image__img {
    border-radius: 4px;
  }
}

.van-image-rounded-top {
  .van-image__img {
    border-radius: 4px 4px 0 0;
  }
}

// .van-tab--grow {
//   padding: 0 8px;
// }

.van-pull-refresh__track {
  flex-grow: 1;
}

.van-field__label--top {
  margin-bottom: 12px;
}

.van-button--info {
  color: white;;
  background-color: var(--van-info-color);
  border: var(--van-button-border-width) solid var(--van-button-info-border-color)
}

.van-swipe {
  height: 100%;
}

.van-nav-bar__title {
  font-weight: inherit;
}

.van-cell::after {
  position: absolute;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  content: "";
  pointer-events: none;
  right: var(--van-padding-md);
  bottom: 0;
  left: var(--van-padding-md);
  border-bottom: 1px solid var(--van-cell-border-color);
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5);
}

.van-tab--active {
  font-weight: inherit;
}

.vjs-error-display {
  display: none;
}

.van-password-input__security li {
  width: var(--van-password-input-height);
  flex: none;
  border: 1px solid #dadde0;
  color: #333333;
  border-radius: 5px;
}

.van-number-keyboard__body {
  color: #333333;
}

.bg-gradient-orange {
  background-image: var(--van-gradient-orange);
}

.bg-gradient-primary {
  background-image: linear-gradient(to bottom, var(--van-primary-color), #ffffff 45%);
}

.bg-gradient-orange-1 {
  background-image: linear-gradient(90deg, #ffdb49 0%, rgba(255, 229, 125, 0.45) 55%, #ffefb0 100%);
}

.tp-line::before {
  content: '';
  width: 4px;
  height: 17px;
  background-color: var(--van-primary-color);
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 7.5px;
}

.half-diagonal {
  position: absolute;
  top: 0;
  right: 0;
  width: 36px;
  height: 36px;
  background-color: #007bff;
  border-bottom-left-radius: 12px;
  border-top-right-radius: 12px;
  clip-path: polygon(0% 0%, 100% 0, 100% 100%);
}

.half-diagonal__text {
  position: absolute;
  top: 43%;
  left: 76%;
  transform: translate(-50%, -50%) rotate(45deg);
  color: white;
  width: 28px;
}

.vjs-poster {
  background-size: 100% auto;
}

.aspect-h-9 {
  --tw-aspect-h: 9;
}

.aspect-w-16 {
  position: relative;
  padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
  --tw-aspect-w: 16;
}

/* 修改进度条颜色 */
#nprogress .bar {
  background: var(--van-primary-color) !important;
}

.van-theme-dark {
  --van-number-keyboard-background: #edf1f5;
  --van-number-keyboard-key-background: white;
  --van-number-keyboard-key-active-color: var(--van-gray-6);
};


.van-popover--light .van-popover__action:active {
  background-color: white!important;
}