import { createHtmlPlugin } from 'vite-plugin-html';
import { GLOB_CONFIG_FILE_NAME } from '../constant';
import pkg from '../../package.json';

export function configHtmlPlugin(env, isBuild) {
  const { VITE_APP_TITLE, VITE_PUBLIC_PATH } = env;

  const getAppConfigSrc = () => {
    return `${VITE_PUBLIC_PATH}${GLOB_CONFIG_FILE_NAME}?v=${pkg.version}`;
  };

  const htmlPlugin = createHtmlPlugin({
    minify: isBuild,
    inject: {
      // Inject data into ejs template
      data: {
        title: VITE_APP_TITLE
      },
      tags: isBuild
        ? [
            {
              tag: 'script',
              attrs: {
                src: getAppConfigSrc()
              }
            }
          ]
        : []
    }
  });
  return htmlPlugin;
}
