import { VitePWA } from 'vite-plugin-pwa';

export function virtualMessagePlugin() {
  const virtual = 'virtual:message'
  const resolvedVirtual = `\0${virtual}`
  return {
    name: 'vite-plugin-test',
    resolveId(id) {
      return id === virtual ? resolvedVirtual : null
    },
    load(id) {
      if (id === resolvedVirtual)
        return `export const message = 'Message from Virtual Module Plugin'`
    },
  }
}

export function configPwaConfig(env) {
  const pwaOptions = {
    mode: 'production',
    registerType: 'autoUpdate',
    includeAssets: ['favicon.svg', 'apple-touch-icon.png'],
    display: "standalone",
    manifest: {
      name: env.VITE_APP_TITLE,
      short_name: env.VITE_APP_SHORT_TITLE,
      display: "standalone",
      theme_color: '#000000',
      background_color: "#171717",
      icons: [
        {
          src: 'pwa-192x192.png', // <== don't add slash, for testing
          sizes: '192x192',
          type: 'image/png',
        },
        {
          src: '/pwa-512x512.png', // <== don't remove slash, for testing
          sizes: '512x512',
          type: 'image/png',
        },
      ],
    },
    workbox:  {
      clientsClaim: true,
      skipWaiting: true,
      cleanupOutdatedCaches: true,
      runtimeCaching: [
        // {
        //   urlPattern: ({ request }) => request.destination === 'document',
        //   handler: 'NetworkFirst', // 访问 HTML 页面时，优先从网络获取
        //   options: {
        //     cacheName: 'html-cache',
        //     expiration: { maxAgeSeconds: 60 * 5 } // 5 分钟后过期
        //   }
        // },
        {
          urlPattern: ({ request }) =>
            ['script', 'style', 'worker'].includes(request.destination),
          handler: 'StaleWhileRevalidate', // JS/CSS 资源，使用缓存但同时后台更新
          options: {
            cacheName: 'static-resources',
            expiration: { maxEntries: 50, maxAgeSeconds: 24 * 60 * 60 } // 1 天
          }
        },
        {
          urlPattern: ({ request }) => request.destination === 'image',
          handler: 'CacheFirst', // 图片使用缓存优先
          options: {
            cacheName: 'image-cache',
            expiration: { maxEntries: 50, maxAgeSeconds: 7 * 24 * 60 * 60 } // 7 天
          }
        }
      ]
    },
    devOptions: {
      enabled: env.VITE_SW_DEV === true,
      /* when using generateSW the PWA plugin will switch to classic */
      type: 'module',
      navigateFallback: 'index.html',
      suppressWarnings: true,
    },
  }

  return VitePWA(pwaOptions);
}
