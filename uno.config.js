import {
  defineConfig,
  presetAttributify,
  presetTypography,
  presetMini,
  presetWind3,
  transformerVariantGroup,
} from 'unocss';

import presetRemToPx from '@unocss/preset-rem-to-px'

// 刚使用unocss的朋友，可以借助这个工具： https://to-unocss.netlify.app

export default defineConfig({
  presets: [
    presetWind3(),
    presetAttributify(),
    presetTypography(),
    presetMini(),
    presetRemToPx()
  ],
  transformers: [
    transformerVariantGroup(),
  ],
  shortcuts: [
    // shortcuts to multiple utilities
    ['menu-category', 'w-[57px] h-[65px] flex flex-col items-center justify-center relative box-border border border-transparent border-solid not-last:border-b-slate-700'],
    ['menu-category-active', 'w-[57px] h-[65px] flex flex-col items-center justify-center relative box-border border border-white border-solid bg-[var(--van-primary-color)] text-[var(--van-gray-8)]'],
    ['action-item-row', 'flex items-center justify-around h-50px bg-white rounded-lg relative font-medium'],
    ['action-item-col', 'flex flex-col items-center justify-center h-50px bg-white rounded-lg relative font-medium'],
    ['u-tab-item', 'h-50px rounded-lg flex items-center justify-center relative shadow text-sm relative bg-white font-medium text-[var(--van-gray-7)]'],
    ['u-tab-item-active', 'bg-violet-500 text-white'],
  ],
})