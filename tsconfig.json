{
  "compilerOptions": {
    "target": "esnext",
    "jsx": "preserve",
    "lib": ["esnext", "dom", "dom.iterable", "scripthost"],
    "experimentalDecorators": true,
    "baseUrl": ".",
    "module": "esnext",
    "moduleResolution": "Bundler",
    "paths": {
      "@/*": ["src/*"]
    },
    "types": ["node", "unplugin-vue-router/client", "vite-plugin-pwa/client"],
    "allowJs": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "importHelpers": true,
    "sourceMap": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "verbatimModuleSyntax": true,
    "skipLibCheck": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.ts",
    "tests/**/*.tsx",
    "tests/*.ts",
    "types/*.d.ts",
  ]
}
